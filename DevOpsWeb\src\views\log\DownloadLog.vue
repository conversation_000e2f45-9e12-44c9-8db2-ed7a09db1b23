<template>
  <div class="down-main-log">
    <div class="search-wrapper">
      <div class="search-left-wrapper">
        <div class="search-list-item">
          <span class="search-label">{{ $t('serviceList') }}</span>
          <tvt-select
            v-model="serviceNames"
            class="search-value"
            :placeholder="$t('microservicesPhd')"
            clearable
            multiple
            collapse-tags
            @change="handleChange"
          >
            <el-option
              v-for="(item, index) in microservicesList"
              :key="index"
              :label="item"
              :value="item"
            >
              <span style="float: left">{{ item }}</span>
            </el-option>
          </tvt-select>
        </div>
        <div class="search-list-item">
          <span class="search-label">{{ $t('logCreateTimeLab') }}</span>
          <el-date-picker
            v-model="date"
            class="search-value"
            type="date"
            :placeholder="$t('selectTime')"
            @change="handleCollectLog(1)"
          />
        </div>
      </div>
    </div>
    <div class="table-wrap">
      <tvt-table
        v-myLoading="loading"
        :data="logList"
        :border="true"
        :border-bottom="true"
        resizable
        :columns="columns"
        height="886px"
        @onFetchData="handleCollectLog(1)"
      >
        <!-- 操作按钮 -->
        <template #operation="{ row }">
          <el-button
            v-if="row.status === 1"
            type="text"
            @click="handelDownload(row)"
          >
            {{ $translate('download') }}
          </el-button>
          <span v-if="row.status === 1" class="btn-line">|</span>
          <el-button type="text" @click="deleteOne(row)">
            {{ $translate('delete') }}
          </el-button>
        </template>
      </tvt-table>
    </div>
    <tip-dialog
      ref="tipDialogRef"
      title="info"
      :tip-msg="$t('configTab.delete')"
      @submit="handleDelete"
    />
  </div>
</template>

<script>
import TipDialog from '@/components/common/TipDialog.vue'
import {
  collectLog,
  getCollectLog,
  removeCollectLog,
  getProjectNameList,
} from '@/api/log.js'
import { formatDate } from '@/utils/dateFormat.js'
import { STATUS_OBJ } from '@/utils/constant.js'
import { mapState } from 'vuex'
import { debounce } from '@/utils/common.js'
export default {
  name: 'DownLoadLog',
  components: {
    TipDialog,
  },
  data() {
    return {
      STATUS_OBJ: STATUS_OBJ(),
      serviceNames: [],
      date: null,
      microservicesList: [],
      loading: false,
      total: 0,
      logList: [],
      deleteId: '',
      docUrl: '',
      columns: [
        { label: 'ID', prop: 'id' },
        {
          label: this.$translate('serviceName'),
          prop: 'serviceName',
          minWidth: '200px',
          'show-overflow-tooltip': true,
        },
        {
          label: this.$translate('logCreateTime'),
          prop: 'logCreateTime',
          width: '200px',
        },
        {
          label: this.$translate('status'),
          prop: 'collectStatus',
          width: '200px',
        },
        {
          label: this.$translate('logCollectTime'),
          prop: 'logsCollectTime',
          width: '200px',
        },
        {
          label: this.$translate('operation'),
          slotName: 'operation',
          width: '200px',
          hide: !this.$isAuthorityExist(),
        },
      ],
    }
  },
  computed: {
    ...mapState('params', ['environmentCode', 'dcName']),
  },
  created() {
    const date = new Date()
    const nowYear = date.getFullYear()
    const nowMonth = date.getMonth()
    const nowdate = date.getDate()
    // 设置日期筛选框默认时间为今天
    this.date = formatDate(new Date(nowYear, nowMonth, nowdate))
  },
  mounted() {
    this.getData()
    this.getLogData()
  },
  methods: {
    // 选择组件变化
    handleChange: debounce(function () {
      this.handleCollectLog()
    }, 500),
    handleCollectLog() {
      if (this.serviceNames.length < 1) {
        this.$message.error(this.$t('notEmptySerName'))
        return
      } else if (this.serviceNames.length > 5) {
        this.$message.error(this.$t('maxSupportSerName'))
        return
      }
      let param = {
        serviceNames: this.serviceNames,
        date: new Date(this.date).getTime(),
      }
      let { environmentCode, dcName } = this
      param.environmentCode = environmentCode
      param.dcName = dcName || undefined
      this.loading = true
      collectLog(param)
        .then(() => {
          this.getLogData()
        })
        .catch((err) => {
          this.loading = false
          console.log('err', err)
          this.$message.error(err.msg)
        })
    },
    // 获取微服务列表
    getData() {
      let { environmentCode, dcName } = this
      let data = {
        environmentCode,
        dcName,
      }
      getProjectNameList(data)
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            if (res.data) {
              this.microservicesList = res.data
              this.serviceNames = [this.microservicesList[0]] //默认第一个
            } else {
              this.microservicesList = []
            }
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 获取日志列表数据
    getLogData() {
      getCollectLog()
        .then(({ data }) => {
          if (data) {
            this.logList = (data || []).map((v) => {
              return {
                ...v,
                serviceName: v.msList !== 'null' ? v.msList : '',
                collectStatus: this.STATUS_OBJ[v.status],
                logCreateTime:
                  v.logsTime && formatDate(new Date(+v.logsTime), 'yyyy-mm-dd'),
                logsCollectTime:
                  v.logsCollectTime && formatDate(new Date(+v.logsCollectTime)),
              }
            })
          }
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 下载
    handelDownload(row) {
      window.location.href = `${window.location.protocol}//${window.location.host}/dev-ops/log-collect/download?id=${row.id}`
    },
    deleteOne(row) {
      this.deleteId = row.id
      this.$refs.tipDialogRef.open()
    },
    // 删除
    handleDelete() {
      removeCollectLog([this.deleteId])
        .then(() => {
          this.$message.success(this.$t('deleteSuccess'))
          this.getLogData()
        })
        .catch((err) => {
          console.log(err)
          this.$message.error(err.msg)
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.down-main-log {
  width: calc(100% - 48px);
  padding: 20px;
  .search-wrap {
    .search-label {
      margin-right: 10px;
      color: #606266;
      font-size: 14px;
    }
    .search-value {
      width: 300px;
      margin-right: 20px;
    }
  }
  .search-wrapper {
    width: 100%;
    margin: 20px 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    .search-left-wrapper {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
    }
    .search-right-wrapper {
      width: max-content;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }

    .search-list-item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-right: 20px;
    }

    .search-label {
      margin-right: 10px;
      color: #606266;
      font-size: 14px;
    }
    .search-value {
      width: 250px;
      margin-right: 20px;
    }
  }
  .table-wrap {
    .btn-line {
      display: inline-block;
      margin: 0 4px;
      color: #409eff;
    }
  }
}
</style>

<style>
.down-main-log .el-input__inner {
  height: 32px;
  line-height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 16px;
}

.down-main-log .search-wrapper .tvt-input .tvt-field-set {
  border: none;
}
.down-main-log .search-wrapper .tvt-select .tvt-field-set {
  border: none;
}
.down-main-log {
  ::v-deep .el-table .el-table__cell {
    padding: 8px 0px;
  }
}
</style>
