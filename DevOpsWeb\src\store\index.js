import Vue from 'vue'
import Vuex from 'vuex'
import params from './params'
import monitor from './monitor'
import selfLocaLStorage from '@/utils/selfLocalStorage'

Vue.use(Vuex)

const state = {
  // 用户id-名称映射表
  userMap: {},
  nickName: selfLocaLStorage.getItem('nickName'),
  userId: selfLocaLStorage.getItem('userId'),
  token: selfLocaLStorage.getItem('opsToken'),
  sid: selfLocaLStorage.getItem('sid'),
  authResList: selfLocaLStorage.getItem('authResList') || [],
}

const mutations = {
  // 保存登录用户信息到存储
  SAVE_USERINFO(state, userInfo) {
    state.authResList = userInfo.authResList
    state.nickName = userInfo.nickName || ''
    state.mobile = userInfo.mobile
    state.userId = userInfo.userId
    state.token = userInfo.token
    state.sid = userInfo.sid

    // 使用封装的storage方法
    userInfo.authResList &&
      selfLocaLStorage.setItem('authResList', userInfo.authResList)
    userInfo.areaList && selfLocaLStorage.setItem('areaList', userInfo.areaList)
    userInfo.dcResCodeObj &&
      selfLocaLStorage.setItem('dcResCodeObj', userInfo.dcResCodeObj)
    selfLocaLStorage.setItem('nickName', state.nickName)
    selfLocaLStorage.setItem('userName', userInfo.userName)
    selfLocaLStorage.setItem('mobile', userInfo.mobile)
    selfLocaLStorage.setItem('userId', userInfo.userId)
    selfLocaLStorage.setItem('opsToken', userInfo.token)
    selfLocaLStorage.setItem('sid', userInfo.sid)
    selfLocaLStorage.setItem('environmentCode', userInfo.environmentCode)
  },
}

const store = new Vuex.Store({
  state,
  mutations,
  modules: {
    params,
    monitor,
  },
})

export default store
