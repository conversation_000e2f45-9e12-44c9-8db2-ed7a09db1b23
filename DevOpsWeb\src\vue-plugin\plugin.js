import { formatDate } from '@/utils/dateFormat'
import selfLocaLStorage from '@/utils/selfLocalStorage'

/**
 * 权限校验全局方法
 * @param {string}
 * @return {Boolean}
 * @example 校验是否有用户查看权限：this.$isAuthorityExist('user_qry')
 */
export const checkAuthority = {
  install(Vue) {
    Vue.prototype.$isAuthorityExist = function (authorityCode) {
      if (!selfLocaLStorage.getItem('authResList')) return false
      let authResList = selfLocaLStorage.getItem('authResList') || []
      let authResSet = new Set()
      if (Array.isArray(authResList)) {
        authResSet = new Set(authResList)
      } else {
        authResSet = new Set(authResList.split(','))
      }
      if (!authorityCode) return true
      if (Array.isArray(authorityCode)) {
        // 只要authorityCode中有一个权限在authResList则返回true
        return authorityCode.some((code) => authResSet.has(code))
      } else {
        // return authResList.indexOf(authorityCode) >= 0
        return authorityCode.split(',').some((code) => authResSet.has(code))
      }
    }
  },
}

/**
 * 时间戳转格式化的时间字符串
 * @param {Number} timeStamp: 时间戳
 * @return {String} 格式化后的时间字符串
 * @example this.$formatTimeStamp(1565681428358) // '2019-08-13 15:30:28'
 */
export const formatTimeStamp = {
  install(Vue) {
    Vue.prototype.$formatTimeStamp = (
      timeStamp,
      format = 'yyyy-mm-dd hh:mm:ss'
    ) => {
      if (!timeStamp) return ''
      let dateObj = new Date(timeStamp * 1)
      return formatDate(dateObj, format)
    }
  },
}
