export default {
  enterImgCode: '请输入图形验证码',
  emptyImgCode: '图形验证码不能为空！',
  errorImgcode: '图形验证码错误！',
  hello: '你好！',
  cancel: '取消',
  confirm: '确定',
  info: '提示',
  edit: '编辑',
  editSuccess: '编辑成功!',
  on: '开启',
  off: '关闭',
  filter: '过滤',
  delete: '删除',
  deleteSuccess: '删除成功！',
  addContinue: '添加并继续',
  index: '序号',
  operation: '操作',
  search: '搜索',
  searchText: '查询',
  add: '新增',
  input: '请输入',
  choose: '请选择',
  refresh: '刷新',
  import: '导入',
  export: '导出',
  exportFail: '导出失败！',
  ipUncorrect: 'ip不合法',
  downloadTemplate: '下载模板',
  importSuccess: '导入成功！',
  importFail: '导入失败！',
  chooseImportExcel: '请选择要导入的文件！',
  uploadSuccess: '上传成功',
  detail: '详情',
  close: '关闭',
  noData: '暂无数据',
  LoadingMsg: '正在加载中...',
  LoadingUpload: '正在上传中...',
  LoadingDownload: '正在下载中...',
  packageTagLoading: '部署包',
  haveChoose: '已添加数据',
  addThen: '请先添加数据后再确定！',
  chooseEnvAndDcName: '请选择环境和DC名称',
  modifyPass: '修改密码',
  logout: '退出登录',
  download: '下载',
  noEmpty: '{name}不能为空',
  all: '全部',
  projectNameInput: '请输入微服务名称',
  pleaseEnter: '请输入{type}',
  pleaseSelect: '请选择{type}',
  pleaseCheckOne: '请至少勾选一个',
  columnSetting: '列设置',
  activityTypeTRequire: '字段名称不能为空',
  compareTypeRequire: '比较方式不能为空',
  inputValRequire: '输入不能为空',
  microservicesPhd: '请选择微服务',
  serviceName: '采集微服务名称',
  logCreateTime: '日志生成时间',
  status: '采集任务状态',
  logCollectTime: '日志收集时间',
  logCloudUrl: '日志云存储URL地址',
  selectTime: '请选择日期',
  serviceList: '微服务列表：',
  logCreateTimeLab: '日志产生时间：',
  collecting: '采集中',
  collectSucc: '采集成功',
  collectFail: '采集失败',
  notEmptySerName: '请先选择微服务后再进行操作！',
  maxSupportSerName: '最多支持采集5个微服务,请重新选择！',
  quotaSelect: '请选择指标',
  instanceSelect: '请选择实例',
  areaSelect: '请选择区域',
  startDate: '开始日期',
  endDate: '结束日期',
  today: '今天',
  yesterday: '昨天',
  weekAgo: '一周前',
  lastHour: '近1小时',
  last3Hour: '近3小时',
  last6Hour: '近6小时',
  last12Hour: '近12小时',
  last24Hours: '过去24小时',
  last7d: '近7天',
  last30d: '近30天',
  last3m: '近3个月',
  last6m: '近6个月',
  last1y: '近1年',
  last2y: '近2年',
  timePlaceholder: '请选择时间',
  fullScreen: '全屏',
  fullScreenView: '全屏查看',
  save: '保存',
  restoration: '复原',
  zoom: '区域缩放',
  allClose: '全部关闭',
  allOpen: '全部开启',
  open: '开启',
  alarmStatus: '状态',
  alarmEmailStatus: '邮件告警状态',
  alarmSmsStatus: '短信告警状态',
  allEmailClose: '全部关闭邮件告警',
  allEmailOpen: '全部开启邮件告警',
  allSmsClose: '全部关闭短信告警',
  allSmsOpen: '全部开启短信告警',
  hostMonitor: '主机监控',
  cpuRate: 'CPU利用率',
  loadOne: '1分钟平均负载',
  loadFive: '5分钟平均负载',
  loadFifteen: '15分钟平均负载',
  hostAvailMemory: '可用内存',
  hostMemoryRate: '内存使用率',
  hostAvailDisk: '剩余空间',
  hostDiskRate: '空间使用率',
  hostRecvmb: '入流量',
  hostSentmb: '出流量',
  hostIoRead: 'IO读',
  hostIoWrite: 'IO写',
  hostBandwidthRate: '带宽利用率',
  microService: '微服务',
  javaMicroService: 'Java微服务',
  cplusMicroService: 'C++微服务',
  webMicroService: 'Web微服务',
  hostTraffic: '主机流量',
  microMemoryRate: '微服务内存使用率',
  microcpuRate: 'CPU使用率',
  mysqlDbSlows: 'mysql慢查询次数',
  redisMemory: 'Redis已使用内存',
  redisMemoryRate: 'Redis内存利用率',
  kafkaMemoryRate: 'Kafka内存利用率',
  kafkaCpuRate: 'KafkaCPU利用率',
  mongoSpace: 'Mongo数据库占用空间',
  mongoConnections: 'Mongo已使用连接数',
  mongoAvailConnections: 'Mongo剩余可用连接数',
  mongoMemory: 'Mongo已使用内存',
  mongoSlows: 'Mongo慢查询次数',
  nginxConnections: 'Nginx已使用连接数',
  nginxAvailConnections: 'Nginx剩余可用连接数',
  nginxIncrRequests: 'Nginx请求数',
  storages: '桶已使用存储容量',
  httpsDomain: 'Https域名',
  domainRemainDays: '域名有效剩余时间',
  certificate: '证书',
  certRemainDays: '证书有效剩余时间',
  dpikeyErrors: 'dpikey异常日志次数',
  email: '邮件',
  emailErrors: '邮件发送失败量',
  emailLimits: '邮件发送超出频率次数',
  emailNums: '邮件发送成功量',
  emailTps: '邮件发送的TPS的峰值',
  textMessage: '短信',
  smsErrors: '每分钟短信发送失败存在异常次数',
  smsLimits: '每分钟短信发送超出频率次数',
  smsNums: '每分钟短信发送总次数',
  smsTps: '每分钟发送短信的TPS的峰值',
  account: '账号',
  push2: '推送2.0',
  kafkaNode: 'kafka节点',
  kafkaCluster: 'kafka集群',
  pushOfflineErrors: '每分钟离线推送失败次数',
  pushOfflineLimits: '每分钟离线推送超出频率次数',
  pushOfflinePushs: '每分钟离线推送总次数',
  pushOfflineTps: '每分钟离线推送的TPS的峰值',
  pushOnlineErrors: '每分钟在线推送失败次数',
  pushOnlinePushs: '每分钟在线推送总次数',
  pushOnlineTps: '每分钟在线推送的TPS的峰值',
  pushPushs: '每分钟推送总数',
  pushTps: '每分钟推送的TPS的峰值',
  cpuMemoryHost: 'CPU/内存/主机流量',
  processMemory: '进程使用物理内存',
  processCpuRate: '单进程CPU使用率',
  processMemoryRate: '单进程内存使用率',
  usedMemory: '已使用内存',
  memory: '内存',
  disk: '磁盘',
  process: '进程',
  domain: '域名',
  cert: '证书',
  remainDays: '有效剩余时间',
  videoFlow: '视频服务流量监控',
  mediaTrafficIn: '每秒入流量',
  mediaTrafficOut: '每秒出流量',
  day: '天',
  days: '天',
  hour: '小时',
  hours: '小时',
  minute: '分',
  minutes: '分',
  second: '秒',
  seconds: '秒',
  mysqlInstance: 'Mysql实例',
  mysqlDb: 'Mysql数据库',
  fileSizeLimits: '文件大小不能超过{size}{unit}！',
  noPermissionPage: '无权限访问任何系统页面！',
  nav: {
    home: '首页',
    installDeploy: '安装部署',
    // resourceConfiguration:'部署环境资源规划',
    resourceConfiguration: '资源规划',
    relationMaintenance: '部署包依赖关系维护',
    // versionMatch:'部署包版本配套',
    versionMatch: '微服务版本配套',
    configurationParams: '开源件配置参数',
    // deploymentPlan:'部署规划',
    deploymentPlan: '安装与部署',
    // serviceStatus:'服务状态(启停卸载)',
    serviceStatus: '基础特性自动化测试',
    monitor: '监控',
    resourceMonitor: '基础资源监控',
    applicationMonitor: '应用监控',
    businessMonitor: '业务监控',
    envTesting: '环境检测',
    domainConfiguration: '域名配置',
    // log:'日志',
    log: '下载中心',
    logFile: '主机日志下载',
    // downloadLog:'日志下载',
    downloadLog: '日志下载任务',
    envHealthCheck: '环境健康检查',
    systemMonitor: '系统监控',
    resLayerMonitor: '资源层监控',
    appLayerMonitor: '应用层监控',
    businessLayerMonitor: '业务层监控',
    domainCert: '域名/证书状态',
    dpikey: 'dpikey',
    email: '邮件',
    textMessage: '短信',
    push: '推送2.0',
    account: '账号',
    p2p: 'P2P2.0',
    p2p1: 'P2P1.0',
    systemAlarm: '系统告警',
    alarmConfig: '告警配置',
    alarmHistory: '告警历史数据',
    alarmData: '告警实时数据',
    systemSLA: '系统SLA',
    systemAnalysis: '数据分析',
    businessData: '业务数据统计',
    costData: '成本数据统计',
    serviceQuality: '服务质量统计',
    devBasicTool: '运维基础工具',
    microServiceLog: '微服务运行日志',
    restfulLog: 'Restful接口日志',
    appLog: 'APP Log日志',
    kafkaSearch: 'Kafka查询',
    ossSearch: 'OSS/S3查询',
    redisSearch: 'Redis查询',
    mysqlSearch: 'Mysql查询',
    deviceSearch: '设备信息查询',
    configureParam: '配置参数管理',
    deviceLog: '设备日志',
    operateLog: '操作日志',
    scheduleTaskLog: '定时任务日志',
    devopsStatistics: '运维统计',
    characteVisitVolume: '特性访问量',
    lowFrequencyAccess: '特性低频访问',
    featureAlarmStatics: '特性告警统计',
  },
  login: {
    userNamePHD: '请输入用户名',
    passwordPHD: '请输入密码',
    login: '登录',
    userNameRequired: '用户名不能为空',
    passwordRequired: '密码不能为空',
    confirmLogout: '确认退出？',
    modifyPass: '修改密码',
    oldPassword: '原密码',
    newPassword: '新密码',
    confirmPassword: '确认密码',
    noOldPwd: '原密码不能为空',
    noNewpwd: '新密码不能为空',
    noConfirmPwd: '确认密码不能为空',
    pwdFormUncorrect: '密码格式不正确',
    pwdDifferent: '两次输入密码不一致',
  },
  configTab: {
    envRes: '部署环境资源',
    environmentName: '环境名称',
    projectName: '微服务名称',
    resourceIp: 'IP资源',
    projectPort: '端口',
    startXms: '最小启动堆内存(M)',
    startXmx: '最大启动堆内存(M)',
    resourceMemory: '主机内存',
    resourceCpu: '主机CPU',
    dcName: 'DC名称',
    operation: '操作',
    info: '提示',
    delete: '确认删除该条数据吗？',
    deleteEnv: '确认删除该环境吗？',
    environmentNameRequired: '环境名称不能为空',
    projectNameRequired: '微服务名称不能为空',
    projectPortRequired: '端口不能为空',
    ipResourceRequired: 'IP资源不能为空',
    memoryRequired: '内存不能为空',
    CPURequired: 'CPU不能为空',
    DCnameRequired: 'DC名称不能为空',
    ipUncorrect: 'ip不合法',
    addEnv: '新增环境',
    addResource: '新增资源',
    viewPortMatrix: '查看端口矩阵',
    portNumber: '端口号',
    portScope: '规划端口',
    projectType: '微服务类型',
    dcType: 'DC类型',
    dcNamePhd: '请选择DC名称',
    hostName: '主机名',
    instanceId: '实例ID',
    orderId: '顺序',
    startMinMax: '输入范围0~4096',
    startLimit: '最小启用堆内存要小于最大启用堆内存',
    type: '分类',
    startXmxParam: '启用最大内存参数',
    clusterName: '集群名称',
    cpu: 'CPU',
    memory: '内存',
    disk: '磁盘',
    cpuCore: '核数',
  },
  maintenceTab: {
    projectName: '微服务',
    artifactId: 'artifactId',
    packageName: '包名称',
    architectureType: '元素类型',
    needDeployProjectNames: '依赖部署',
    dcType: 'DC类型',
    buildOrder: '构建顺序',
    buildDependency: '部署依赖',
    buildParamNames: '构建参数',
    info: '提示',
    delete: '确认删除该条数据吗？',
    nameRequired: '微服务名称不能为空',
    artifactIdRequired: 'artifactId不能为空',
    packageNameRequired: '包名称不能为空',
    packageNameError: '包名称必须包含$(version)，以.jar或.zip结尾',
    elementTypeRequired: '元素类型不能为空',
    dependencyDeploymentRequired: '依赖部署不能为空',
    dcTypeRequired: 'DC类型不能为空',
    nameInput: '请输入微服务名称',
    packageNameInput: '请输入DC类型',
  },
  versionTab: {
    downloadPackageTag: '下载部署包',
    downloadPackageTagSuccess: '下载部署包成功!',
    downloadPackageTagTip: '根据选择的部署包标签,下载对应的部署包',
    deleteTag: '确认删除该条部署包标签吗？',
    deletePackageTagSuccess: '部署包标签删除成功',
    packageTagInput: '请选择配套表名称',
    packageNameInput: '请输入微服务名称',
    manualUploadPKG: '手动上传包',
    getLatestPKG: '获取最新包',
    applyLatestPKG: '应用最新包',
    archivePKG: '创建版本配套表',
    packageTag: '配套表名称',
    serviceType: '服务类型',
    businessService: '业务微服务',
    cppService: 'c++服务',
    p2pwebService: 'p2pweb',
    projectName: '微服务名称',
    artifactId: 'artifactId',
    packageName: '包文件名称',
    architectureType: '元素类型',
    version: '版本号',
    archiveTime: '归档时间',
    uploadPkgUncorrect: '手动上传包格式不正确',
    environmentNameSelect: '请选择环境',
    packageTagRequired: '配套表名称不能为空',
    serviceTypeRequired: '服务类型不能为空',
    needChooseList: '请至少选择一条数据',
    projectNameInput: '请输入微服务名',
    latestApplySuccess: '更新部署包版本成功',
    archiveSuccess: '归档成功',
    uploadLimit: '手动上传包只能上传zip/jar文件,且不超过150M',
    needChooseversion: '所选数据的版本号不能为空 请选择后再操作',
    versionSave: '本地版本配套保存',
    versionSaveConfirm: '确认保存本地版本配套？',
    ossPath: 'OSS上的目录路径',
    ossPathSelect: '部署配套选择',
    createTime: '创建时间',
    checkPackageName: '请选择OpenSource包',
    checkNoRepeatName: '同一版本微服务下的包只能选一个',
    checkNoRepeatProject: '同个微服务的包只能选一个',
  },
  deployPlanTab: {
    joinDeployment: '加入部署',
    executeDeployment: '部署业务微服务',
    environmentName: '环境',
    packageTag: '部署包标签',
    id: 'id',
    instanceId: '实例ID',
    projectName: '微服务名称',
    hostIp: 'IP',
    projectPort: '端口',
    startMaxMemory: '启动内存(M)',
    version: '版本号',
    dcName: '部署DC',
    deployStatus: '部署状态',
    sqlUpStatus: 'mysql升级状态',
    deployLog: '部署日志',
    envInput: '请选择环境',
    microserviceName: '请输入微服务名称',
    addDeploy: '新增部署',
    environmentNameRequired: '环境不能为空',
    projectNameRequired: '微服务名称不能为空',
    resourceIpRequired: 'ip不能为空',
    projectPortRequired: '端口不能为空',
    startMaxMemoryRequired: '启动最大内存不能为空',
    versionRequired: '版本号不能为空',
    dcNameRequired: '部署DC不能为空',
    ipUncorrect: 'ip不合法',
    status0: '待部署',
    status1: '部署中',
    status2: '部署成功',
    status3: '部署失败',
    startMaxMemoryEmpty:
      '所选加入部署的数据中存在启动最大内存为空的情况 需要填写',
    versionEmpty: '所选加入部署的数据中存在版本号为空的情况 需要填写',
    existDeploying:
      '所选数据中存在部署中的数据 如仍需执行部署 请去掉部署中的数据 或者等待部署完成后再进行部署',
    deployEndTime: '部署结束时间',
    executeDeploymentDalete: '批量删除',
    joinDeployError: '连通性检测异常',
    sqlUpCost: 'mysql升级耗时(秒)',
    deployCost: '部署耗时(秒)',
    totalCost: '总耗时(秒)',
    tableName: '配套表名称',
    executeDeployError: '执行部署异常',
    executeOpenSource: '部署开源软件',
    sourceName: '开源软件名称',
    executeCPlusService: '部署C++服务',
    executeP2pWeb: '部署P2P Web',
    deployType: '部署类型',
    deployTypeRequired: '部署类型不能为空',
    fileType: '文件类型',
    fileTypeRequired: '文件类型不能为空',
    fileUrl: '文件地址',
    fileUrlRequired: '文件地址不能为空',
    checkFileUrl: '请检查文件地址格式',
    deployService: '部署服务',
    deployPackageName: '部署包名称',
    packageSource: '软件包来源',
    inputFileUrl: '请输入文件地址',
    urlDownload: 'URL下载',
    uploadFile: '手动上传',
    clickUpload: '点击上传',
    archiveVersion: '归档版本包',
    chooseDeployService: '请选择部署服务',
    choosePackageSource: '请选择部署包来源',
    pleaseUploadFile: '请上传文件',
    deployPatch: '部署补丁',
    patchPackageSelect: '请选择补丁包',
    remark: '备注',
    selectDeploymentMode: '请选择部署方式',
    packageTableDeployment: '配套表部署',
    patchDeployment: '补丁部署',
    remarkPlaceholder: '请输入备注',
    cannotOnlySelectOpenSource: '不能只选择openSource的包',
    onlyTagOrService: '配套表和服务类型不能同时选择',
  },
  serviceStatusTab: {
    serviceStatus: '服务状态',
    startService: '启动',
    restartService: '重启',
    reloadService: '重加载',
    stopService: '停止',
    uninstallService: '卸载',
    serviceInstanceId: '实例ID',
    openEureka: '打开Eureka',
    openSpringBootAdmin: '打开SpringBootAdmin',
    openEurekaTip: '找不到Eureka服务地址',
    openSpringBootAdminTip: '找不到springBootAdmin服务地址',
  },
  resourceMonitor: {
    envInput: '请选择环境名称',
    envName: '环境名称',
    ipResource: 'IP资源',
    memory: '内存',
    CPU: 'CPU',
    diskSpace: '磁盘空间',
    DCname: 'DC名称',
    hostCpuUseRate: '主机CPU利用率',
    averageLoad: '平均负载',
    cpuUseRate: 'CPU利用率',
    memoryUseRate: '内存利用率',
    diskRemain: '磁盘剩余',
  },
  applicationMonitor: {
    envInput: '请选择环境名称',
    envName: '环境名称',
    ipResource: 'IP资源',
    name: '名称',
    process: '进程',
    info: '信息',
    abnormalInfo: '异常信息',
  },
  envTesting: {
    env: '环境',
    type: '分类',
    testingItem: '检测项',
    testingRes: '检测结果',
    testingDesc: '检测内容描述',
    typePhd: '请选择分类',
    testingItemPhd: '请选择检测项',
    testingResPhd: '请选择检测结果',
    typePhd1: '请输入分类',
    testingItemPhd1: '请输入检测项',
    testingResPhd1: '请输入检测结果',
    testing: '检测',
    normal: '正常',
    abnormal: '异常',
    paramConfig: '配置参数检测',
    microServiceStatus: '微服务状态',
    dbTableAndField: '字段检测',
    testingTime: '检测时间',
    dcNamePhd: '请选择DC名称',
    envPhd: '请选择环境名称',
    dcNameEmpty: 'DC名称不能为空',
    dataBase: '数据库',
  },
  hostTab: {
    importTemplate: '资源规划检测更新',
    hostInitialization: '主机初始化',
    searchName: '主机名称/ip',
    hostName: '主机名称',
    hostIp: '主机ip',
    hostInit: '主机初始化状态',
    nfsInit: 'nfs安装状态',
    installStatus: '软件安装状态',
    agentVersion: 'agent版本',
    updateTime: '更新时间',
    status0: '待初始化',
    status1: '初始化中',
    status2: '成功',
    status3: '失败',
    statusY: '未完成初始化',
    statusN: '已完成初始化',
    nfsStatus0: '待安装',
    nfsStatus1: '安装中',
    nfsStatus2: '成功',
    nfsStatus3: '失败',
    nfsStatusY: '未完成安装',
    nfsStatusN: '已完成安装',
    importSuccess: '资源规划检测更新成功',
  },
  warnTab: {
    RealTimeAlarm: '实时告警',
    title: '实时告警数据',
    ip: 'IP',
    host: '节点',
    ct: '时间',
    warnInfo: '告警信息',
  },
  trendMonitor: {
    title: '实时监控趋势数据',
    monitorTitle: '趋势数据',
    onlineDevices: 'NAT: 在线设备数',
    deviceRegist: 'NAT: 设备注册指标',
  },
  resourceTab: {
    monitorTableTitle: '指标数据表格',
    ip: 'IP',
    node: '节点',
    host: '节点',
    time: '采集时间',
    ct: '采集时间',
    cpuRate: 'CPU利用率(%)',
    loadOne: '1分钟平均负载',
    loadFive: '5分钟平均负载',
    loadFifteen: '15分钟平均负载',
    availMemory: '可用内存(M)',
    memoryRate: '内存使用率(%)',
    fs: '磁盘的Filesystem',
    disk: 'Filesystem的总空间(M)',
    availDisk: '剩余空间(M)',
    diskRate: '空间使用率(%)',
    recvmb: '入流量(Mbits/s)',
    sentmb: '出流量(Mbits/s)',
    bandwidthRate: '带宽利用率(%)',
    memory: '进程使用物理内存(M)',
    processCpuRate: '单进程CPU使用率(%)',
    processMemoryRate: '单进程内存使用率(%)',
    nfsAvailable: 'NFS可使用',
    pid: '进程id',
    parentPid: '父进程id',
    name: '进程名称',
    user: '用户',
    state: '进程状态',
    threads: '线程数',
    openFiles: '打开文件数',
    sqlId: 'sql描述',
    cost: '执行耗时(ms)',
    st: '开始执行时间',
    errorCode: '错误码',
    errorType: '错误类型',
    errorMsg: '错误信息',
    errorTime: '错误时间',
    businessMsg: '业务信息',
    ioRead: 'IO读(kb/s)',
    ioWrite: 'IO写(kb/s)',
    hi: 'CPU硬件中断时间(%)',
    si: 'CPU软件中断时间(%)',
    steal: 'CPU被抢占时间(%)',
  },
  microServeTab: {
    projectName: '工程名称',
    projectPort: '微服务端口号',
    startXmx: '最大启动堆内存(M)',
    pid: '进程ID',
    instanceId: '实例ID',
    available: '服务可使用',
    cpuRate: 'CPU使用率(%)',
    memory: '已使用内存(M)',
    memoryRate: '内存使用率(%)',
    restarts: '重启次数',
    dmpfiles: '内存溢出文件数',
    type: '类型',
  },
  mySqlTab: {
    available: 'mysql可使用',
    dbName: '数据库名称',
    // dbSpaces: 'mysql占用总空间(M)',
    dbTables: '表个数',
    maxConnections: 'mysql实例最大连接总数',
    dbConnections: '已使用连接数',
    dbAvailConnections: '可用连接数',
    dbDeadlocks: '死锁次数',
    dbSpaces: 'mysql占用空间(M)',
    dbSlows: '慢查询次数',
    spaceRate: '空间使用率(%)',
    availSpace: '可使用空间(M)',
    mysqlType: 'mysql类型',
    totalSpace: '总空间(M)',
  },
  redisTab: {
    available: 'redis可使用',
    memory: '已使用内存(M)',
    memoryRate: '内存利用率(%)',
    connections: '客户端连接数',
    blockedClients: '阻塞连接数',
    rejectedConnections: '拒绝连接数',
    clusterDowns: 'redis集群状态down的节点数',
    redisType: 'redis类型',
  },
  kafkaTab: {
    available: 'kafka可使用',
    memory: '已使用内存(M)',
    memoryRate: '内存利用率(%)',
    cpuRate: 'CPU利用率(%)',
    lags: '堆积消息数',
    clusterDowns: 'kafka集群状态down的节点数',
  },
  mongoTab: {
    available: 'mongo可使用',
    spaces: '占用空间(M)',
    connections: '已使用连接数',
    availConnections: '剩余可用连接数',
    memory: '已使用内存(M)',
    requests: '每分钟请求次数',
    slows: '慢查询次数',
  },
  nginxTab: {
    available: 'nginx可使用',
    connections: '已使用连接数',
    availConnections: '剩余可用连接数',
    totalRequests: 'nginx总请求数',
    incrRequests: 'nginx请求数(次/分)',
  },
  ossTab: {
    bucketName: '桶名称',
    bucketType: '桶类型',
    businessType: '业务类型',
    available: '桶可使用',
    storages: '桶已使用存储容量(M)',
    objects: '桶已使用存储对象数',
  },
  queueTab: {
    queueName: '队列名称',
    queueType: '队列类型',
    maxSize: '最大值',
    usedSize: '使用值',
    usedRate: '使用率(%)',
  },
  apacheTab: {
    busyWorkers: '忙碌线程数',
    idleWorkers: '空闲线程数',
    incrRequests: 'apache请求数(次/分)',
    available: 'apache可使用',
  },
  wsConnectTab: {
    connectNum: '实例APP连接数',
    totalConnectNum: '实例连接总数',
  },
  domainCertTab: {
    domain: '域名',
    customerName: '客户名称',
    envType: '环境类型',
    available: '域名可使用',
    remainDays: '有效剩余时间(天)',
    cert: '证书名称',
    appId: '关联的AppId',
    ptp: '推送服务类型',
  },
  dpikeyTab: {
    dpikeySend: 'dpikey下发',
    dpikeyErr: 'dpikey异常日志',
    errors: 'dpikey异常日志次数(个/分)',
  },
  websocketTab: {
    heartbeat: 'P2P和设备服务的心跳正常',
    disconnects: 'P2P和设备服务的断开次数',
    instanceId: '设备服务实例',
    serverInstance: '服务端实例',
    clientInstance: '客户端实例',
    p2pInstanceId: 'P2P实例',
    websocketType: 'websocket类型',
    agentHost: 'agent节点',
  },
  emailTab: {
    emailErr: '邮件发送失败存在异常告警',
    emailLimit: '邮件发送超出频率告警',
    errors: '邮件发送失败量(个/分)',
    limits: '邮件发送超出频率次数(个/分)',
    nums: '邮件发送成功量(个/分)',
    tps: '邮件发送的TPS的峰值(个/秒)',
    summary: '邮件发送总量(个/分)',
  },
  textMessageTab: {
    smsErr: '短信发送失败存在异常告警',
    smsLimit: '短信发送超出频率控制告警',
    errors: '每分钟短信发送失败存在异常次数(个/分)',
    limits: '每分钟短信发送超出频率次数(个/分)',
    nums: '每分钟短信发送成功次数(个/分)',
    tps: '每分钟发送短信的TPS的峰值(个/秒)',
  },
  pushTab: {
    pushErr: '离线推送发送失败存在异常告警',
    pushLimit: '离线推送超出频率控制告警',
    offlineErrors: '离线推送失败数',
    offlineLimits: '离线推送超出频率数',
    offlineTps: '离线推送的TPS的峰值',
    offlinePushs: '离线推送成功数',
    onlineErrors: '在线推送失败数',
    onlinePushs: '在线推送成功数',
    onlineTps: '在线推送的TPS的峰值',
    errors: '推送失败总数',
    pushs: '推送成功总数',
    tps: '推送的TPS的峰值',
    apnsPushs: 'ios推送成功数',
    apnsErrors: 'ios推送失败数',
    fcmPushs: '谷歌推送成功数',
    fcmErrors: '谷歌推送失败数',
    hmsPushs: '华为推送成功数',
    hmsErrors: '华为推送失败数',
    linePushs: 'line推送成功数',
    lineErrors: 'line推送失败数',
  },
  accountTab: {
    accountType: '账号类型',
    registerErr: '账号注册过程发生异常告警',
    loginErr: '账号登录过程发生异常告警',
    registerErrors: '每分钟账号注册异常数',
    loginErrors: '每分钟账号登录异常数',
    registers: '注册账号数',
    logins: '登录账号次数',
  },
  natTab: {
    instanceId: '实例ID',
    serviceInstanceId: '服务实例ID',
    natInstanceId: 'NAT实例ID',
    nodeName: '区域名称',
    clusterName: '集群名称',
    clusterCode: '集群代码',
    nat_cleint_p2p_req_sn_online: '客户端通过sn请求连接的数量',
    nat_cleint_p2p_req_token_online: '客户端通过datoken请求连接的数量',
    nat_client_offline: '客户端掉线数量',
    nat_client_online: '客户端在线数量',
    nat_client_reg: '客户端上线数量',
    nat_dev_big_heat: '单端口设备端大心跳数量',
    nat_dev_offline: '设备端掉线数量',
    nat_dev_online: '设备端在线数量',
    nat_dev_reg: '设备端上线数量',
    nat_dev_small_heat: '单端口设备端小心跳数量',
    nat_dev_update_ip: '单端口设备端心跳协商数量',
    nat_dev_big_heat_v6: '双端口设备端大心跳数量',
    nat_dev_small_heat_v6: '双端口设备端小心跳数量',
    nat_dev_update_ip_v6: '双端口设备端心跳协商数量',
    nat_dev_online_dec: '设备在线数量降低数',
    syncErrors: 'RDC同步异常数',
    sendErrors: '下发异常数',
    cplusGetErrors: 'P2P获取异常数',
    javaGetErrors: '服务获取异常数',
    maxDevNum: '最大设备数',
    devNum: '设备在线数',
    connectionNum: '请求连接数',
    clientNum: '客户端在线数',
    p2pReqNum: 'P2P请求总数',
    relayReqNum: 'P2P转发请求总数',
    restartNum: '重启总数',
  },
  relayTab: {
    relay_client_req: '客户端请求转发数量',
    relay_dev_req: '设备端请求转发数量',
    relay_session: '转发连接数量',
    relay_session_end: '转发连接结束的数量',
    relay_speed_limit_count: '设备端转发限速的连接数量',
    currentSession: '当前转发连接数',
    natServerAddr: '所属NAT地址',
    maxSession: '最大转发连接数',
  },
  redirectTab: {
    redirect_client: '客户端重定向数量',
    redirect_client_full_list: '客户端重定向请求服务器全列表数量',
    redirect_dev: '设备端重定向数量',
    redirect_dev_inc: '设备端重定向新增数',
  },
  push1Tab: {
    customer: '客户',
    tpnsMsgRecv: 'TPNS每秒接受报警数',
    tpnsDispMsg: 'TPNS每秒分发报警数',
    tpnsDevTotal: 'TPNS推送设备总量',
    tpnsEnableDev: 'TPNS打开推送设备数',
    tpnsPendingJobCount: 'TPNS分发队列积压数',
    amdsPushReq: 'AMDS每秒接受Push请求数',
    amdsPushMsg: 'AMDS推送消息数',
    amdsExpCertCount: 'AMDS推送证书过期数',
    gmdsPushReq: 'GMDS每秒接受Push请求数',
    gmdsDispMsg: 'GMDS消息分发数',
    gpnsPushReq: 'GPNS每秒接受Push请求数',
    gpnsPushMsg: 'GPNS推送消息数',
    gpnsClientNum: 'GPNS在线手机数',
    gpnsClientRegNum: 'GPNS注册请求数',
    sdnsDevSrvReq: '设备端每秒重定向数',
    sdnsAppSrvReq: 'APP端重定向数',
  },
  alarmConfigTab: {
    ctTypeSelect: '请选择采集项',
    ctType: '采集项',
    ctPeriod: '采集周期',
    ctPeriodInput: '请输入采集周期',
    ctSubtype: '采集项子类',
    alarmIndexDesc: '告警指标名称',
    threshold: '普通阈值',
    thresholdInput: '请输入普通阈值',
    thresholdSelect: '请选择普通阈值',
    alarmThreshold: '发送告警阈值',
    alarmThresholdInput: '请输入告警阈值',
    alarmThresholdSelect: '请选择告警阈值',
    algorithm: '指标算法',
    alarmReceiver: '告警接收人',
    alarmEmail: '邮件接收人',
    modifyAlarmReceiver: '修改告警接收人',
    alarmEmailInput: '请输入邮件接收人',
    alarmReceiverInput: '请输入告警接收人',
    alarmSms: '短信接收人',
    alarmSmsInput: '请输入短信接收人',
    alarmLang: '语言',
    alarmLangSelect: '请选择语言',
    alarmReceiverLimit: '告警接收人最多允许10条',
    emailLangEmpty: '请确保邮件/短信接收人及语言填写完整',
    collectTime: '采集时长(s)',
    hourLimits: '每小时告警发送限制',
    dayLimits: '每天告警发送限制',
    alarmType: '告警类',
    alarmTypeSelect: '请选择告警类',
    ctIndexFieldSelect: '请选择告警指标名称',
  },
  alarmHistoryTab: {
    ctTypeSelect: '请选择采集项',
    hostNameInput: '请输入主机名',
    dcName: 'DC名称',
    dcNameInput: '请输入dc名称',
    dcSelect: '请选择dc',
    rangeSeparator: '至',
    monitorType: '采集项',
    host: '主机名',
    os: '系统',
    ip: 'IP',
    threshold: '告警阈值',
    actual: '实际值',
    ct: '时间',
    msg: '信息',
    sendNotice: '是否已发送告警',
    sendEmai: '已发送邮件',
    sendSms: '已发送短信',
    yes: '是',
    no: '否',
  },
  alarmDataTab: {
    systemMonitortType: '系统监控类',
    warnStartTime: '告警开始时间',
    warnDuration: '告警持续时长',
    alarmObject: '告警对象',
  },
  microLogTab: {
    microSelect: '请选择微服务列表',
    hostIpSelect: '请选择主机IP',
    dayTimeSelect: '请选择日期',
    fileName: '文件名称',
    fileUpdateTime: '文件更新时间',
    fileSize: '大小',
    hostIpInput: '请输入主机IP',
    downloadFreq: '下载太频繁，请10后重试',
  },
  appLogTab: {
    logIDInput: '请输入日志ID',
    phoneTypeInput: '请输入手机型号',
    softVersionInput: '请输入手机软件版本',
    errorCodeInput: '请输入错误码组',
    mccCodeInput: '请输入运营商编号',
    deviceSnInput: '请输入设备SN',
    logid: '日志ID',
    hwmodel: '手机型号',
    logver: '日志协议版本',
    firmver: '手机软件版本',
    mobileVer: '手机系统版本',
    errors: '错误码组',
    mcc: '运营商编号',
    sn: '设备SN',
    time: '日志生成时间',
    downloadLog: '下载日志',
    timezone: '日志生成时区',
    memory: '手机内存',
    cpucore: '手机CPU核数',
    appver: '软件版本号',
    logintime: '登录时间',
    nettype: '连接状况',
    conntype: '连接类型',
    isreduce: '是否压缩',
    reduceformat: '压缩格式',
    isencrypt: '是否加密',
    encryformat: '加密方式',
  },
  restfullLogTab: {
    userIdInput: '请输入用户ID',
    hostnameSelect: '请选择主机名',
    requestPathInput: '请输入接口地址',
    requestPath: 'url地址',
    requsetId: '请求ID',
    username: '用户名',
    userId: '用户ID',
    startTime: '开始时间',
    endTime: '结束时间',
    requestCost: '接口耗时',
    callClient: '调用客户端',
    responseCode: '接口响应码',
    reqParams: '入参',
    resParams: '出参',
    platform: '平台',
    coId: '企业ID',
    clientIp: '客户端IP',
    partner: '经销商',
    installer: '安装商',
    reqStatusSelect: '请选择请求状态',
    reqSuccess: '请求成功',
    reqFail: '请求失败',
    errorMsg: '错误信息',
    tidInput: '请输入tid',
  },
  kafkaSearchTab: {
    topicSelect: '请选择topic',
    hourSelect: '请选择小时',
    urlInput: '请输入url',
    snInput: '请输入sn',
    keyInput: '请输入key',
    keywordInput: '请输入关键字',
    keyOrUrlInput: '请输入url或关键字',
    partition: '分区',
    offset: '偏移量',
    content: '消息内容',
    createTime: '生成时间',
    prefixFilter: '按名称前缀过滤',
    fieldSearchKeyInput: '请输入搜索字段',
    copySuccess: '复制成功！',
    customSearch: '自定义搜索',
    customInput: '请输入自定义搜索',
    abnormalData: '数据格式异常',
    pasteExample: '粘贴如下示例：',
    paramsDesc: '参数说明',
    hour: '小时数',
    protocolName: '协议名',
    forExample: '例如：',
    ciphertextSN: '密文SN',
    searchKeywords: '搜索关键字',
  },
  ossSearchTab: {
    bucketSelect: '请选择桶',
    name: '名称',
    typeSize: '类型/大小',
    modifyTime: '最后修改时间',
    folder: '目录',
  },
  redisSearchTab: {
    instanceSelect: '请选择实例列表',
  },
  mysqlSearchTab: {
    customMysql: '自定义mysql',
    customMysqlInput: '请输入sql',
    inputDesc: '查询限制：只能执行select查询语句，返回结果限制100条',
    searchStatus: '查询状态',
    errorMessage: '错误信息',
    costTime: '耗时',
  },
  deviceSearchTab: {
    p2pServerSelect: '请选择P2P Server',
    snListInput: '请输入SN',
    onLine: '在线',
    offLine: '离线',
    onLineStatus: '在线状态',
    p2pVersion: 'P2P版本',
    bindStatus: '绑定状态',
    bindUserInfo: '绑定用户信息',
    devType: '设备类型',
    publicIp: '公共IP',
    publicPort: '公共端口',
    localIp: '本地IP',
    localPort: '本地端口',
  },
  configParamTab: {
    sourceTypePhd: '请选择数据源类型',
    dataSourceType: '数据源类型',
    dataSourceIdenti: '数据源标识',
    configuration: '配置',
    configRelease: '配置发布',
    releaseTip: '确认发布配置吗？',
    configReleaseSucccess: '配置发布成功',
    pendingEffective: '待生效',
    effective: '生效',
  },
  operateLogTab: {
    userName: '用户名',
    eventModule: '事件模块',
    eventType: '事件类型',
    eventModuleSelect: '请选择事件模块',
    eventTypeSelect: '请选择事件类型',
  },
  scheduleTaskLogTab: {
    excuteClassPHD: '请输入执行类',
    scheduleTaskSelect: '请选择定时任务',
    scheduleTask: '定时任务',
    businessId: '业务ID',
    executeClass: '执行类',
    executeMethod: '执行方法',
    executeStatus: '执行状态',
    zone: '时区',
    scheduleInstanceId: '调度实例ID',
    excuteInstanceId: '执行实例ID',
    createTime: '创建时间',
    modifyTime: '修改时间',
    excuteCost: '执行耗时',
    code: '结果编码',
    msg: '结果信息',
    excutePeroid: '执行周期',
    excuteStatusSelect: '请选择执行状态',
    other: '其他',
    success: '成功',
    fail: '失败',
    running: '运行中',
    timeout: '超时',
  },
  devopsStatisticsTab: {
    clientType: '客户端类型',
    clientTypeSelect: '请选择客户端类型',
    function: '功能',
    functionSelect: '请选择功能',
    product: '产品',
    module: '模块',
    requestCount: '调用总次数',
    successCount: '调用成功次数',
    failedCount: '调用失败次数',
    requestPath: '请求路径',
    accountCount: '调用账号数',
    maxCost: '最大耗时(ms)',
    avgCost: '平均耗时(ms)',
    monitorType: '监控类型',
    ctIndexField: '监控指标',
    groupBySelect: '请选择分组',
    count: '总数',
    hostAlarm: '主机告警',
    microServiceAlarm: '微服务告警',
    openSourceAlarm: '开源件告警',
    domainCert: '域名和证书',
    businessMonitor: '业务监控',
    host: '主机',
    alarmCount: '告警次数',
    serverSide: '服务端',
    clientSide: '客户端',
  },
  authConfig: {
    module: '模板',
    func: '功能',
    // VMS
    vms: 'VMS产品',
    vms_account: 'VMS账号',
    vms_account_member_mgr: 'VMS成员账号管理',
    vms_account_person_mgr: 'VMS个人账号管理',
    vms_account_qry: 'VMS账号查询',
    vms_account_invite_query: 'VMS账号邀请查询',
    vms_account_mgr: 'VMS账号管理',
    vms_cloudstorage: '云存储',
    vms_cloudstorage_mgr: '云存储管理',
    vms_cloudstorage_qry: '云存储查询',
    vms_stgorder_qry: '云存储订单查询',
    vms_archives: '云归档',
    vms_archives_mgr: '云归档管理',
    vms_archives_qry: '云归档查询',
    vms_site: '站点',
    vms_site_mgr: '站点管理',
    vms_site_qry: '站点查询',
    vms_device: '设备',
    vms_device_mgr: '设备管理',
    vms_device_qry: '设备查询',
    vms_device_bind: '设备绑定管理',
    vms_device_connect: '跳转设备Web页面',
    vms_device_opr: '设备操作',
    vms_devicetrusteeships: '设备托管',
    vms_devicetrusteeships_mgr: '设备托管管理',
    vms_devicetrusteeships_qry: '设备托管查询',
    vms_event: '设备事件',
    vms_event_mgr: '设备事件管理',
    vms_event_qry: '设备事件查询',
    vms_eventrule: '事件规则',
    vms_eventrule_mgr: '事件规则管理',
    vms_eventrule_qry: '事件规则查询',
    vms_schedule: '排程',
    vms_schedule_mgr: '排程管理',
    vms_schedule_qry: '排程查询',
    vms_vehicleplate: '车牌',
    vms_vehicleplate_mgr: '车牌管理',
    vms_vehicleplate_qry: '车牌查询',
    vms_vehicleplate_exp: '车牌导出',
    vms_license: 'license',
    vms_license_mgr: 'license管理',
    vms_license_qry: 'license查询',
    vms_licorder_qry: 'license购买查询',
    vms_customercustomize: '经销商定制',
    vms_customercustomize_qry: '经销商定制查询',
    vms_collectionchl: '收藏通道',
    vms_collectionchl_mgr: '收藏通道管理',
    vms_collectionchl_qry: '收藏通道查询',
    vms_config: '用户配置',
    vms_config_qry: '用户配置查询',
    vms_config_mgr: '用户配置管理',
    vms_archauth_qry: '归档权限查询',
    vms_archauth_mgr: '归档权限管理',
    vms_remoteaccessreq: '远程访问',
    vms_remoteaccessreq_mgr: '远程访问管理',
    vms_remoteaccessreq_qry: '远程访问查询',
    vms_system: '系统管理',
    vms_system_dict_qry: '数据字典查询',
    vms_system_msg_qry: '系统消息查询',
    vms_system_paytype_qry: '支付类型查询',
    vms_system_oplog_qry: '操作日志查询',
    vms_system_version_qry: '系统版本',
    vms_system_other: '其他',
    vms_account_role: '角色配置',
    vms_account_role_mgr: '角色管理',
    vms_account_role_qry: '角色查询',
    vms_arming: '布撤防',
    vms_arming_mgr: '布撤防管理',
    vms_arming_qry: '布撤防查询',
    monitor_dev_notify: '告警通知',
    monitor_dev_notify_qry: '告警通知查询',
    monitor_dev_notify_mgr: '告警通知管理',
    mfa_config: 'mfa配置',
    mfa_config_qry: 'mfa配置查询',
    mfa_config_mgr: 'mfa配置管理',
    mfa_config_switch_mgr: 'mfa配置开关管理',
    vms_org: '用户组织',
    vms_org_qry: '用户组织查询',
    vms_org_mgr: '用户组织管理',
    // partner
    partner: '服务上云-安装商',
    partner_user: '安装商账号',
    partner_user_member_mgr: '安装商成员账号管理',
    partner_user_person_mgr: '安装商个人账号管理',
    partner_coinfo: '安装商企业',
    partner_coinfo_mgr: '安装商企业管理',
    partner_coinfo_qry: '安装商企业查询',
    partner_customter: '客户',
    partner_customter_mgr: '客户管理',
    partner_customter_qry: '客户查询',
    partner_tob_user_mgr: 'VMS客户管理',
    partner_tob_user_qry: 'VMS客户查询',
    partner_site: '站点',
    partner_site_mgr: '站点信息管理',
    partner_site_qry: '站点信息查询',
    partner_device: '设备',
    partner_device_mgr: '设备管理',
    partner_device_qry: '设备查询',
    partner_cloudres: '云商',
    partner_cloudres_qry: '云商查询',
    partner_vms: 'CloudVMS',
    partner_vms_mgr: 'VMS配置',
    partner_vms_qry: 'VMS查询',
    partner_push: '推送',
    partner_push_mgr: '推送配置/推送信息管理',
    partner_push_qry: '推送配置/推送信息查询',
    partner_ra: '远程访问',
    partner_ra_mgr: '远程访问操作',
    partner_ra_qry: '远程访问查询',
    partner_sysinfo: '系统信息',
    partner_sysinfo_qry: '系统信息查询',
    partner_cldstorage: '云存储',
    partner_cldstorage_qry: '云存储查询',
    partner_feedback: '意见反馈',
    partner_feedback_mgr: '意见反馈管理',
    partner_feedback_qry: '意见反馈查询',
    partner_account_role: '角色配置',
    partner_account_role_mgr: '角色管理',
    partner_account_role_qry: '角色查询',
    partner_other: '其它',
    partner_other_log_qry: '操作日志',
    partner_other_res_qry: '功能资源',
    partner_other_remote_access: '远程访问',
    partner_points_mall: '积分商城',
    partner_points_mall_mgr: '积分商城管理',
    partner_points_mall_qry: '积分商城查询',
    // user
    user: 'SuperLivePlus/Max',
    user_account: '账号',
    user_account_qry: '账号查询',
    user_account_mgr: '账号管理',
    user_device: '设备',
    user_device_qry: '设备查询',
    user_device_mgr: '设备管理',
    user_subscribe: '订阅',
    user_subscribe_qry: '订阅查询',
    user_subscribe_mgr: '订阅管理',
    user_alarm: '告警',
    user_alarm_qry: '告警查询',
    user_alarm_mgr: '告警管理',
    user_cloudstorage: '云存储',
    user_cloudstorage_qry: '云存储查询',
    user_cloudstorage_mgr: '云存储管理',
    user_message: '消息',
    user_message_qry: '消息查询',
    user_message_mgr: '消息管理',
    user_feedback: '意见反馈',
    user_feedback_qry: '意见反馈查询',
    user_feedback_mgr: '意见反馈管理',
    user_config: '用户配置',
    user_config_qry: '用户配置查询',
    user_config_mgr: '用户配置管理',
    user_site: '站点',
    user_site_qry: '站点查询',
    user_site_mgr: '站点管理',
    user_devicetrusteeships: '设备托管',
    user_devicetrusteeships_qry: '设备托管查询',
    user_devicetrusteeships_mgr: '设备托管管理',
    user_devicetransfer: '设备转移',
    user_devicetransfer_qry: '设备转移查询',
    user_devicetransfer_mgr: '设备转移管理',
    user_building: '门禁楼宇',
    user_building_qry: '门禁楼宇查询',
    // Distributor
    distributor: '服务上云-经销商',
    distributor_account: '经销商账号',
    distributor_account_mgr: '经销商企业账号管理',
    distributor_account_person_mgr: '经销商个人账号管理',
    distributor_account_qry: '经销商账号查询',
    distributor_installerinvite: '邀请安装商',
    distributor_installerinvite_mgr: '邀请安装商管理',
    distributor_subcustomerinvite: '邀请分销商',
    distributor_subcustomerinvite_mgr: '邀请分销商管理',
    distributor_storage: '云存储订单',
    distributor_storage_qry: '云存储订单查询',
    distributor_account_role: '角色配置',
    distributor_account_role_mgr: '角色管理',
    distributor_account_role_qry: '角色查询',
    distributor_other: '其它',
    distributor_other_log_qry: '操作日志',
    distributor_other_message_qry: '消息',
  },
}
