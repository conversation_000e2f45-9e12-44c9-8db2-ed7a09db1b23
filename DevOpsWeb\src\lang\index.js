import Vue from 'vue'
import VueI18n from 'vue-i18n'

// 引入element的语言包
import enLocale from 'element-ui/lib/locale/lang/en'
import zhLocale from 'element-ui/lib/locale/lang/zh-CN'

import tvtEnLocale from 'tvtcloudbaiscwidget/lib/locale/lang/en-US'
import tvtZhLocale from 'tvtcloudbaiscwidget/lib/locale/lang/zh-CN'

// 自定义语言包
import en from './en-US'
import zh from './zh-CN'

Vue.use(VueI18n)

const messages = {
  'en-US': {
    ...en,
    ...enLocale,
    ...tvtEnLocale,
  },
  'zh-CN': {
    ...zh,
    ...zhLocale,
    ...tvtZhLocale,
  },
}

export const getLocale = () => {
  let locale = sessionStorage.getItem('localeType')
  if (!locale) {
    const navigatorLocal = navigator.language
    locale = Object.keys(messages).find((o) => navigatorLocal.indexOf(o) > -1)
    sessionStorage.setItem('localeType', locale || 'en-US')
  }
  // return locale || 'en-US'
  return locale || 'zh-CN'
}

export const langOptions = [
  { label: '简体中文', value: 'zh-CN' },
  { label: 'English', value: 'en-US' },
]

const i18n = new VueI18n({
  locale: getLocale(),
  // 对应的本地化key不存在时回退到英文
  // fallbackLocale: 'en-US',
  fallbackLocale: 'zh-CN',
  // 静默回退，控制台没有warning提示
  globalInjection: false,
  messages,
})

export default i18n
