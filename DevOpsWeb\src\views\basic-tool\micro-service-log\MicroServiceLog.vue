<template>
  <!-- 微服务运行日志 -->
  <div class="page-wrapper">
    <div class="search-wrapper">
      <tvt-select
        v-model="filterCond.projectName"
        :options="microTypeOptions"
        :mockplaceholder="$t('microLogTab.microSelect')"
        :clearable="false"
        style="margin-right: 20px"
        @change="getMicroLogList"
      />
      <tvt-date-picker
        v-if="filterCond.projectName !== 'HeapDump'"
        v-model="filterCond.dayTime"
        align="right"
        type="daterange"
        :placeholder="$t('microLogTab.dayTimeSelect')"
        :clearable="false"
        style="width: 250px; margin-right: 20px"
        :picker-options="pickerOptions"
        @change="handleDate"
      ></tvt-date-picker>
      <tvt-select
        v-model="filterCond.hostIp"
        :options="hostIpOptions"
        :mockplaceholder="$t('microLogTab.hostIpSelect')"
        clearable
        style="width: 250px; margin-right: 20px"
        @change="getMicroLogList"
      />
      <div class="search-btn-box">
        <el-button type="primary" size="small" round @click="handleReset">{{
          $t('reset')
        }}</el-button>
        <el-button type="primary" size="small" round @click="handleRefresh">{{
          $t('refresh')
        }}</el-button>
      </div>
      <div class="search-left-wrapper"></div>
    </div>
    <!-- 微服务运行日志表格 -->
    <div class="table-wrapper">
      <tvt-table
        ref="myTable"
        v-myLoading="loading"
        :data="microLogList"
        :columns="microLogColumn"
        :border="true"
        :border-bottom="true"
        :pagination="false"
        @onFetchData="getMicroLogList"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.prop === 'updateTime'">
            <span>{{ stampToStrLongMethod(row[column.prop]) }}</span>
          </template>
          <template v-if="column.prop === 'operation'">
            <span
              v-if="filterCond.projectName !== 'HeapDump'"
              class="btn-text"
              @click="downloadFile(row)"
              >{{ $t('download') }}</span
            >
          </template>
        </template>
      </tvt-table>
    </div>
  </div>
</template>
<script>
import {
  debounce,
  stampToStrLong,
  hanldeDownload,
  commonShortcuts,
} from '@/utils/common'
import {
  getServiceList,
  getServiceLogList,
  getHostNameList,
  downloadServiceLog,
} from '@/api/basicTool.js'
import { getLocale } from '@/lang'
import errorCode from '@/api/errorCode.js'
import { getInitDate } from '@/utils/dateFormat.js'

export default {
  name: 'MicroServiceLog',
  components: {},
  data() {
    const pageVM = this
    return {
      defaultTimeRange: [],
      filterCond: {
        projectName: '',
        dayTime: getInitDate(),
        hostIp: '',
      },
      pickerOptions: {
        // 点击时，选择的是开始时间，也就是minDate
        onPick: (time) => {
          // 记录首次选择的日期（自然月计算基准）
          if (time.minDate && !time.maxDate) {
            pageVM.startDate = time.minDate
          }
          if (time.maxDate) {
            pageVM.startDate = null
          }
        },
        disabledDate(time) {
          if (pageVM.startDate) {
            const start = moment(pageVM.startDate)
            const end = moment(time)
            // 限制结束日期在[start, start+1月]范围内,且不能超过当前日期
            return (
              time.getTime() > Date.now() ||
              end < start.clone().add(-29, 'd') ||
              end > start.clone().add(29, 'd')
            )
          } else {
            return time.getTime() > Date.now()
          }
        },
        shortcuts: commonShortcuts,
      },
      loading: false,
      microTypeOptions: [],
      hostIpOptions: [],
      microLogList: [],
      microLogColumn: [
        {
          label: this.$t('microServeTab.projectName'),
          prop: 'projectName',
          minWidth: 150,
        },
        {
          label: this.$t('microServeTab.instanceId'),
          prop: 'instanceId',
          width: 150,
        },
        {
          label: this.$t('hostTab.hostName'),
          prop: 'hostName',
          width: 150,
        },
        {
          label: this.$t('resourceTab.ip'),
          prop: 'agentIp',
          width: 150,
        },
        {
          label: this.$t('microLogTab.fileName'),
          prop: 'fileName',
          minWidth: 150,
        },
        {
          label: this.$t('microLogTab.fileUpdateTime'),
          prop: 'updateTime',
          slotName: 'bodyCell',
          width: 160,
        },
        {
          label: this.$t('microLogTab.fileSize'),
          prop: 'dataSize',
          width: 150,
        },
        {
          label: this.$t('operation'),
          prop: 'operation',
          slotName: 'bodyCell',
          fixed: 'right',
          width: 100,
          hide: !this.$isAuthorityExist(['devops_tool_serviceLog_mgr']),
        },
      ],
      downloadSet: new Set(), // 记录下载的日志
    }
  },
  async mounted() {
    await this.getServiceList()
    this.getHostNameList()
    this.getMicroLogList()
  },
  methods: {
    stampToStrLongMethod(time) {
      let intTime = parseInt(time) //接口给的字符串时间
      return stampToStrLong(intTime)
    },
    // 获取微服务列表
    async getServiceList() {
      try {
        // 1:用于微服务日志查询 2:用于resultful接口日志查询
        const { data = [] } = await getServiceList(1)
        this.microTypeOptions = data.map((item) => ({
          label: item,
          value: item,
        }))
        // 微服务列表默认选中第一个
        this.filterCond.projectName = data[0]
      } catch (error) {
        console.error(error)
      }
    },
    // 获取主机名下拉选项
    async getHostNameList() {
      try {
        const { data = [] } = await getHostNameList(1) // 1:用于微服务日志查询 2:用于resultful接口日志查询
        this.hostIpOptions = data.map((item) => ({
          label: item.hostIp,
          value: item.hostIp,
        }))
      } catch (error) {
        console.error(error)
      }
    },
    getMicroLogList: debounce(async function () {
      this.loading = true
      try {
        const { projectName, dayTime, hostIp } = this.filterCond
        const params = {}
        if (projectName) {
          params.projectName = projectName
        }
        // HeapDump不传时间
        if (projectName !== 'HeapDump' && dayTime && dayTime.length > 0) {
          params.startDate = window.moment(dayTime[0]).format('yyyy-MM-DD')
          params.endDate = window.moment(dayTime[1]).format('yyyy-MM-DD')
        }
        if (hostIp) {
          params.hostIp = hostIp
        }
        const { data = [] } = await getServiceLogList(params)
        this.microLogList = data
        this.loading = false
      } catch (error) {
        console.error(error)
        this.loading = false
      }
    }, 500),
    handleReset() {
      // 条件重置
      this.filterCond = {
        ...this.filterCond,
        projectName: this.microTypeOptions[0].value,
        dayTime: getInitDate(),
        hostIp: '',
      }
      this.$nextTick(() => {
        this.getMicroLogList()
      })
    },
    handleRefresh() {
      this.getMicroLogList()
    },
    async downloadFile(row) {
      const { downloadPath, fileName } = row
      const params = {
        url: downloadPath,
        type: 2, // 1 OSS类 2 日志类
      }
      // 判断url是否在10s内点击过，如果点击过则提示频率太快
      if (this.downloadSet.has(downloadPath)) {
        this.$message.warning(this.$t('microLogTab.downloadFreq'))
        return
      }
      this.downloadSet.add(downloadPath)

      // 添加loading效果
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, .5)',
        text: this.$t('LoadingDownload'), // 或者使用 '下载中...'
      })

      try {
        const resBlob = await downloadServiceLog(params)
        // 去掉文件名中的后缀
        const baseFileName =
          fileName.substring(0, fileName.lastIndexOf('.')) || fileName
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            // 尝试解析返回内容
            const result = JSON.parse(e.target.result)
            if (result.basic && result.basic.code) {
              const errorObj = errorCode[getLocale()]
              // 解析成功，提示错误信息
              this.$message.error(errorObj[result.basic.code])
              // 出错时需要清除下载限制
              this.downloadSet.delete(downloadPath)
              loading.close()
              return
            }
          } catch (parseError) {
            // 如果无法解析为JSON，说明是正常的文件内容，直接下载
            hanldeDownload(resBlob, `${baseFileName}.zip`)
            // 定时器10s后放开下载限制
            setTimeout(() => {
              this.downloadSet.delete(downloadPath)
            }, 10000)
            loading.close()
          }
        }
        reader.readAsText(resBlob)
      } catch (error) {
        console.error('下载文件失败:', error)
        this.downloadSet.delete(downloadPath)
        loading.close()
      }
    },
    handleDate(val) {
      this.filterCond.dayTime = val
      this.getMicroLogList()
    },
  },
}
</script>
<style lang="scss" scoped>
.btn-text {
  color: #429efd !important;
  cursor: pointer;
  + .btn-text {
    margin-left: 20px;
  }
}
</style>
