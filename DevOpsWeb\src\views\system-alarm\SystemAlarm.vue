<template>
  <div class="system-alarm">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'SystemAlarm',
  components: {},
  computed: {},
  mounted() {},
  methods: {},
}
</script>
<style lang="scss" scoped>
.system-alarm {
  width: 100%;
  // height: 100%;
  overflow: auto;
}
</style>
<style>
.system-alarm .search-wrapper .el-input__inner {
  height: 32px;
  line-height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 16px;
}
.system-alarm .search-wrapper .el-input__icon {
  line-height: 34px;
}

.system-alarm .search-wrapper .tvt-input .tvt-field-set {
  border: none;
}
.system-alarm .search-wrapper .tvt-select .tvt-field-set {
  border: none;
}

.system-alarm .table-wrapper .el-table .el-table__cell {
  padding: 8px 0 !important;
}
</style>
