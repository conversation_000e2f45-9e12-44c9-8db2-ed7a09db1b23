<template>
  <!-- 操作日志 -->
  <div class="page-wrapper">
    <div class="search-wrapper">
      <tvt-select
        v-if="envDcInfo && envDcInfo.dcType === 'rdc'"
        v-model="filterCond.dcId"
        :options="areaOptions"
        :mockplaceholder="$t('alarmHistoryTab.dcSelect')"
        style="width: 250px; margin-right: 20px"
        @focus="handleFocus"
        @change="handleRefresh"
      />
      <tvt-select
        v-model="filterCond.eventModule"
        :options="moduleOptions"
        :mockplaceholder="$t('operateLogTab.eventModuleSelect')"
        style="width: 250px; margin-right: 20px"
        clearable
        @focus="handleFocus"
        @change="handleEventModule"
      />
      <tvt-select
        v-model="filterCond.eventType"
        :options="typeOptions"
        :mockplaceholder="$t('operateLogTab.eventTypeSelect')"
        style="width: 250px; margin-right: 20px"
        clearable
        @focus="handleFocus"
        @change="handleRefresh"
      />
      <search-input
        v-model="filterCond.userName"
        :placeholder="$t('login.userNamePHD')"
        clearable
        size="small"
        maxlength="32"
        style="width: 250px; margin-right: 20px"
        @change="handleRefresh"
      />
      <tu-search-time
        ref="tuSearchTimeRef"
        v-model="filterCond.timeRange"
        class="search-time"
        :default="defaultTimeRange"
        @change="handleRefresh"
      />
      <div class="search-btn-box">
        <el-button type="primary" size="small" round @click="handleReset">{{
          $t('reset')
        }}</el-button>
        <el-button type="primary" size="small" round @click="handleRefresh">{{
          $t('refresh')
        }}</el-button>
      </div>
      <div class="search-left-wrapper"></div>
    </div>
    <!-- 操作日志表格 -->
    <div class="table-wrapper">
      <tvt-table
        ref="myTable"
        v-myLoading="loading"
        :data="operateLogList"
        :columns="operateLogColumn"
        :border="true"
        :border-bottom="true"
        :pagination="{
          total: operateLogTotal,
          current: filterCond.current,
          size: filterCond.size,
          'page-sizes': $tablePageSizes,
          background: true,
        }"
        @onFetchData="getOperateLogList"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.prop === 'eventTime'">
            <span>{{ stampToStrLongMethod(row[column.prop]) }}</span>
          </template>
          <template v-if="column.prop === 'content'">
            <el-tooltip
              class="item"
              effect="dark"
              :content="row[column.prop]"
              placement="left-start"
              :disabled="!isShowTooltip"
            >
              <span
                class="tooltip-ellipsis-box text-over-ellipsis"
                @mouseenter="visibilityChange($event)"
                >{{ row[column.prop] }}</span
              >
            </el-tooltip>
          </template>
        </template>
      </tvt-table>
    </div>
  </div>
</template>
<script>
import { debounce, stampToStrLong } from '@/utils/common'
import { getOperateType, getOperateLogList } from '@/api/basicTool'
import TuSearchTime from '@/components/common/TuSearchTime.vue'
import { getLocale } from '@/lang'
import { getDcInfoListApi } from '@/api/home.js'
import { mapState } from 'vuex'

export default {
  name: 'OperateLog',
  components: {
    TuSearchTime,
  },
  data() {
    return {
      defaultTimeRange: [],
      filterCond: {
        current: 1,
        size: 20,
        dcId: '',
        eventModule: '',
        eventType: '',
        userName: '',
        timeRange: [],
      },
      loading: false,
      operateLogTotal: 0,
      operateLogList: [],
      areaOptions: [],
      operateLogColumn: [
        {
          label: this.$t('restfullLogTab.clientIp'),
          prop: 'clientIp',
          width: 180,
        },
        {
          label: this.$t('operateLogTab.userName'),
          prop: 'userName',
          width: 200,
        },
        {
          label: this.$t('operateLogTab.eventModule'),
          prop: 'eventModule',
          width: 150,
        },
        {
          label: this.$t('operateLogTab.eventType'),
          prop: 'eventType',
          width: 180,
        },
        {
          label: this.$t('kafkaSearchTab.content'),
          prop: 'content',
          slotName: 'bodyCell',
          minWidth: 200,
        },
        {
          label: this.$t('kafkaSearchTab.createTime'),
          prop: 'eventTime',
          slotName: 'bodyCell',
          width: 160,
        },
      ],
      downloadSet: new Set(), // 记录下载的日志
      isShowTooltip: false, // 是否显示提示文字
      operateTypeList: [],
      moduleOptions: [],
      typeOptions: [],
    }
  },
  computed: {
    ...mapState('params', ['envDcInfo']),
  },
  async mounted() {
    // 获取DC下拉
    await this.getAreaOptions()
    // 获取操作日志类型
    this.getOperateType()
    this.getOperateLogList()
  },
  methods: {
    // 获取DC下拉选项
    async getAreaOptions() {
      const res = await getDcInfoListApi(getLocale())
      this.areaOptions = res.data.map((item) => ({
        label: item.dcName,
        value: item.dcId,
      }))
      // DC的默认勾选
      const { dcId } = this.envDcInfo || {}
      this.filterCond.dcId = dcId
    },
    // 获取操作日志类型
    async getOperateType() {
      const { data = [] } = await getOperateType(getLocale())
      this.operateTypeList = data.slice()
      this.moduleOptions = Array.from(
        new Set((data || []).map((item) => item.module))
      ).map((item2) => ({
        label: item2,
        value: item2,
      }))
    },
    // 选择框获取焦点时
    handleFocus() {
      // 手动关闭时间弹窗
      if (this.$refs.tuSearchTimeRef) {
        this.$refs.tuSearchTimeRef.$refs.dateTimePicker.handleClose()
      }
    },
    handleEventModule(val) {
      const list = this.operateTypeList.filter((item) => item.module === val)
      this.typeOptions = list.map((item) => ({
        label: item.desc,
        value: item.type,
      }))
      this.filterCond.eventType = ''
      this.$nextTick(() => {
        this.handleRefresh()
      })
    },
    stampToStrLongMethod(time) {
      let intTime = parseInt(time) //接口给的字符串时间
      return stampToStrLong(intTime)
    },
    getOperateLogList: debounce(async function (pageSize) {
      try {
        if (pageSize == 1) {
          this.filterCond.current = 1
        } else if (pageSize && pageSize.current) {
          this.filterCond.current = pageSize.current
          this.filterCond.size = pageSize.size
        }
        const {
          current,
          size,
          dcId,
          eventModule,
          eventType,
          userName,
          timeRange,
        } = this.filterCond
        this.loading = true
        const params = { pageNum: current, pageSize: size, lang: getLocale() }
        if (dcId) {
          params.dcId = dcId
        }
        if (eventModule) {
          params.eventModule = eventModule
        }
        if (eventType) {
          params.eventType = eventType
        }
        if (userName) {
          params.userName = userName
        }
        if (timeRange && timeRange.length) {
          params.startTime = timeRange[0]
          params.endTime = timeRange[1]
          params.startTimeTmp = window
            .moment(timeRange[0])
            .format('YYYY-MM-DD HH:mm:ss')
          params.endTimeTmp = window
            .moment(timeRange[1])
            .format('YYYY-MM-DD HH:mm:ss')
        }
        const { data } = await getOperateLogList(params)
        if (data) {
          this.operateLogList = data.records
          this.operateLogTotal = parseInt(data.total)
        } else {
          this.operateLogList = []
          this.operateLogTotal = 0
        }
        this.loading = false
      } catch (error) {
        console.error(error)
        this.loading = false
      }
    }, 500),
    handleReset() {
      const { dcId } = this.envDcInfo || {}
      // 条件重置
      this.filterCond = {
        ...this.filterCond,
        dcId,
        eventModule: '',
        eventType: '',
        userName: '',
        timeRange: [],
        current: 1,
        size: 20,
      }
      this.typeOptions = []
      this.$nextTick(() => {
        this.handleRefresh()
      })
    },
    handleRefresh() {
      this.getOperateLogList(1)
    },
    // 是否提示toolTip
    visibilityChange(event) {
      const cellDom = event.target
      // range 表示文档的一个区域
      const range = document.createRange()
      range.setStart(cellDom, 0)
      range.setEnd(cellDom, cellDom.childNodes.length)
      const flag = this.getStyle(cellDom, '-webkit-line-clamp')
      if (flag == 'none') {
        // rangeWidth 表示元素内容的宽度
        const rangeWidth = range.getBoundingClientRect().width
        let padding =
          (parseInt(this.getStyle(cellDom, 'paddingLeft')) || 0) +
          (parseInt(this.getStyle(cellDom, 'paddingRight')) || 0)

        // cellDom.offsetWidth 表示选定区域的宽度
        if (rangeWidth > cellDom.offsetWidth - padding) {
          // 显示tooltip
          this.isShowTooltip = true
        } else {
          this.isShowTooltip = false
        }
      } else {
        // rangeHeight 表示元素内容的高度
        const rangeHeight = range.getBoundingClientRect().height
        let padding =
          (parseInt(this.getStyle(cellDom, 'paddingTop')) || 0) +
          (parseInt(this.getStyle(cellDom, 'paddingBottom')) || 0)

        // cellDom.offsetHeight 表示选定区域的高度
        if (rangeHeight > cellDom.offsetHeight - padding) {
          // 显示tooltip
          this.isShowTooltip = true
        } else {
          this.isShowTooltip = false
        }
      }
    },
    // 获取dom的样式
    getStyle(dom, attr) {
      return getComputedStyle(dom, null)[attr]
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .tooltip-ellipsis-box {
  display: block;
  width: 100%;
}

::v-deep .text-over-ellipsis {
  overflow: hidden; // 超出的文本隐藏
  text-overflow: ellipsis; // 溢出用省略号显示
  white-space: nowrap; // 溢出不换行
}

::v-deep .search-wrapper .kafka-mutli-input .el-input__inner {
  width: 300px;
}

::v-deep .search-wrapper .kafka-mutli-input.isFocus .label {
  border: 1px solid #dcdfe6;
  border-radius: 18px;
  width: 300px;
  box-sizing: border-box;
}

::v-deep .search-wrapper .kafka-mutli-input.isFocus .el-input__inner {
  border: 1px solid #429efd;
  box-sizing: border-box;
}
</style>
