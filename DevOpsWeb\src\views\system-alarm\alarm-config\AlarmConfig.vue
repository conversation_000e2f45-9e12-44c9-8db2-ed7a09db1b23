<template>
  <!-- 告警配置 -->
  <div class="alarm-config">
    <div class="search-wrapper">
      <div class="search-left-wrapper">
        <tvt-select
          v-model="filterCond.ctType"
          :options="ctyTypeOptions"
          :placeholder="$t('alarmConfigTab.alarmTypeSelect')"
          clearable
          style="width: 250px; margin-right: 20px"
          @change="getAlarmConfig(1)"
        />
        <!-- <el-button type="primary" size="small" round @click="handleQuery">{{$t('search')}}</el-button> -->
        <!-- 邮件 全部开启/关闭按钮 -->
        <el-button
          v-if="$isAuthorityExist(['devops_alarm_config_mgr'])"
          type="primary"
          size="small"
          round
          @click="handleToggle('alarmEmailStatus')"
        >
          {{ toggleFlag ? $t('allEmailClose') : $t('allEmailOpen') }}
        </el-button>
        <!-- 短信 全部开启/关闭按钮 -->
        <el-button
          v-if="$isAuthorityExist(['devops_alarm_config_mgr'])"
          type="primary"
          size="small"
          round
          @click="handleToggle('alarmSmsStatus')"
        >
          {{ toggleFlag2 ? $t('allSmsClose') : $t('allSmsOpen') }}
        </el-button>
        <!-- 批量修改告警接收人 -->
        <el-button
          v-if="$isAuthorityExist(['devops_alarm_config_mgr'])"
          type="primary"
          size="small"
          round
          @click="handleReceivers"
        >
          {{ $t('alarmConfigTab.modifyAlarmReceiver') }}
        </el-button>
      </div>
    </div>
    <!-- 告警表格 -->
    <div class="table-wrapper row-table-wrapper">
      <div class="tree-content">
        <el-tree
          ref="dcHostTree"
          v-loading="treeLoading"
          :data="treeData"
          :props="defaultProps"
          :current-node-key="currentNodeKey"
          node-key="key"
          highlight-current
          @node-click="handleNodeClick"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <i
              v-if="!data.isLeaf"
              class="el-icon-folder"
              style="color: #429efd"
            ></i>
            <span>{{ node.label }}</span>
          </span>
        </el-tree>
      </div>
      <div class="table-content">
        <tvt-table
          v-myLoading="loading"
          :data="alarmConfigList"
          :columns="alarmConfigColumns"
          :border="true"
          :border-bottom="true"
          :span-method="arraySpanMethod"
          :pagination="false"
          @onFetchData="getAlarmConfig"
        >
          <template #bodyCell="{ row, column, $index }">
            <template v-if="column.prop === 'alarmEmailStatus'">
              <!-- <span class="btn-text" @click="configEdit(row,$index)" v-if="$isAuthorityExist()">{{ $t('edit') }}</span> -->
              <el-switch
                v-if="$isAuthorityExist(['devops_alarm_config_mgr'])"
                class="switchStyle"
                :value="row.alarmEmailStatus === 2"
                :active-text="$t('on')"
                :inactive-text="$t('off')"
                @change="
                  (val) => handleSwitch(val, row, $index, 'alarmEmailStatus')
                "
              />
              <span v-else>{{
                row.alarmEmailStatus === 2 ? $t('on') : $t('off')
              }}</span>
            </template>
            <template v-if="column.prop === 'alarmSmsStatus'">
              <el-switch
                v-if="$isAuthorityExist(['devops_alarm_config_mgr'])"
                class="switchStyle"
                :value="row.alarmSmsStatus === 2"
                :active-text="$t('on')"
                :inactive-text="$t('off')"
                @change="
                  (val) => handleSwitch(val, row, $index, 'alarmSmsStatus')
                "
              />
              <span v-else>{{
                row.alarmSmsStatus === 2 ? $t('on') : $t('off')
              }}</span>
            </template>
            <template v-if="column.prop === 'operate'">
              <span class="btn-text" @click="configEdit(row, $index)">{{
                $t('edit')
              }}</span>
            </template>
          </template>
        </tvt-table>
      </div>
    </div>
    <!-- 告警配置编辑弹窗 -->
    <alarm-config-edit ref="alarmConfigDialog" @btnSave="configSave" />
    <!-- 批量修改邮件接收人 -->
    <alarm-receiver-edit ref="alarmReceiverDialog" @btnSave="receiverSave" />
  </div>
</template>
<script>
import {
  getMonitorConfList,
  editMonitorConf,
  getDcHostList,
} from '@/api/systemAlarm.js'
import { debounce } from '@/utils/common'
import { ctIndexAlgorithmObj } from '@/views/system-alarm/config.js'
import AlarmConfigEdit from '@/views/system-alarm/alarm-config/AlarmConfigEdit'
import AlarmReceiverEdit from '@/views/system-alarm/alarm-config/AlarmReceiverEdit'
import { getLocale } from '@/lang'

const lang = getLocale() || 'zh-CN'

export default {
  name: 'AlarmConfig',
  components: {
    AlarmConfigEdit,
    AlarmReceiverEdit,
  },
  data() {
    return {
      ctyTypeOptions: [], // 动态从接口获取
      orderList: [],
      alarmConfigList: [],
      loading: false,
      alarmConfigColumns: [
        {
          label: this.$t('alarmConfigTab.alarmType'),
          prop: 'ctTypeDesc',
          'min-width': lang === 'zh-CN' ? 120 : 140,
        },
        // {
        //   label: this.$t('alarmConfigTab.ctPeriod'),
        //   prop: 'ctPeriodDesc',
        //   width: lang === 'zh-CN' ? 100 : 140,
        // },
        {
          label: this.$t('alarmConfigTab.alarmIndexDesc'),
          prop: 'alarmIndexDesc',
          width: lang === 'zh-CN' ? 220 : 240,
        },
        {
          label: this.$t('alarmConfigTab.threshold'),
          prop: 'threshold',
          width: lang === 'zh-CN' ? 120 : 150,
        },
        {
          label: this.$t('alarmConfigTab.alarmThreshold'),
          prop: 'alarmThreshold',
          width: lang === 'zh-CN' ? 150 : 180,
        },
        // { label: this.$t('alarmConfigTab.algorithm'), prop: 'algorithm', 'min-width': 380},
        {
          label: this.$t('alarmEmailStatus'),
          prop: 'alarmEmailStatus',
          slotName: 'bodyCell',
          width: lang === 'zh-CN' ? 110 : 150,
        },
        {
          label: this.$t('alarmSmsStatus'),
          prop: 'alarmSmsStatus',
          slotName: 'bodyCell',
          width: lang === 'zh-CN' ? 110 : 150,
        },
        {
          label: this.$t('alarmConfigTab.collectTime'),
          prop: 'collectTime',
          width: lang === 'zh-CN' ? 120 : 140,
        },
        {
          label: this.$t('alarmConfigTab.hourLimits'),
          prop: 'hourLimits',
          width: lang === 'zh-CN' ? 150 : 210,
        },
        {
          label: this.$t('alarmConfigTab.dayLimits'),
          prop: 'dayLimits',
          width: lang === 'zh-CN' ? 140 : 190,
        },
        {
          label: this.$t('alarmConfigTab.alarmEmail'),
          prop: 'alarmEmail',
          'min-width': 140,
        },
        {
          label: this.$t('alarmConfigTab.alarmSms'),
          prop: 'alarmSms',
          'min-width': 140,
        },
        {
          label: this.$t('operation'),
          prop: 'operate',
          slotName: 'bodyCell',
          fixed: 'right',
          width: 100,
          hide: !this.$isAuthorityExist(['devops_alarm_config_mgr']),
        },
      ],
      filterCond: {
        ctType: 'All',
        current: 1,
        size: 1000,
        dcCode: null, // 选中主机对应的dcCode
        clusterCode: null, // 选中主机对应的clusterCode
        host: null, // 选中的主机名
      },
      toggleFlag: false,
      toggleFlag2: false,
      switchValue: false,
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: 'isLeaf',
      },
      treeLoading: false, // 树加载状态
      treeData: [],
      currentNodeKey: null, // 当前选中的节点key
    }
  },
  async mounted() {
    await this.getCtyTypeOptions() // 先获取下拉选项
    await this.getDcHostList()
    this.getAlarmConfig()
  },
  methods: {
    // 获取监控类型下拉选项
    async getCtyTypeOptions() {
      try {
        const params = {
          lang,
          dcCode: null,
          clusterCode: null,
          host: null,
        }
        const { data: resData = [] } = await getMonitorConfList(params)

        // 提取ctTypeDesc和ctType，并去重
        const typeMap = new Map()
        resData.forEach((item) => {
          if (item.ctType && item.ctTypeDesc) {
            typeMap.set(item.ctType, item.ctTypeDesc)
          }
        })

        // 转换为选项数组
        const options = Array.from(typeMap.entries()).map(([value, label]) => ({
          label,
          value,
        }))

        // 在最前面加上"全部"选项
        this.ctyTypeOptions = [
          { label: this.$t('all'), value: 'All' },
          ...options,
        ]
      } catch (error) {
        console.error('获取监控类型选项失败:', error)
        // 如果接口调用失败，使用默认的空数组
        this.ctyTypeOptions = [{ label: this.$t('all'), value: 'All' }]
      }
    },
    // 获取DC主机树形结构
    async getDcHostList() {
      this.treeLoading = true
      try {
        const { data = [] } = await getDcHostList(getLocale())
        // 创建树形结构
        const resData = data.slice().map((item) => {
          const { dcCode, dcName, clusterList = [] } = item
          const node = {
            label: dcName,
            dcCode,
            key: dcCode, // 添加唯一标识
            isLeaf: !Array.isArray(clusterList) || clusterList.length === 0,
            children: Array.isArray(clusterList)
              ? clusterList.map((cluster) => {
                  const { clusterCode, clusterName, hostList = [] } = cluster
                  return {
                    label: clusterName,
                    clusterCode,
                    dcCode, // 保存父节点dcCode
                    key: `${dcCode}-${clusterCode}`,
                    isLeaf: !Array.isArray(hostList) || hostList.length === 0,
                    children: Array.isArray(hostList)
                      ? hostList.map((host) => ({
                          label:
                            host.hostName === 'default'
                              ? `${clusterName}-${host.hostName}`
                              : host.hostName,
                          hostName: host.hostName,
                          hostIp: host.hostIp,
                          dcCode, // 保存父节点dcCode
                          clusterCode, // 保存父节点clusterCode
                          key: `${dcCode}-${clusterCode}-${host.hostName}`,
                          isLeaf: true,
                        }))
                      : null,
                  }
                })
              : null,
          }
          return node
        })
        this.treeData = resData
        this.$nextTick(() => {
          // 设置默认选中的 dcCode、clusterCode 和 host
          if (resData.length > 0) {
            const firstDc = resData[0]
            const { dcCode, label, children } = firstDc
            this.filterCond.dcCode = dcCode !== 'default' ? dcCode : null

            // 如果第一个DC节点有集群子节点
            if (children && children.length > 0) {
              const firstCluster = children[0]
              const { clusterCode, children: hostList } = firstCluster
              this.filterCond.clusterCode = clusterCode
              // 如果第一个集群有主机子节点
              if (hostList && hostList.length > 0) {
                const { dcCode, clusterCode, label, hostName } = hostList[0]
                this.filterCond.host = hostName !== 'default' ? label : null
                this.currentNodeKey = `${dcCode}-${clusterCode}-${label}`
              } else {
                this.currentNodeKey = `${dcCode}-${clusterCode}`
              }
              this.$refs.dcHostTree.setCurrentKey(this.currentNodeKey)
            } else {
              this.currentNodeKey = label
              this.$refs.dcHostTree.setCurrentKey(label)
            }
          }
        })
      } catch (error) {
        console.error(error)
      } finally {
        this.treeLoading = false
      }
    },
    // 点击节点重新请求表格
    handleNodeClick(data) {
      if (data.isLeaf) {
        // 更新当前选中节点的key
        this.currentNodeKey = data.key
        this.$refs.dcHostTree.setCurrentKey(data.key)
        // 是叶子节点才请求表格
        const { dcCode, clusterCode = null, label, hostName = null } = data
        const newDcCode = dcCode && dcCode !== 'default' ? dcCode : null
        const newHost = hostName && hostName !== 'default' ? label : null
        const {
          dcCode: rowDcCode,
          host: rowHost,
          clusterCode: rowClusterCode,
        } = this.filterCond
        if (
          rowDcCode !== newDcCode ||
          rowHost !== newHost ||
          rowClusterCode !== clusterCode
        ) {
          this.filterCond = {
            ...this.filterCond,
            dcCode: newDcCode,
            host: newHost,
            clusterCode,
            current: 1,
          }
          this.handleQuery()
        }
      } else {
        // 如果点击的是父节点，保留之前选中的状态
        this.$refs.dcHostTree.setCurrentKey(this.currentNodeKey)
      }
    },
    // 计算表格数据的rowSpan和colSpan
    countCtType(data) {
      // 先遍历一遍表格，统计每种采集项和采集子项的数量，用作后续计算rowSpan的依据
      const collectCountObj = data.reduce((pre, next) => {
        const { ctType } = next
        if (pre[ctType]) {
          pre[ctType] += 1
        } else {
          pre[ctType] = 1
        }
        return pre
      }, {})
      return collectCountObj
    },
    // 给计算表格数据的rowSpan和colSpan
    calculateRowSpan(collectCountObj, data) {
      const ctTypeSet = new Set()
      data.forEach((item) => {
        const { ctType } = item
        const ctTypeNum = collectCountObj[ctType]
        if (!ctTypeSet.has(ctType)) {
          item.firstColumnSpan = [ctTypeNum, 1]
          ctTypeSet.add(ctType)
        }
        // 指标直接用alarmIndexDesc，后台进行了国际化
        // item.alarmIndexFieldName = alarmIndexDescObj[item.alarmIndexDesc] // 指标中英文翻译
        item.algorithm = ctIndexAlgorithmObj[item.alarmIndexDesc] // 指标算法
      })
      this.alarmConfigList = data
    },
    handleQuery() {
      // 查询
      this.getAlarmConfig()
    },
    getAlarmConfig: debounce(async function () {
      this.loading = true
      const { ctType, dcCode, clusterCode, host } = this.filterCond
      const data = {
        lang,
        dcCode,
        clusterCode,
        host,
      }
      if (ctType !== 'All') {
        data.ctType = ctType
      }
      try {
        const { data: resData = [] } = await getMonitorConfList(data)
        this.loading = false
        if (resData) {
          // 遍历表格数据，生成邮件和短信接收人
          resData.forEach((item) => {
            const { alarmReceivers = [] } = item
            item.alarmEmail = alarmReceivers
              .map((item2) => item2.email)
              .filter((item) => item)
              .join(',')
            item.alarmSms = alarmReceivers
              .map((item2) => item2.sms)
              .filter((item) => item)
              .join(',')
          })
          // 遍历表格数据，计算每种采集项和采集子项的数量
          const collectCountObj = this.countCtType(resData)
          // 判断是否为全部开启状态
          this.toggleFlag = resData.every((item) => item.alarmEmailStatus === 2)
          this.toggleFlag2 = resData.every((item) => item.alarmSmsStatus === 2)
          // console.table(collectCountObj)
          // 根据采集项和指标的数量生成对应的rowSpan和cellSpan
          this.calculateRowSpan(collectCountObj, resData)
        } else {
          this.alarmConfigList = []
        }
      } catch (error) {
        this.loading = false
        console.error(error)
      }
    }, 500),
    configEdit(row) {
      // 编辑时找到对应采集项的所有数据
      const list =
        this.alarmConfigList.filter((item) => item.ctType === row.ctType) || []
      // 对alarmIndexType为Double和long的值进行处理，有值则变成数值，否则为undefined；
      const dealList = list.map((item) => {
        const temp = { ...item }
        if (
          temp.alarmIndexType === 'Double' ||
          temp.alarmIndexType === 'Long'
        ) {
          temp.threshold =
            temp.threshold === null ? undefined : Number(temp.threshold)
          temp.alarmThreshold =
            temp.alarmThreshold === null
              ? undefined
              : Number(temp.alarmThreshold)
        } else {
          // 对非数值的类型进行处理（选择框）
          temp.threshold = temp.threshold === null ? '' : temp.threshold
          temp.alarmThreshold =
            temp.alarmThreshold === null ? '' : temp.alarmThreshold
        }
        // 对collectTime、hourLimits、dayLimits进行处理，有值则变成数值，否则为undefined；
        const fieldArr = ['collectTime', 'hourLimits', 'dayLimits']
        fieldArr.forEach((key) => {
          temp[key] = temp[key] === null ? undefined : Number(temp[key])
        })
        return temp
      })
      if (dealList.length) {
        const {
          ctType,
          ctPeriod,
          // alarmEmail,
          // alarmSms,
          ctPeriodUnit,
          alarmReceivers,
        } = dealList[0]
        const editRecord = {
          ctType,
          ctPeriod,
          // alarmEmail,
          // alarmSms,
          alarmReceivers,
          editConfigs: dealList,
          ctPeriodUnit,
        }
        this.$refs.alarmConfigDialog.open(editRecord)
      }
    },
    // 单元格合并
    arraySpanMethod({ row, columnIndex }) {
      if (row) {
        if ([0, 9, 10, 11].includes(columnIndex)) {
          return row.firstColumnSpan || [0, 0]
        }
      } else {
        return [1, 1]
      }
    },
    // 保存配置
    async configSave(formData) {
      // console.log('表单数据', formData)
      // this.$TvtLoading.show({text: this.$t('LoadingMsg')})
      const {
        ctType,
        ctPeriod,
        // alarmEmail = null,
        // alarmSms = null,
        editConfigs = [],
        alarmReceivers = [],
      } = formData
      const list = editConfigs.map((item) => {
        const keyArr = [
          'alarmIndexKey',
          'threshold',
          'alarmThreshold',
          'collectTime',
          'hourLimits',
          'dayLimits',
        ]
        const temp = keyArr.reduce((pre, next) => {
          pre[next] = item[next]
          return pre
        }, {})
        return temp
      })
      // 过滤掉邮件接收人、短信接收人和语言都没有值的记录
      const list2 = alarmReceivers.filter(
        (item) => item.email || item.sms || item.lang
      )
      const { dcCode, clusterCode, host } = this.filterCond
      const data = {
        dcCode,
        clusterCode,
        host,
        ctType,
        ctPeriod,
        // alarmEmail,
        // alarmSms,
        editConfigs: list,
        alarmReceivers: list2,
      }
      try {
        await editMonitorConf(data)
        this.$refs.alarmConfigDialog.closeDialog()
        this.getAlarmConfig()
      } catch (error) {
        console.error(error)
      }
    },
    // 邮件/短信告警全部切换
    handleToggle: debounce(async function (prop) {
      const flag =
        prop === 'alarmEmailStatus' ? !this.toggleFlag : !this.toggleFlag2
      const { dcCode, clusterCode, host } = this.filterCond
      const params = {
        dcCode,
        clusterCode,
        host,
        [prop]: flag ? 2 : 1,
        editConfigs: [],
      }
      try {
        await editMonitorConf(params)
        this.$message.success(this.$t('editSuccess'))
        if (prop === 'alarmEmailStatus') {
          this.toggleFlag = flag
        } else {
          this.toggleFlag2 = flag
        }
        this.getAlarmConfig()
      } catch (error) {
        console.error(error)
      }
    }, 200),
    async handleSwitch(val, row, index, prop) {
      // console.log('row', row, 'flag', flag)
      // this.switchValue = val
      const status = val ? 2 : 1
      const { dcCode, clusterCode, host } = this.filterCond
      const keyArr = [
        'alarmIndexKey',
        'threshold',
        'alarmThreshold',
        'collectTime',
        'hourLimits',
        'dayLimits',
      ]
      const record = keyArr.reduce((pre, next) => {
        pre[next] = row[next]
        return pre
      }, {})
      const params = {
        dcCode,
        clusterCode,
        host,
        [prop]: status,
        editConfigs: [record],
      }
      try {
        await editMonitorConf(params)
        this.$message.success(this.$t('editSuccess'))
        // 直接修改当前记录的状态或者重新请求
        // this.getAlarmConfig()
        const alarmConfigList = this.alarmConfigList.slice()
        alarmConfigList[index][prop] = status
        this.alarmConfigList = alarmConfigList
        if (prop === 'alarmEmailStatus') {
          // 判断是否为全部开启状态
          this.toggleFlag = alarmConfigList.every(
            (item) => item.alarmEmailStatus === 2
          )
        } else {
          // 判断是否为全部开启状态
          this.toggleFlag2 = alarmConfigList.every(
            (item) => item.alarmSmsStatus === 2
          )
        }
      } catch (error) {
        console.error(error)
      }
    },
    handleReceivers() {
      this.$refs.alarmReceiverDialog.open()
    },
    // 批量修改邮件接收人
    async receiverSave(formData) {
      const { alarmReceivers } = formData
      try {
        await editMonitorConf({ alarmReceivers })
        this.$refs.alarmReceiverDialog.closeDialog()
        this.getAlarmConfig()
      } catch (error) {
        this.$refs.alarmReceiverDialog.clearParams()
        console.error(error)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.alarm-config {
  margin: 24px;
  width: calc(100% - 48px);
  .search-wrapper {
    width: 100%;
    margin: 20px 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    .search-left-wrapper {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
    }
    .search-right-wrapper {
      width: max-content;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
  }

  .btn-text {
    color: #429efd;
    cursor: pointer;

    + .btn-text {
      margin-left: 20px;
    }
  }

  .pre-detail {
    line-height: 24px;
    white-space: pre-line;
  }

  .no-detail {
    width: 600px;
    text-align: center;
  }

  .row-table-wrapper {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .tree-content {
    width: 220px;
    height: calc(100vh - 155px);
    overflow: auto;
    background: white;
    margin-right: 20px;
  }

  .table-content {
    flex: 1;
    height: 100%;
    overflow: hidden;
  }
}
</style>
<style>
.alarm-config .table-wrapper .el-table__body tr.hover-row > td.el-table__cell {
  background-color: unset !important;
}
.alarm-config
  .table-wrapper
  .el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell {
  background-color: unset !important;
}
.alarm-config .table-wrapper .el-table__body .cell {
  white-space: pre-line;
}

.switchStyle .el-switch__label {
  position: absolute;
  display: none;
  color: #fff;
}
.el-switch__core {
  background-color: rgba(166, 166, 166, 1);
}
.switchStyle .el-switch__label--left {
  z-index: 9;
  left: 20px;
}
.switchStyle .el-switch__label--right {
  z-index: 9;
  left: 4px;
}
.switchStyle .el-switch__label.is-active {
  display: block;
}
.switchStyle.el-switch .el-switch__core,
.el-switch .el-switch__label {
  width: 60px !important;
}
</style>
