{"name": "cloud-data-analysis", "version": "0.1.0", "private": true, "scripts": {"serve": "tvt serve build/webpack.dev.conf.js", "dev": "tvt serve build/webpack.dev.conf.js", "build": "tvt build build/webpack.prod.conf.js", "fix": "tvt lint", "prepare": "cd .. && husky install DevOpsWeb/.husky", "lint": "eslint --ext .js,.vue src --fix", "lint-staged": "lint-staged"}, "lint-staged": {"*.(js|vue)": ["eslint"]}, "dependencies": {"crypto-browserify": "^3.12.0", "echarts": "^5.4.3", "js-cookie": "^3.0.5", "moment": "^2.30.1", "stream-browserify": "^3.0.0", "tvtcloudbaiscwidget": "^2.0.24", "vue-dompurify-html": "^2.6.0", "vue-i18n": "^8.28.2", "vue-observe-visibility": "^1.0.0"}, "devDependencies": {"babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.8", "tvtclouddevdependency": "^1.0.22", "vuex-persistedstate": "^3.1.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}