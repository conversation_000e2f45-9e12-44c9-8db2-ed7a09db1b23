<template>
  <!-- 公共布局组件：包含左侧和右侧两部分，右侧可伸缩，左侧宽度为弹性 -->
  <div class="layout-wrapper">
    <div class="layout-search-wrapper">
      <slot name="search"> 搜索框 </slot>
    </div>
    <div class="layout-content-wrapper">
      <div class="layout-left-wrapper">
        <div class="left-content-wrapper">
          <slot name="left-content"> 内容区域 </slot>
        </div>
      </div>
      <div
        :class="[
          'layout-right-wrapper',
          collapse ? 'layout-right-collapsed' : '',
        ]"
      >
        <div
          class="layout-right-collapse"
          :style="{ left: collapse ? '-20px' : '-10px' }"
          @click="changeCollapse"
        >
          <i
            v-if="collapse"
            class="el-icon-caret-left"
            style="font-size: 24px"
          />
          <i v-else class="el-icon-caret-right" style="font-size: 24px" />
        </div>
        <div class="right-content-wrapper">
          <slot name="right-content"> 右侧表格 </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommonLayout',
  props: {},
  data() {
    return {
      collapse: false,
    }
  },
  methods: {
    changeCollapse() {
      this.collapse = !this.collapse
    },
  },
}
</script>

<style lang="scss" scoped>
.layout-wrapper {
  width: 100%;
  height: 100%;
  // height: calc(100vh - 94px);
  overflow-x: hidden;
  overflow-y: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

.layout-search-wrapper {
  width: 100%;
  // min-height: 54px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.layout-content-wrapper {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  padding-top: 20px;
}

.layout-left-wrapper {
  height: 100%;
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 10px;
  overflow-x: hidden;
}

.layout-right-wrapper {
  // flex: 0 0 500px;
  width: 500px;
  height: 100%;
  position: relative;
  transition: width 1s;
  .layout-right-collapse {
    position: absolute;
    top: 50%;
    left: 0px;
    transform: translateY(-50%);
    z-index: 999;
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
}
.layout-right-wrapper ::-webkit-scrollbar {
  /* 设置滚动条的样式 */
  display: none;
}
.layout-right-collapsed {
  // flex: 0 0 0px;
  width: 0.5px;
}

.left-content-wrapper {
  width: 100%;
  height: calc(100%);
}

.right-content-wrapper {
  width: 100%;
  height: calc(100%);
}
</style>
