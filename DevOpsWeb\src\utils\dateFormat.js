// 个位数补零
const addZero = (str) => {
  return str.toString().length < 2 ? `0${str}` : str
}
/**
 * 格式化date对象
 * @param {Object} dateObj: 日期对象
 * @param {String} format: 格式化的规则
 */
export const formatDate = (dateObj, format = 'yyyy-mm-dd hh:mm:ss') => {
  if (!dateObj) return
  let year = dateObj.getFullYear()
  let month = addZero(dateObj.getMonth() + 1)
  let day = addZero(dateObj.getDate())
  let hour = addZero(dateObj.getHours())
  let minute = addZero(dateObj.getMinutes())
  let second = addZero(dateObj.getSeconds())
  switch (format) {
    case 'yyyy-mm-dd hh:mm:ss':
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`
    case 'yyyy/mm/dd hh:mm:ss':
      return `${year}/${month}/${day} ${hour}:${minute}:${second}`
    case 'yyyy-mm-dd':
      return `${year}-${month}-${day}`
    case 'yyyy/mm/dd':
      return `${year}/${month}/${day}`
    case 'mm-dd':
      return `${month}-${day}`
    case 'yyyy年mm月dd日':
      return `${year}年${month}月${day}日`
    case 'hh:mm':
      return `${hour}:${minute}`
  }
}

// 获取初始化日期
export const getInitDate = () => {
  const date = new Date()
  date.setTime(date.getTime() - 3600 * 1000 * 24)
  return [date, date]
}
