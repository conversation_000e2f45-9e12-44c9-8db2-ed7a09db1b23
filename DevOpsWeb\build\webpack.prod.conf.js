const webpackConfig = require('tvtclouddevdependency')
const { alias, cssLoader } = require('./utils.js')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const webpack = require('webpack')
const CompressionPlugin = require('compression-webpack-plugin')
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin

// 获取基础配置
const baseConfig = webpackConfig.prod

// 替换原有的 MiniCssExtractPlugin
const plugins = baseConfig.plugins.filter(
  (plugin) => !(plugin instanceof MiniCssExtractPlugin)
)
plugins.push(
  new MiniCssExtractPlugin({
    filename: `css/[name].[contenthash:8].css`,
    chunkFilename: `css/[name].[contenthash:8].chunk.css`,
  })
)

const prodWebpackConfig = {
  output: {
    publicPath: process.env.NODE_ENV === 'development' ? '/' : '/devops/',
    // 输出重构  打包编译后的 文件名称  【模块名称.版本号.js】
    filename: `js/[name].[contenthash:8].js`,
    chunkFilename: `js/[name].[contenthash:8].chunk.js`,
    // 缓存配置
    assetModuleFilename: 'assets/[name].[contenthash:8][ext]',
  },
  resolve: {
    alias,
  },
  plugins: [
    // 此配置会导致路由懒加载组件失效，如果路由懒加载组件超过这个数量，webpack 会强制合并一些 chunks
    // 需要根据项目实际情况调整 -- 目前调整成24
    // 路由懒加载组件中，将相同模块下的文件打包成同一个chunk
    new webpack.optimize.LimitChunkCountPlugin({
      maxChunks: 24, // 来限制 chunk 的最大数量，避免出现太多小的chunk
    }),
    new CompressionPlugin({
      test: /\.(js|css|html|svg)$/,
      threshold: 1024 * 400, // 只压缩大于 400KB 的文件
      minRatio: 0.8, // 只有压缩率小于这个值的资源才会被处理
      exclude: /\.(woff2?|eot|ttf|otf)$/, // 排除字体文件
    }),
    // // 添加打包分析插件
    // new BundleAnalyzerPlugin({
    //   analyzerMode: 'server',
    //   analyzerHost: '127.0.0.1',
    //   analyzerPort: 8888,
    //   reportFilename: 'report.html',
    //   defaultSizes: 'parsed',
    //   openAnalyzer: true,
    //   generateStatsFile: false,
    //   statsFilename: 'stats.json',
    //   statsOptions: null,
    //   logLevel: 'info'
    // }),
  ],
  optimization: {
    // runtimeChunk: 'single',
    // 使用 deterministic 模式 确保相同的模块在不同构建中获得相同的 ID  提高长期缓存的效率
    moduleIds: 'deterministic', // 确保模块 ID 稳定
    chunkIds: 'deterministic', // 确保 chunk ID 稳定
    // 抽离 runtime 代码  将 webpack 的运行时代码单独打包  避免因为添加新模块导致主包哈希值变化
    runtimeChunk: {
      name: 'runtime', // 将 runtime 代码抽离为单独的 chunk
    },
    splitChunks: {
      chunks: 'all',
      maxInitialRequests: 10, // 指定最多可以拆分为多少个同步加载的新区块
      maxAsyncRequests: 10, // 指定最多可以拆分为多少个异步加载的新区块
      maxSize: 1024 * 1024, // 目标文件超过maxSize时才会分包
      minSize: 50 * 1024, // 最小 chunk 体积为 50KB
      cacheGroups: {
        tvtcloudbaiscwidget: {
          name: 'vender-widget',
          test: /[\\/]node_modules[\\/]tvtcloudbaiscwidget[\\/]/,
          priority: 20, // 优先级提高，确保单独打包
          chunks: 'all',
          enforce: true,
        },
        vendors: {
          test: /[\\/]node_modules[\\/]/, // 使用正则匹配node_modules中引入的模块
          priority: -10, // 优先级，值越大优先级越高,默认-10
          name(module) {
            // 设定分包以后的文件模块名字，按照包名字替换拼接下
            if (!module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/))
              return
            const packageName = module.context.match(
              /[\\/]node_modules[\\/](.*?)([\\/]|$)/
            )[1]
            return `npm.${packageName.replace('@', '')}`
          },
        },
      },
    },
  },
  module: {
    rules: [cssLoader],
  },
}

// 直接设置处理后的插件列表
baseConfig.plugins = plugins

webpackConfig.mergeProd(prodWebpackConfig)

// // 用于调试确认最终配置
// console.log('Final plugins:', webpackConfig.prod.plugins)

module.exports = webpackConfig.prod
