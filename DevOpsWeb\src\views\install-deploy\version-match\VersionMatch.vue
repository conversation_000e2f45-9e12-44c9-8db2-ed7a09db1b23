<template>
  <div>
    <div class="version-match">
      <div class="search-wrapper">
        <div class="search-left-wrapper">
          <tvt-select
            v-model="filterCond.packageTag"
            :placeholder="$t('versionTab.packageTagInput')"
            clearable
            style="margin-right: 20px"
            @change="getOrderList(1)"
          >
            <el-option
              v-for="(item, index) in packageTagList"
              :key="index"
              :label="item"
              :value="item"
            />
          </tvt-select>
          <search-input
            v-model="filterCond.projectName"
            :placeholder="$t('versionTab.packageNameInput')"
            clearable
            maxlength="32"
            style="width: 250px; margin-right: 20px"
            @change="getOrderList(1)"
          />
          <!-- 创建版本配套表只在RDC中才根据权限显示，DDC直接不显示 -->
          <el-button
            v-if="$isAuthorityExist(['devops_deploy_versionMatch_mgr_create'])"
            type="primary"
            size="small"
            round
            @click="archivePKG"
          >
            {{ $t('versionTab.archivePKG') }}
          </el-button>
        </div>
      </div>
      <!-- 表格 -->
      <div class="table-wrapper">
        <tvt-table-pro
          v-loading="loading"
          :data="orderList"
          stripe
          row-key="id"
          :expand-row-keys="expandRowKeys"
          :border="true"
          :border-bottom="true"
          :primary="{
            pagination: {
              total,
              current: filterCond.current,
              size: filterCond.size,
              'page-sizes': $tablePageSizes,
              background: true,
            },
            columns,
            customClass: 'test',
          }"
          :second="{
            pagination: false,
            columns: subColumns,
            'max-height': 500,
          }"
          @onFetchData="getOrderList"
        >
          <template #bodyCell="{ row, column }">
            <template v-if="column.prop === 'createTime'">
              <span>{{ stampToStrLongMethod(row.createTime) }}</span>
            </template>
            <template v-if="column.prop === 'serviceType'">
              <span>{{
                serviceTypeObj[row.serviceType] || row.serviceType
              }}</span>
            </template>
            <template v-if="column.key === 'operation'">
              <span class="btn-text" @click="versionDelete(row)">{{
                $t('delete')
              }}</span>
            </template>
          </template>
          <template #start="{ row }">
            <span>{{ stampToStrLongMethod(row.createTime) }}</span>
          </template>
        </tvt-table-pro>
      </div>
      <archive-pkg ref="archivePkg" @btnSave="archivePkgSuccess" />
    </div>
    <tvt-dialog
      :title="$t('detail')"
      :show.sync="showDetail"
      width="1300px"
      :cancel-show="false"
      :submit-text="$t('close')"
      :modal-append-to-body="false"
      @close="closeDetail"
      @Submit="closeDetail"
    >
      <tvt-table
        :data="detailTableData"
        :columns="detailColumns"
        :border="true"
        :border-bottom="true"
        :height="600"
        style="width: 100%"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.prop === 'createTime'">
            <span>{{ stampToStrLongMethod(row.createTime) }}</span>
          </template>
        </template>
      </tvt-table>
    </tvt-dialog>
    <tip-dialog
      ref="tipDialogRef"
      title="configTab.info"
      :tip-msg="$t('configTab.delete')"
      @onClose="handleClose"
      @submit="handleDelete"
    />
  </div>
</template>

<script>
import {
  getPackageTagList,
  deleteVersionMatch,
  getVersionMatchList,
  getPackageDetail,
} from '@/api/installDeploy.js'
import { debounce, addObjKey, stampToStrLong } from '@/utils/common'
import ArchivePkg from '@/views/install-deploy/version-match/ArchivePkg'
import TipDialog from '@/components/common/TipDialog.vue'
import { mapState } from 'vuex'
export default {
  name: 'VersionMatch',
  components: {
    ArchivePkg,
    TipDialog,
  },
  data() {
    return {
      orderList: [],
      loading: false,
      packageTagList: [],
      columns: [
        { type: 'expand', slotName: 'expand', align: 'left' },
        { label: this.$t('index'), type: 'index', width: 120 },
        { label: this.$t('versionTab.packageTag'), prop: 'packageTag' },
        {
          label: this.$t('deployPlanTab.remark'),
          prop: 'deployRemark',
          width: 150,
        },
        {
          label: this.$t('versionTab.createTime'),
          prop: 'createTime',
          slotName: 'bodyCell',
          width: 400,
        },
        {
          label: this.$t('versionTab.serviceType'),
          prop: 'serviceType',
          slotName: 'bodyCell',
          width: 400,
        },
        {
          label: this.$t('configTab.operation'),
          key: 'operation',
          slotName: 'bodyCell',
          width: 180,
          hide: !this.$isAuthorityExist([
            'devops_deploy_versionMatch_mgr_delete',
          ]),
        },
      ],
      subColumns: [
        { label: this.$t('index'), type: 'index', width: 60 },
        {
          label: this.$t('versionTab.packageTag'),
          prop: 'packageTag',
          width: 160,
        },
        {
          label: this.$t('versionTab.packageName'),
          prop: 'packageName',
          minWidth: 200,
        },
        {
          label: this.$t('versionTab.ossPath'),
          prop: 'ossPath',
          minWidth: 440,
        },
        {
          label: this.$t('versionTab.createTime'),
          prop: 'createTime',
          slotName: 'start',
          width: 180,
        },
        {
          label: this.$t('deployPlanTab.remark'),
          prop: 'deployRemark',
          width: 150,
        },
      ],
      filterCond: {
        current: 1,
        size: 20,
        packageTag: '',
        projectName: '',
      },
      total: 0,
      showDetail: false,
      detailTableData: [], // 详情中的表格
      versionRecord: null,
      detailColumns: [
        { label: this.$t('index'), type: 'index', width: 60 },
        {
          label: this.$t('versionTab.packageTag'),
          prop: 'packageTag',
          width: 160,
        },
        {
          label: this.$t('versionTab.projectName'),
          prop: 'projectName',
          width: 240,
        },
        {
          label: this.$t('versionTab.packageName'),
          prop: 'packageName',
          width: 340,
        },
        { label: this.$t('versionTab.version'), prop: 'version', width: 120 },
        {
          label: this.$t('versionTab.ossPath'),
          prop: 'ossPath',
          'min-width': 200,
        },
        {
          label: this.$t('versionTab.createTime'),
          prop: 'createTime',
          slotName: 'bodyCell',
          fixed: 'right',
          width: 100,
        },
      ],
      expandRowKeys: [], // 展开的行
      serviceTypeObj: {
        java: this.$t('versionTab.businessService'),
        cpp: this.$t('versionTab.cppService'),
        p2pweb: this.$t('versionTab.p2pwebService'),
      },
    }
  },
  computed: {
    ...mapState('params', ['envDcInfo']),
  },
  mounted() {
    this.packageTagListOption()
    this.getOrderList(1)
  },
  methods: {
    packageTagListOption(data = []) {
      // 获取配套表名称
      getPackageTagList(data)
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            this.packageTagList = res.data
          } else {
            this.packageTagList = []
          }
        })
        .catch((err) => {
          this.packageTagList = []
          console.log(err)
        })
    },
    stampToStrLongMethod(time) {
      // 时间格式化
      let intTime = parseInt(time)
      return stampToStrLong(intTime)
    },
    getOrderList: debounce(function (pageSize) {
      // 分页获取版本配套列表
      this.loading = true
      if (pageSize == 1) {
        this.filterCond.current = 1
      } else if (pageSize && pageSize.current) {
        this.filterCond.current = pageSize.current
        this.filterCond.size = pageSize.size
      }
      let data = addObjKey(this.filterCond)
      data.pageNum = this.filterCond.current
      data.pageSize = this.filterCond.size
      delete data['current']
      delete data['size']
      getVersionMatchList(data)
        .then((res) => {
          this.loading = false
          if (res.basic && res.basic.code == 200) {
            if (res.data) {
              const list = res.data.records.map((o, index) => ({
                // ...o, // 不能把children传过去
                createTime: o.createTime,
                packageTag: o.packageTag,
                serviceType: o.serviceType,
                id: o.packageTag + '-1-' + index,
                deployRemark: o.deployRemark,
                subList: o.children || [],
              }))
              this.orderList = list
              this.total = parseInt(res.data.total)
              if (data.packageTag || data.projectName) {
                // 选择配套表/输入项目名进行搜索，默认展开所有行
                this.expandRowKeys = this.orderList.map(
                  (item, index) => item.packageTag + '-1-' + index
                )
              } else {
                // 否则关闭所有展开行
                this.expandRowKeys = []
              }
            } else {
              this.orderList = []
            }
          }
        })
        .catch((err) => {
          this.loading = false
          console.log(err)
        })
    }, 500),
    archivePKG() {
      this.$refs.archivePkg.open()
    },
    archivePkgSuccess() {
      // 归档后查询条件都清空
      this.filterCond.packageTag = ''
      this.filterCond.projectName = ''
      this.packageTagListOption([])
      this.getOrderList(1)
    },
    packageDetailList(packageTag) {
      getPackageDetail(packageTag)
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            this.detailTableData = res.data || []
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 查看详情
    versionDetial(row) {
      this.showDetail = true
      // 获取详情表格
      const { packageTag } = row
      this.packageDetailList(packageTag)
    },
    closeDetail() {
      this.showDetail = false
      this.versionRecord = null
      this.detailTableData = []
    },
    // 单数据删除
    versionDelete(row) {
      this.versionRecord = row
      this.$refs.tipDialogRef.open()
    },
    handleDelete() {
      const { packageTag } = this.versionRecord
      this.deleteVersionMatch(packageTag)
    },
    handleClose() {
      this.versionRecord = null
    },
    // 删除配套表
    deleteVersionMatch(packageTag) {
      const data = [packageTag]
      deleteVersionMatch(data)
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            this.$message.success(this.$t('deleteSuccess'))
            this.getOrderList(1) // 把页面归到第一页
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
  },
}
</script>
<style lang="scss" scoped>
.version-match {
  margin: 24px;
  width: calc(100% - 48px);
  .btn-text {
    color: #429efd !important;
    cursor: pointer;
    + .btn-text {
      margin-left: 20px;
    }
  }
  .search-wrapper {
    width: 100%;
    margin: 20px 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    .search-left-wrapper {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
    }
    .search-right-wrapper {
      width: max-content;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
    .upload-div {
      margin-right: 10px;
    }
  }
}
.tip-box {
  min-height: 120px;
  display: flex;
  align-items: center;
  font-size: 16px;
}
</style>
<style>
.version-match .search-wrapper .el-input__inner {
  height: 32px;
  line-height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 16px;
}
.version-match .search-wrapper .el-input__icon {
  line-height: 34px;
}
.version-match .search-wrapper .tvt-input .tvt-field-set {
  border: none;
}
.version-match .search-wrapper .tvt-select .tvt-field-set {
  border: none;
}
.version-match .search-wrapper .upload-div .el-button--small {
  height: 34px;
}
</style>
