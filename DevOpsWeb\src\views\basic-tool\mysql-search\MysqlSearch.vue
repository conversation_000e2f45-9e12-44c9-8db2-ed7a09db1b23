<template>
  <!-- MySQL搜索 -->
  <div class="page-wrapper">
    <div class="search-wrapper">
      <tvt-select
        v-model="filterCond.projectName"
        :options="microTypeOptions"
        :mockplaceholder="$t('microLogTab.microSelect')"
        :clearable="false"
        style="margin-right: 20px"
      />
      <el-input
        v-model="filterCond.sql"
        :placeholder="$t('mysqlSearchTab.customMysqlInput')"
        type="textarea"
        rows="1"
        class="custom-mysql-input"
        style="margin-right: 20px"
      >
      </el-input>
      <div class="search-btn-box">
        <el-button type="primary" size="small" round @click="handleReset">{{
          $t('reset')
        }}</el-button>
        <el-button type="primary" size="small" round @click="handleSearch">{{
          $t('search')
        }}</el-button>
      </div>
      <div class="search-left-wrapper"></div>
    </div>
    <div class="quota-wrapper">
      <div class="quota-line-wrapper">
        <div class="quota-label">{{ $t('mysqlSearchTab.searchStatus') }}:</div>
        <div class="quota-value" style="min-width: 60px">
          {{ searchResult.statusLabel || '' }}
        </div>
        <div class="quota-label">{{ $t('mysqlSearchTab.costTime') }}(ms):</div>
        <div class="quota-value" style="min-width: 60px">
          {{ searchResult.costTime || '' }}
        </div>
        <div class="quota-label">{{ $t('mysqlSearchTab.errorMessage') }}:</div>
        <div class="quota-value text-over-ellipsis" style="min-width: 60px">
          {{ searchResult.msg || '' }}
        </div>
      </div>
    </div>
    <!-- MySQL日志表格 -->
    <div class="table-wrapper">
      <tvt-table
        ref="myTable"
        v-myLoading="loading"
        :data="mysqlLogList"
        :columns="mysqlLogColumn"
        :border="true"
        :border-bottom="true"
        :pagination="false"
        @onFetchData="getMysqlLogList"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.prop === 'executeTime'">
            <span>{{ stampToStrLongMethod(row[column.prop]) }}</span>
          </template>
          <template v-if="column.prop === 'sql'">
            <el-tooltip
              class="item"
              effect="dark"
              :content="row[column.prop]"
              placement="left-start"
            >
              <span class="tooltip-ellipsis-box">{{ row[column.prop] }}</span>
            </el-tooltip>
          </template>
        </template>
      </tvt-table>
    </div>
  </div>
</template>

<script>
import { debounce, stampToStrLong } from '@/utils/common'
import { getServiceList, getMysqlQuery } from '@/api/basicTool.js'

export default {
  name: 'MysqlSearch',
  data() {
    return {
      filterCond: {
        projectName: '',
        sql: '',
      },
      loading: false,
      mysqlTotal: 0,
      microTypeOptions: [],
      searchResult: {},
      mysqlLogList: [],
      mysqlLogColumn: [],
    }
  },
  async mounted() {
    await this.getServiceList()
  },
  methods: {
    stampToStrLongMethod(time) {
      let intTime = parseInt(time)
      return stampToStrLong(intTime)
    },
    // 获取微服务列表
    async getServiceList() {
      try {
        // 1:用于微服务日志查询 2:用于resultful接口日志查询 4:用于mysql查询查询
        const { data = [] } = await getServiceList(4)
        this.microTypeOptions = data.map((item) => ({
          label: item,
          value: item,
        }))
        // 微服务列表默认选中第一个
        this.filterCond.projectName = data[0]
      } catch (error) {
        console.error(error)
      }
    },
    // 获取MySQL日志列表
    getMysqlLogList: debounce(async function () {
      const { projectName, sql } = this.filterCond
      // 校验自定义mysql
      if (!sql || sql.trim() === '') {
        this.$message.error(this.$t('mysqlSearchTab.customMysqlInput'))
        return
      }
      this.loading = true
      try {
        const { data } = await getMysqlQuery({ projectName, sql: sql.trim() })
        const { status, msg, costTime, data: tableData, fields } = data
        this.mysqlLogList = tableData || []
        this.mysqlLogColumn = (fields || []).map((item) => ({
          label: item,
          prop: item,
          minWidth: 100,
        }))
        this.searchResult = {
          status,
          statusLabel:
            status === 'success' ? this.$t('success') : this.$t('fail'),
          msg,
          costTime,
        }
        this.loading = false
      } catch (error) {
        console.error(error)
        this.loading = false
      }
    }, 500),
    handleReset() {
      this.filterCond = {
        projectName: this.microTypeOptions[0]?.value || '',
        sql: '',
      }
      this.searchResult = {}
      this.mysqlLogList = []
      this.mysqlLogColumn = []
    },
    handleSearch() {
      this.getMysqlLogList()
    },
  },
}
</script>

<style lang="scss" scoped>
.tooltip-ellipsis-box {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.quota-wrapper {
  font-size: 14px;
  background: white;
  padding: 5px 10px;
  border-radius: 5px;
  margin: 0px 24px 20px 24px;
  box-sizing: border-box;
  height: 54px;
  display: flex;
  align-items: center;
  .quota-line-wrapper {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    line-height: 20px;
    .quota-label {
      margin-right: 10px;
      white-space: nowrap;
    }
    .quota-value {
      margin-right: 20px;
    }
    .text-over-ellipsis2 {
      overflow: hidden; // 超出的文本隐藏
      text-overflow: ellipsis; // 溢出用省略号显示
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
  }
}
.custom-mysql-input {
  display: inline-block;
  width: 400px;
  border-radius: 16px;
  ::v-deep .el-textarea__inner {
    border-radius: 16px;
  }
}
</style>
