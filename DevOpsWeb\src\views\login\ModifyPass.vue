<!--修改密码-->
<template>
  <div class="password-box">
    <tvt-dialog
      :title="$t('login.modifyPass')"
      :show.sync="visable"
      width="570px"
      :modal-append-to-body="false"
      :append-to-body="true"
      :cancel-text="$t('cancel')"
      :submit-text="$t('confirm')"
      :close-on-click-modal="false"
      @close="close"
      @Cancel="close"
      @Submit="Submit"
    >
      <div class="password-msg">
        <el-form ref="pwdForm" :model="form">
          <el-form-item
            prop="oldPassword"
            :rules="[
              {
                required: true,
                message: $t('login.noOldPwd'),
                trigger: 'blur',
              },
            ]"
          >
            <tvt-input
              v-model.trim="form.oldPassword"
              type="password"
              :is-sensitive="true"
              required
              :label="$t('login.oldPassword')"
              style="width: 360px; margin-top: 10px"
              maxlength="32"
            />
          </el-form-item>
          <el-form-item
            prop="newPassword"
            :rules="[
              {
                required: true,
                message: $t('login.noNewpwd'),
                trigger: 'blur',
              },
              {
                validator: pwdValidator,
                message: $t('login.pwdFormUncorrect'),
                trigger: 'blur',
              },
              {
                validator: validatePass,
                message: $t('login.pwdDifferent'),
                trigger: 'blur',
              },
            ]"
          >
            <tvt-input
              v-model.trim="form.newPassword"
              type="password"
              :is-sensitive="true"
              required
              :label="$t('login.newPassword')"
              style="width: 360px; margin-top: 10px"
              maxlength="32"
              @blur="checkPwdSame"
            />
          </el-form-item>
          <el-form-item
            prop="confirmPwd"
            :rules="[
              {
                required: true,
                message: $t('login.noConfirmPwd'),
                trigger: 'blur',
              },
              {
                validator: pwdValidator,
                message: $t('login.pwdFormUncorrect'),
                trigger: 'blur',
              },
              {
                validator: validatePass,
                message: $t('login.pwdDifferent'),
                trigger: 'blur',
              },
            ]"
          >
            <tvt-input
              v-model.trim="form.confirmPwd"
              type="password"
              :is-sensitive="true"
              required
              :label="$t('login.confirmPassword')"
              style="width: 360px; margin-top: 10px"
              maxlength="32"
            />
          </el-form-item>
        </el-form>
      </div>
    </tvt-dialog>
  </div>
</template>

<script>
import { checkPwdStrongStrength } from '@/utils/common'
import { devopsModifyPass } from '@/api/user'
import MD5 from 'js-md5'
import selfLocaLStorage from '@/utils/selfLocalStorage'

export default {
  name: 'ChangePassword',
  components: {},
  data() {
    return {
      visable: false,
      form: {
        oldPassword: '',
        newPassword: '',
        confirmPwd: '', //确认密码
      },
    }
  },
  mounted() {},
  methods: {
    // 关闭
    close() {
      this.resetFields()
      this.visable = false
    },
    open() {
      this.visable = true
    },
    // 校验密码格式
    pwdValidator({ message }, value, callback) {
      if (!checkPwdStrongStrength(value)) {
        callback(new Error(message))
      } else {
        callback()
      }
    },
    // 判断两次输入密码是否一致
    validatePass({ message }, value, callback) {
      if (value !== this.form.newPassword) {
        callback(new Error(message))
      } else {
        callback()
      }
    },
    checkPwdSame() {
      if (this.form.newPassword && this.form.confirmPwd) {
        this.$refs['pwdForm'].validateField('confirmPwd', (valid) => {
          console.log(valid)
        })
      }
    },
    // 确定
    Submit() {
      this.validataPwdForm()
    },

    // 检验表单数据
    validataPwdForm() {
      this.$refs['pwdForm'].validate((valid) => {
        // 密码加密处理
        if (!valid) {
          return
        }
        let paramData = {
          devopsUser: selfLocaLStorage.getItem('userName'),
          oldDevopsPwd: MD5(this.form.oldPassword),
          newDevopsPwd: MD5(this.form.newPassword),
        }
        devopsModifyPass(paramData).then((res) => {
          if (res.basic && res.basic.code == 200) {
            // 将envHomeQuota额外保存下载，其余的清除--特殊要求
            const treeQuota = selfLocaLStorage.getItem('envHomeQuota') || null
            selfLocaLStorage.clear()
            sessionStorage.clear()
            // 将treeQuotq暂存下来
            if (treeQuota) {
              selfLocaLStorage.setItem('envHomeQuota', treeQuota)
            }
            this.$router.push('/login')
          } else {
            this.$message.error(res.basic.msg)
          }
        })
      })
    },
    // 重置表单 清除校验
    resetFields() {
      this.$refs.pwdForm.resetFields()
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__headerbtn {
  position: absolute;
  top: 17px;
  right: 20px;
}

::v-deep .el-dialog .el-dialog__body {
  min-height: 240px;
  align-items: flex-start;
  margin-top: 20px;
}

::v-deep .el-form-item__content {
  margin-bottom: 12px;
}

.password-msg {
  .password-title {
    font-weight: 600;
    color: #909399;
  }
}

.newpwd-tips {
  width: 357px;
  line-height: 12px;
  font-size: 12px;
  color: #909399;
  padding-top: 2px;
}
</style>
