<template>
  <!-- 告警信息表格组件 -->
  <div class="warn-wrapper">
    <div class="warn-table-title">
      {{ title }}
    </div>
    <div class="warn-table-wrapper">
      <tvt-table
        v-myLoading="loading"
        :data="warnTableDatas"
        :columns="warnColumns"
        :border="true"
        :border-bottom="true"
        :pagination="{
          total,
          current: pageNum,
          size: pageSize,
          'page-sizes': $tablePageSizes,
          background: true,
        }"
        style="width: 100%"
        @onFetchData="getWarnTableDatas"
      >
        <!-- <tvt-table
        :data="warnTableDatas"
        :columns="warnColumns"
        v-myLoading="loading"
        :border="true"
        :borderBottom="true"
        @onFetchData='getWarnTableDatas'
        style="width: 100%"
      > -->
      </tvt-table>
    </div>
  </div>
</template>

<script>
import { getMonitorHisList } from '@/api/systemAlarm.js'
import { getLocale } from '@/lang'
import { debounce } from '@/utils/common'
import { fliterCondObj } from '@/views/system-monitor/config.js'
export default {
  name: 'WarnTable',
  props: {},
  data() {
    return {
      title: this.$t('warnTab.title'),
      warnTableDatas: [],
      loading: false,
      warnColumns: [
        { label: this.$t('warnTab.ip'), prop: 'ip', width: 110 },
        { label: this.$t('warnTab.host'), prop: 'host', width: 100 },
        { label: this.$t('warnTab.ct'), prop: 'ct', width: 150 },
        { label: this.$t('warnTab.msg'), prop: 'msg', width: 140 },
      ],
      pageNum: 1,
      pageSize: 10,
      monitorType: null,
      total: 0,
    }
  },
  created() {
    // console.log('router', this.$route.name)
    const { name } = this.$route
    // 根据name确定filterCond和preFilterCond
    const searchCond = fliterCondObj[name] || {}
    const { monitorType = 'monitor.host' } = searchCond
    console.log('monitorType', monitorType)
    const newMonitorType = monitorType.replace('\.', '_') // 告警历史数据中monitorType是用下划线形式的，所以需要将‘.’转换成‘_’
    this.monitorType = newMonitorType
    this.$nextTick(() => {
      this.getWarnTableDatas()
    })
  },
  mounted() {
    this.$EventBus.$on('monSearchCond', (val) => {
      if (val) {
        console.log('父组件发来的查询数据', val) // 查询父组件发来的查询数据
        const { monitorType } = val
        const newMonitorType = monitorType.replace('\.', '_') // 告警历史数据中monitorType是用下划线形式的，所以需要将‘.’转换成‘_’
        this.monitorType = newMonitorType
        this.pageNum = 1
        this.$nextTick(() => {
          this.getWarnTableDatas()
        })
      }
    })
  },
  beforeDestroy() {
    this.$EventBus.$off('monSearchCond')
  },
  methods: {
    // 分页获取告警信息列表
    getWarnTableDatas: debounce(function (pageSize) {
      this.loading = true
      let curPageNum = this.pageNum
      let curPageSize = this.pageSize
      if (pageSize === 1) {
        curPageNum = 1
      } else if (pageSize && pageSize.current) {
        curPageNum = pageSize.current
        curPageSize = pageSize.size
      }
      const data = {
        monitorType: this.monitorType,
        pageNum: curPageNum,
        pageSize: curPageSize,
        lang: getLocale(),
      }
      this.pageNum = curPageNum
      this.pageSize = curPageSize
      this.loading = false
      getMonitorHisList(data)
        .then((res) => {
          this.loading = false
          if (res.basic && res.basic.code === 200) {
            if (res.data) {
              this.warnTableDatas = res.data.records
              this.total = parseInt(res.data.total)
            } else {
              this.warnTableDatas = []
            }
          } else {
            this.$message.error(res.basic.msg)
          }
        })
        .catch((err) => {
          this.loading = false
          console.log(err)
        })
    }, 500),
  },
}
</script>

<style lang="scss" scoped>
.warn-wrapper {
  width: 100%;
  height: 100%;
  .warn-table-title {
    width: 100%;
    height: 30px;
    line-height: 30px;
    font-size: 24px;
    margin-bottom: 20px;
    text-align: center;
    min-width: 100px;
    overflow-x: hidden;
  }
  .warn-table-wrapper {
    width: 100%;
    height: calc(100% - 50px);
  }
}
</style>
