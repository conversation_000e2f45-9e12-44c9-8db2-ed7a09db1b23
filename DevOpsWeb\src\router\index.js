import Vue from 'vue'
import Router from 'vue-router'
Vue.use(Router)

// 重写路由方法
const originPrototype = Router.prototype
const originPush = originPrototype.push
const originReplace = originPrototype.replace

// 重写 push 方法
Router.prototype.push = function (location, onComplete, onAbort) {
  if (onComplete || onAbort) {
    return originPush.call(this, location, onComplete, onAbort)
  }
  return originPush.call(this, location).catch((err) => err)
}

// 重写 replace 方法
Router.prototype.replace = function (location, onComplete, onAbort) {
  if (onComplete || onAbort) {
    return originReplace.call(this, location, onComplete, onAbort)
  }
  return originReplace.call(this, location).catch((err) => err)
}

import Layout from '@/layout/Layout.vue'
export const constantRoutes = [
  {
    path: '/login',
    component: () =>
      import(/* webpackChunkName: "login" */ '@/views/login/Login.vue'),
    hidden: true,
  },
  {
    path: '/main',
    name: 'main',
    component: Layout,
    redirect: '/main/install/resourceConfiguration',
    meta: {
      title: 'main',
      icon: 'main',
      affix: true,
      // pageAuthList: ['devops_monitor_qry'],
    },
    show: true,
    children: [
      {
        path: '/main/home',
        name: 'home',
        component: () =>
          import(
            /* webpackChunkName: "home" */
            /* webpackPrefetch: true */
            '@/views/home/<USER>'
          ),
        meta: {
          title: 'home',
          icon: 'home',
          affix: true,
          pageAuthList: ['devops_main_home_qry'],
        },
        show: true,
      },
      {
        path: '/main/install',
        component: () =>
          import(
            /* webpackChunkName: "installDeploy" */ '@/views/install-deploy/InstallDeploy'
          ),
        name: 'installDeploy',
        meta: {
          title: 'installDeploy',
          icon: 'el-icon-setting',
          affix: true,
          pageAuthList: [
            'devops_deploy_deploy_qry',
            'devops_deploy_resourcePlan_qry',
            'devops_deploy_versionMatch_qry',
            'devops_deploy_datasourceConfig_qry',
          ],
        },
        show: true,
        redirect: '/main/install/deploymentPlan',
        children: [
          {
            path: '/main/install/deploymentPlan',
            component: () =>
              import(
                /* webpackChunkName: "installDeploy" */
                /* webpackPrefetch: true */
                '@/views/install-deploy/deployment-plan/DeploymentPlan.vue'
              ),
            name: 'deploymentPlan',
            meta: {
              title: 'deploymentPlan',
              icon: 'deploymentPlan',
              affix: true,
              pageAuthList: ['devops_deploy_deploy_qry'],
            },
            show: true,
          },
          {
            path: '/main/install/resourceConfiguration',
            component: () =>
              import(
                /* webpackChunkName: "installDeploy" */
                /* webpackPrefetch: true */
                '@/views/install-deploy/resource-configuration/ResourceConfiguration.vue'
              ),
            name: 'resourceConfiguration',
            meta: {
              title: 'resourceConfiguration',
              icon: 'resourceConfiguration',
              affix: true,
              pageAuthList: ['devops_deploy_resourcePlan_qry'],
            },
            show: true,
          },
          {
            path: '/main/install/versionMatch',
            component: () =>
              import(
                /* webpackChunkName: "installDeploy" */ '@/views/install-deploy/version-match/VersionMatch.vue'
              ),
            name: 'versionMatch',
            meta: {
              title: 'versionMatch',
              icon: 'versionMatch',
              affix: true,
              pageAuthList: ['devops_deploy_versionMatch_qry'],
            },
            show: true,
            // showDcType: 'rdc' // 在RDC环境下才显示
          },
          // {
          //   path: '/main/install/serviceStatus',
          //   // component: () => import('@/views/installDeploy/service-status/ServiceStatus.vue'),
          //   component: () => import('@/views/Hello.vue'),
          //   name: 'serviceStatus',
          //   meta: { title: 'serviceStatus', icon: 'serviceStatus', affix: true },
          //   show: true,
          // },
          // 配置参数管理
          {
            path: '/main/install/configureParam',
            component: () =>
              import(
                /* webpackChunkName: "installDeploy" */ '@/views/install-deploy/configure-param/ConfigureParam.vue'
              ),
            name: 'configureParam',
            meta: {
              title: 'configureParam',
              icon: 'configureParam',
              affix: true,
              pageAuthList: ['devops_deploy_datasourceConfig_qry'],
            },
            show: true,
          },
        ],
      },
      {
        path: '/main/monitor',
        component: () =>
          import(
            /* webpackChunkName: "systemMonitor" */ '@/views/system-monitor/SystemMonitor.vue'
          ),
        name: 'systemMonitor',
        meta: {
          title: 'systemMonitor',
          icon: 'el-icon-monitor',
          affix: true,
          pageAuthList: [
            'devops_monitor_resource_qry',
            'devops_monitor_application_qry',
            'devops_monitor_business_qry',
            'devops_monitor_p2p_qry',
            'devops_monitor_p2p1_qry',
          ],
        },
        show: true,
        redirect: '/main/monitor/resLayerMonitor',
        children: [
          {
            path: '/main/monitor/resLayerMonitor',
            component: () =>
              import(
                /* webpackChunkName: "systemMonitor" */ '@/views/system-monitor/res-layer-monitor/ResLayerMonitor.vue'
              ),
            name: 'resLayerMonitor',
            meta: {
              title: 'resLayerMonitor',
              icon: 'resLayerMonitor',
              affix: true,
              pageAuthList: ['devops_monitor_resource_qry'],
            },
            show: true,
          },
          {
            path: '/main/monitor/appLayerMonitor',
            component: () =>
              import(
                /* webpackChunkName: "systemMonitor" */ '@/views/system-monitor/app-layer-monitor/AppLayerMonitor.vue'
              ),
            name: 'appLayerMonitor',
            meta: {
              title: 'appLayerMonitor',
              icon: 'appLayerMonitor',
              affix: true,
              pageAuthList: ['devops_monitor_application_qry'],
            },
            show: true,
          },
          {
            path: '/main/monitor/businessLayerMonitor',
            component: () =>
              import(
                /* webpackChunkName: "systemMonitor" */ '@/views/system-monitor/business-layer-monitor/BusinessLayerMonitor.vue'
              ),
            name: 'businessLayerMonitor',
            meta: {
              title: 'businessLayerMonitor',
              icon: 'businessLayerMonitor',
              affix: true,
              pageAuthList: ['devops_monitor_business_qry'],
            },
            show: true,
          },
          {
            path: '/main/monitor/p2p',
            component: () =>
              import(
                /* webpackChunkName: "systemMonitor" */ '@/views/system-monitor/p2p/P2P.vue'
              ),
            name: 'p2p',
            meta: {
              title: 'p2p',
              icon: 'p2p',
              affix: true,
              pageAuthList: ['devops_monitor_p2p_qry'],
            },
            show: true,
          },
          {
            path: '/main/monitor/p2p1.0',
            component: () =>
              import(
                /* webpackChunkName: "systemMonitor" */ '@/views/system-monitor/p2p1/P2P1.vue'
              ),
            name: 'p2p1',
            meta: {
              title: 'p2p1',
              icon: 'p2p',
              affix: true,
              pageAuthList: ['devops_monitor_p2p1_qry'],
            },
            show: true,
          },
        ],
      },
      {
        path: '/main/alarm',
        component: () =>
          import(
            /* webpackChunkName: "systemAlarm" */ '@/views/system-alarm/SystemAlarm.vue'
          ),
        name: 'systemAlarm',
        meta: {
          title: 'systemAlarm',
          icon: 'el-icon-warning-outline',
          affix: true,
          pageAuthList: [
            'devops_alarm_config_qry',
            'devops_alarm_current_qry',
            'devops_alarm_history_qry',
          ],
        },
        show: true,
        redirect: '/main/alarm/alarmConfig',
        children: [
          // 告警配置
          {
            path: '/main/alarm/alarmConfig',
            component: () =>
              import(
                /* webpackChunkName: "systemAlarm" */ '@/views/system-alarm/alarm-config/AlarmConfig.vue'
              ),
            name: 'alarmConfig',
            meta: {
              title: 'alarmConfig',
              icon: 'alarmConfig',
              affix: true,
              pageAuthList: ['devops_alarm_config_qry'],
            },
            // show: store.getters && store.getters['params/envDcInfo'],
            show: true,
            // showDcType: 'rdc', // 在RDC环境下才显示
          },
          // 告警实时数据
          {
            path: '/main/alarm/alarmData',
            component: () =>
              import(
                /* webpackChunkName: "systemAlarm" */ '@/views/system-alarm/alarm-data/AlarmData.vue'
              ),
            name: 'alarmData',
            meta: {
              title: 'alarmData',
              icon: 'alarmData',
              affix: true,
              pageAuthList: ['devops_alarm_current_qry'],
            },
            show: true,
          },
          // 告警历史数据
          {
            path: '/main/alarm/alarmHistory',
            component: () =>
              import(
                /* webpackChunkName: "systemAlarm" */ '@/views/system-alarm/alarm-history/AlarmHistory.vue'
              ),
            name: 'alarmHistory',
            meta: {
              title: 'alarmHistory',
              icon: 'alarmHistory',
              affix: true,
              pageAuthList: ['devops_alarm_history_qry'],
            },
            show: true,
          },
        ],
      },
      {
        path: '/main/basicTool',
        name: 'devBasicTool',
        component: () =>
          import(
            /* webpackChunkName: "devBasicTool" */ '@/views/basic-tool/BasicTool.vue'
          ),
        meta: {
          title: 'devBasicTool',
          icon: 'el-icon-data-analysis',
          affix: true,
          pageAuthList: [
            'devops_tool_serviceLog_qry',
            'devops_tool_apiLog_qry',
            'devops_tool_appLog_qry',
            'devops_tool_kafka_qry',
            'devops_tool_oss_qry',
            'devops_tool_redis_qry',
            'devops_tool_mysql_qry',
            'devops_tool_devInfo_qry',
            'devops_tool_devlog_qry',
            'devops_tool_operateLog_qry',
            'devops_tool_timer_qry',
          ],
        },
        show: true,
        redirect: '/main/basicTool/microServiceLog',
        children: [
          {
            path: '/main/basicTool/microServiceLog',
            component: () =>
              import(
                /* webpackChunkName: "devBasicTool" */ '@/views/basic-tool/micro-service-log/MicroServiceLog.vue'
              ),
            name: 'microServiceLog',
            meta: {
              title: 'microServiceLog',
              pageAuthList: ['devops_tool_serviceLog_qry'],
            },
            show: true,
          },
          {
            path: '/main/basicTool/restfulLog',
            component: () =>
              import(
                /* webpackChunkName: "devBasicTool" */ '@/views/basic-tool/restfull-log/RestfullLog.vue'
              ),
            name: 'restfulLog',
            meta: {
              title: 'restfulLog',
              pageAuthList: ['devops_tool_apiLog_qry'],
            },
            show: true,
            showDcType: 'ddc', // 在DDC环境下才显示
          },
          {
            path: '/main/basicTool/appLog',
            component: () =>
              import(
                /* webpackChunkName: "devBasicTool" */ '@/views/basic-tool/app-log/AppLog.vue'
              ),
            name: 'appLog',
            meta: {
              title: 'appLog',
              pageAuthList: ['devops_tool_appLog_qry'],
            },
            show: true,
            // showDcType: 'ddc', // 在DDC环境下才显示
          },
          {
            path: '/main/basicTool/kafkaSearch',
            component: () =>
              import(
                /* webpackChunkName: "devBasicTool" */ '@/views/basic-tool/kafka-search/KafkaSearch.vue'
              ),
            name: 'kafkaSearch',
            meta: {
              title: 'kafkaSearch',
              pageAuthList: ['devops_tool_kafka_qry'],
            },
            show: true,
            showDcType: 'ddc', // 在DDC环境下才显示
          },
          {
            path: '/main/basicTool/ossSearch',
            component: () =>
              import(
                /* webpackChunkName: "devBasicTool" */ '@/views/basic-tool/oss-search/OssSearch.vue'
              ),
            name: 'ossSearch',
            meta: {
              title: 'ossSearch',
              pageAuthList: ['devops_tool_oss_qry'],
            },
            show: true,
          },
          {
            path: '/main/basicTool/redisSearch',
            component: () =>
              import(
                /* webpackChunkName: "devBasicTool" */ '@/views/basic-tool/redis-search/RedisSearch.vue'
              ),
            name: 'redisSearch',
            meta: {
              title: 'redisSearch',
              pageAuthList: ['devops_tool_redis_qry'],
            },
            show: true,
          },
          {
            path: '/main/basicTool/mysqlSearch',
            component: () =>
              import(
                /* webpackChunkName: "devBasicTool" */ '@/views/basic-tool/mysql-search/MysqlSearch.vue'
              ),
            name: 'mysqlSearch',
            meta: {
              title: 'mysqlSearch',
              pageAuthList: ['devops_tool_mysql_qry'],
            },
            show: true,
          },
          {
            path: '/main/basicTool/deviceSearch',
            component: () =>
              import(
                /* webpackChunkName: "devBasicTool" */ '@/views/basic-tool/device-search/DeviceSearch.vue'
              ),
            name: 'deviceSearch',
            meta: {
              title: 'deviceSearch',
              pageAuthList: ['devops_tool_devInfo_qry'],
            },
            show: true,
          },
          {
            path: '/main/basicTool/deviceLog',
            component: () =>
              import(
                /* webpackChunkName: "devBasicTool" */ '@/views/basic-tool/device-log/DeviceLog.vue'
              ),
            name: 'deviceLog',
            meta: {
              title: 'deviceLog',
              pageAuthList: ['devops_tool_devlog_qry'],
            },
            show: true,
          },
          {
            path: '/main/basicTool/operateLog',
            component: () =>
              import(
                /* webpackChunkName: "devBasicTool" */ '@/views/basic-tool/operate-log/OperateLog.vue'
              ),
            name: 'operateLog',
            meta: {
              title: 'operateLog',
              pageAuthList: ['devops_tool_operateLog_qry'],
            },
            show: true,
          },
          {
            path: '/main/basicTool/scheduleTaskLog',
            component: () =>
              import(
                '@/views/basic-tool/schedule-task-log/ScheduleTaskLog.vue'
              ),
            name: 'scheduleTaskLog',
            meta: {
              title: 'scheduleTaskLog',
              pageAuthList: ['devops_tool_timer_qry'],
            },
            show: true,
          },
        ],
      },
      {
        path: '/main/devopsStatistics',
        name: 'devopsStatistics',
        component: () =>
          import(
            /* webpackChunkName: "devopsStatistics" */ '@/views/devops-statistics/DevopsStatistics.vue'
          ),
        meta: {
          title: 'devopsStatistics',
          icon: 'el-icon-s-data',
          affix: true,
          pageAuthList: [
            'devops_statistics_access_qry',
            'devops_statistics_lowAccess_qry',
            'devops_statistics_alert_qry',
          ],
        },
        show: true,
        // showDcType: 'ddc', // 在DDC环境下才显示
        redirect: '/main/devopsStatistics/characteVisitVolume',
        children: [
          {
            path: '/main/devopsStatistics/characteVisitVolume',
            component: () =>
              import(
                /* webpackChunkName: "devopsStatistics" */ '@/views/devops-statistics/characte-visit-volume/CharacteVisitVolume.vue'
              ),
            name: 'characteVisitVolume',
            meta: {
              title: 'characteVisitVolume',
              icon: 'businessData',
              affix: true,
              pageAuthList: ['devops_statistics_access_qry'],
            },
            show: true,
          },
          {
            path: '/main/devopsStatistics/lowFrequencyAccess',
            component: () =>
              import(
                /* webpackChunkName: "devopsStatistics" */ '@/views/devops-statistics/low-frequency-access/LowFrequencyAccess.vue'
              ),
            name: 'lowFrequencyAccess',
            meta: {
              title: 'lowFrequencyAccess',
              icon: 'businessData',
              affix: true,
              pageAuthList: ['devops_statistics_lowAccess_qry'],
            },
            show: true,
          },
          {
            path: '/main/devopsStatistics/featureAlarmStatics',
            component: () =>
              import(
                /* webpackChunkName: "devopsStatistics" */ '@/views/devops-statistics/feature-alarm-statics/FeatureAlarmStatics.vue'
              ),
            name: 'featureAlarmStatics',
            meta: {
              title: 'featureAlarmStatics',
              icon: 'businessData',
              affix: true,
              pageAuthList: ['devops_statistics_alert_qry'],
            },
            show: true,
          },
        ],
      },
      // {
      //   path: '/main/log',
      //   component: () => import('@/views/log/Log.vue'),
      //   name: 'log',
      //   redirect: '/main/log/index',
      //   meta: { title: 'log', icon: 'el-icon-document', affix: true },
      //   show: true,
      //   children: [
      //     // {
      //     //   path: '/main/log/logFile',
      //     //   component: () => import('@/views/Hello.vue'),
      //     //   name: 'logFile',
      //     //   meta: { title: 'logFile', icon: 'logFile', affix: true },
      //     //   show: true
      //     // },
      //     {
      //       path: '/main/log/index',
      //       component: () => import('@/views/log/DownloadLog.vue'),
      //       name: 'downloadLog',
      //       meta: { title: 'downloadLog', icon: 'el-icon-data-line', affix: true },
      //       show: true
      //     },
      //     // {
      //     //   path: '/main/log/envHealthCheck',
      //     //   component: () => import('@/views/Hello.vue'),
      //     //   name: 'envHealthCheck',
      //     //   meta: { title: 'envHealthCheck', icon: 'envHealthCheck', affix: true },
      //     //   show: true
      //     // },
      //   ]
      // },
    ],
  },
  {
    path: '*',
    redirect: '/main/install/resourceConfiguration',
    component: Layout,
  },
]

const createRouter = () =>
  new Router({
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  })

const router = createRouter()

// 注意：路由Loading逻辑现在在Layout.vue中处理

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}
export default router
