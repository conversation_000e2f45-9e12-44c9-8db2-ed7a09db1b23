<template>
  <div>
    <div class="basic-tool">
      <router-view />
    </div>
  </div>
</template>

<script>
export default {
  name: 'BasicTool',
  components: {},
  computed: {},
  mounted() {},
  methods: {},
}
</script>
<style lang="scss" scoped>
.basic-tool {
  width: 100%;
  height: 100%;
  overflow: auto;
}
</style>
<style lang="scss">
.page-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  .search-wrapper {
    width: 100%;
    padding: 24px 24px 4px 24px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    min-width: 800px;
    & > div {
      margin-bottom: 20px;
    }
    .search-time {
      max-width: 400px;
      margin-right: 20px;
      overflow: hidden;
    }
    .search-btn-box {
      width: 100px;
      margin-right: 20px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
    .el-input__icon {
      line-height: 34px;
    }
  }
  .table-wrapper {
    flex: 1;
    width: 100%;
    padding: 0px 24px;
    box-sizing: border-box;
    overflow: hidden;
  }
  .tab-wrapper {
    width: 100%;
    padding: 0px 24px;
    box-sizing: border-box;
  }
}

.search-wrapper .el-input__inner {
  height: 32px;
  line-height: 32px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  background: transparent;
  border-radius: 16px;
  width: 250px;
}

.search-wrapper .el-date-editor {
  width: 250px;
}

.search-wrapper .tvt-input .tvt-field-set {
  border: none;
}
.search-wrapper .tvt-select .tvt-field-set {
  border: none;
}

.search-wrapper .tvtDate-custom .el-input__inner:focus {
  outline: none;
  border-color: #409eff;
}

.search-wrapper .tvt-select .el-select-dropdown__item.selected {
  color: #429efd !important;
}

.table-wrapper .el-table .el-table__cell {
  padding: 8px 0 !important;
}

.table-wrapper .el-table__body .cell {
  white-space: pre-line;
}
.search-wrapper .el-date-editor .el-range-separator {
  display: inline-flex;
  align-items: center;
}
.search-wrapper .el-date-editor .el-range__icon {
  line-height: 24px;
}

.search-wrapper .el-date-editor .el-range__close-icon {
  line-height: 28px;
}

.search-wrapper .el-date-editor .el-input__icon {
  line-height: 24px;
}

.search-wrapper .el-date-editor .el-range-separator {
  display: inline-flex;
  align-items: center;
}
.tu-input .search.blur-search:hover {
  border: 1px solid #e0e0e0 !important;
}
.btn-text {
  color: #429efd !important;
  cursor: pointer;
  + .btn-text {
    margin-left: 20px;
  }
}
</style>
