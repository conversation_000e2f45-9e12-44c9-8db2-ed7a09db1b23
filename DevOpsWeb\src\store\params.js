/**
 * 全局 环境 和 dcName
 */
import { getEnvDcName } from '@/api/installDeploy'
import { getLocale } from '@/lang'
import selfLocaLStorage from '@/utils/selfLocalStorage'

export default {
  namespaced: true,
  state: () => ({
    environmentCode: '',
    environmentName: '',
    dcName: '',
    dcNameDesc: '',
    envDcInfo: selfLocaLStorage.getItem('envDcInfo') || null,
  }),
  mutations: {
    ENV__PARAMS_INFO(state, data) {
      state.environmentCode = data
    },
    ENV__NAME(state, data) {
      state.environmentName = data
    },
    DCNAME__PARAMS_INFO(state, data) {
      state.dcName = data
    },
    DCNAMEDESC__PARAMS_INFO(state, data) {
      state.dcNameDesc = data
    },
    EVN_DC_INFO(state, data) {
      state.envDcInfo = data
      selfLocaLStorage.setItem('envDcInfo', JSON.stringify(data))
    },
  },
  actions: {
    // 查询环境和dcName的名称
    fetchEnvDcNameRequest({ commit }) {
      return getEnvDcName(getLocale()).then((res) => {
        if (res && res.data) {
          commit('ENV__PARAMS_INFO', res.data.environmentCode)
          commit('ENV__NAME', res.data.environmentName)
          commit('DCNAME__PARAMS_INFO', res.data.dcCode)
          commit('DCNAMEDESC__PARAMS_INFO', res.data.dcName)
          commit('EVN_DC_INFO', res.data)
        }
      })
    },
  },
}
