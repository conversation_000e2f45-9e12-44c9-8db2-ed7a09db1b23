<template>
  <!-- 首页-实时监控趋势数据 -->
  <div class="monitor-wrapper">
    <div class="monitor-full-content-wrapper">
      <div v-loading="loading" class="monitor-list-wrapper">
        <div
          v-for="item of areas"
          :key="`${searchCond.search}-${item ? item.value : 'collapse'}`"
          :class="[
            areas && areas.length > 1
              ? 'monitor-item-wrapper'
              : 'monitor-line-wrapper',
          ]"
        >
          <chart-component
            :collapse-key="`${searchCond.dcId}-${item ? item.value : 'area'}-${
              searchCond.quota
            }`"
            :search-cond="{ ...collapseCond, area: item }"
            :data-obj="dataObj ? dataObj[item ? item.value : null] : null"
            :collapse-open="collapseOpen"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import ChartComponent from './ChartComponent.vue'
import { getAllMonitorDataApi } from '@/api/home.js'
import { dealLineChartData } from '@/utils/common'
import { mapState } from 'vuex'

const REQ_TIME_INTER = 2 * 60 * 1000 // 2分钟时间间隔请求

export default {
  name: 'MonitorChart',
  components: {
    ChartComponent,
  },
  props: {
    searchCond: {
      type: Object,
      default: () => null,
    },
    collapseOpen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      echartSelectList: [],
      filterCond: {
        searchType: null,
        dcId: null,
        timeRange: [],
        areas: [],
        treeQuota: null,
      },
      collapseCond: {
        // 传给MonitorCollapse组件的参数
        monitorType: 'monitor.nat',
        ctSubtype: 'NatServer',
        metrics: ['nat_dev_online'],
        timeRange: [],
      },
      dataObj: {}, // 统一请求的折线图数据,
      areas: [], // 对应dc、指标的集群选项，默认从查询条件中拿，全量接口返回了则用全量接口的
      loading: false, // 全量请求时loading
      needInitRequest: false, // 是否需要初始化请求数据，当查询参数变化但是又不展开时，需要将此参数置为true，等待展开时再去请求数据
      initFlag: false, // 是否已经初始化请求数据过,
    }
  },
  computed: {
    ...mapState('monitor', ['dcInfoList', 'clusterCodeNameObj']),
  },
  watch: {
    searchCond: {
      handler(newVal, oldVal) {
        if (newVal && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          // console.log('查询参数不一样变化this.searchCond', newVal)
          const {
            monitorType,
            ctSubtype,
            metrics,
            dcId = null,
            timeRange = [],
            quota,
            areas,
          } = newVal
          if (metrics) {
            const formData = {
              monitorType,
              ctSubtype,
              metrics,
              timeRange,
              dcId,
            }
            this.filterCond = { ...formData, quota, areas }
            // this.areas = areas // 查询条件变化则重置area选项 注意：此处不需要，在图表请求返回时重置，避免两者不一致时图表切换变化
            this.areas = [undefined]
            this.collapseCond = formData
            if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
              if (this.collapseOpen) {
                this.needInitRequest = false
                this.initFlag = true
                this.dataObj = {}
                this.getAllMonitorDataList(formData) // 查询条件变化则查询全量趋势图数据
                // 定时刷新功能 -- 2分钟刷新请求一次
                if (this.requestTimer) {
                  clearInterval(this.requestTimer) // 清除定时器
                }
                // 没有选择时间范围则开启定时器
                if (!(timeRange && timeRange.length)) {
                  this.requestTimer = setInterval(() => {
                    this.getAllMonitorDataList(formData)
                  }, REQ_TIME_INTER)
                }
              } else {
                this.needInitRequest = true
                this.initFlag = false
                this.dataObj = {}
              }
            }
          }
        }
      },
      deep: true,
      immediate: true,
    },
    collapseOpen: {
      handler(val) {
        if (val) {
          // 展开时
          if (this.needInitRequest) {
            this.needInitRequest = false
            this.initFlag = true
            // 需要请求数据
            this.getAllMonitorDataList()
            // 定时刷新功能 -- 2分钟刷新请求一次
            if (this.requestTimer) {
              clearInterval(this.requestTimer) // 清除定时器
            }
            const { timeRange = [] } = this.filterCond || {}
            // 没有选择时间范围则开启定时器
            if (!(timeRange && timeRange.length)) {
              this.requestTimer = setInterval(() => {
                this.getAllMonitorDataList()
              }, REQ_TIME_INTER)
            }
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  beforeDestroy() {
    // 定时刷新功能 -- 2分钟刷新请求一次
    if (this.requestTimer) {
      clearInterval(this.requestTimer) // 清除定时器
    }
  },
  methods: {
    handleFocus() {
      this.$emit('focus')
    },
    // 获取全量折线图数据
    async getAllMonitorDataList(formData, refreshTime = null) {
      if (!formData) {
        formData = this.filterCond
      }
      const {
        monitorType,
        // ctSubtype,
        metrics,
        timeRange,
        dcId,
        // area,
      } = formData
      //   const data = { pageNum:  current, pageSize: size}
      const data = {
        monitorType,
        metrics,
        dcId,
      }
      // console.log('查询条件', timeRange)
      if (timeRange && timeRange.length) {
        data.startTime = timeRange[0]
        data.endTime = timeRange[1]
        data.startTimeTmp = window
          .moment(timeRange[0])
          .format('YYYY-MM-DD HH:mm:ss')
        data.endTimeTmp = window
          .moment(timeRange[1])
          .format('YYYY-MM-DD HH:mm:ss')
      }
      this.searchTimeRange = timeRange
      // 判断图形类型
      // if (ctSubtype === 'Https域名' || ctSubtype === '证书') {
      //   // 柱状图不需要一次全部请求
      //   this.dataObj = {}
      //   return
      // }
      // P2P2.0中选择RDC、UDT时，不传dcId
      if (monitorType === 'monitor.udt' && dcId === '1') {
        delete data.dcId // 不传dcId
      }
      // wsConnect 中app连接数，有点特殊，需要前端在入参的extra字段里加个内容：group：appId
      if (
        monitorType === 'monitor.wsConnect' &&
        data.metrics[0] === 'connectNum'
      ) {
        data.extra = {
          group: 'appId',
        }
      }
      this.loading = true
      try {
        const res = await getAllMonitorDataApi(data)
        this.loading = false
        const resultObj = dealLineChartData(
          data,
          res,
          this.clusterCodeNameObj,
          refreshTime
        )
        if (resultObj) {
          const { dataObj, areas } = resultObj
          this.areas = areas
          this.dataObj = dataObj
        }
      } catch (err) {
        this.loading = false
        console.log(err)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.monitor-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: content-box;
  // height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  .echart-search-wrapper {
    width: 100%;
    background: white;
    padding: 10px 0px 0px 0px;
  }
  .monitor-content-wrapper {
    width: 100%;
    height: calc(100% - 55px);
    box-sizing: border-box;
    overflow-y: auto;
    // padding: 10px;
  }
  .monitor-full-content-wrapper {
    width: 100%;
    height: 100%;
    box-sizing: content-box;
    overflow-y: auto;
    // margin: 25px 0px;
    // padding: 10px;
  }
  .monitor-list-wrapper {
    width: 100%;
    height: 100%;
    min-height: 300px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 0px 10px 0px 10px;
    .monitor-item-wrapper {
      width: calc(50% - 10px);
      min-height: 300px;
    }
    .monitor-line-wrapper {
      width: calc(100%);
      height: 100%;
    }
  }
}
</style>
