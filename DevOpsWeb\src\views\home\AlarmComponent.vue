<template>
  <!-- 首页-实时告警表格数据 -->
  <div class="alarm-list-wrapper">
    <div class="alarm-line-wrapper">
      <div class="table-wrapper">
        <tvt-table
          v-myLoading="loading"
          :data="alarmList"
          :columns="alarmDataColumns"
          height="400"
          :border="true"
          :border-bottom="true"
          :pagination="false"
          @onFetchData="getAlarmList"
        >
          <template #bodyCell="{ row, column }">
            <template v-if="column.prop === 'monitorType'">
              <span>{{ dealMonitorType(row.monitorType) }}</span>
            </template>
            <template v-if="column.prop === 'startTime'">
              <span>{{ stampToStrLongMethod(row.startTime) }}</span>
            </template>
            <template v-if="column.prop === 'duration'">
              <span>{{ durationToStrLongMethod(row.duration) }}</span>
            </template>
            <template v-if="column.prop === 'alertMsg'">
              <span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="row[column.prop]"
                  placement="left-start"
                >
                  <span class="tooltip-ellipsis-box">{{
                    row[column.prop]
                  }}</span>
                </el-tooltip>
              </span>
            </template>
          </template>
        </tvt-table>
      </div>
    </div>
  </div>
</template>
<script>
import { getMonitorList } from '@/api/systemAlarm.js'
import { debounce, stampToStrLong, formatDuring } from '@/utils/common'
import { getLocale } from '@/lang'
import {
  ctyTypeOptions,
  alarmDataColumns,
  ctyTypeClassify,
} from '@/views/system-alarm/config.js'
export default {
  name: 'AlarmCollapse',
  components: {},
  props: {
    collapseKey: {
      type: String,
      default: 'alarm-table',
    },
    collapseOpen: {
      type: Boolean,
      default: false,
    },
    searchCond: {
      type: Object,
      default: () => ({
        ctType: 'monitor.host',
        timeRange: [],
        dcId: null,
        area: null,
      }),
    },
  },
  data() {
    return {
      filterCond: {
        monitorType: 'monitor.host',
        timeRange: [],
        area: null,
        dcId: null,
      },
      needInitRequest: false, // 是否需要初始化请求数据，当查询参数变化但是又不展开时，需要将此参数置为true，等待展开时再去请求数据
      alarmDataColumns,
      alarmList: [],
      monitorTypeName: ctyTypeOptions.reduce((pre, next) => {
        pre[next.value] = next.label
        return pre
      }, {}),
      monitorObj: {
        resource: this.$t('nav.resLayerMonitor'),
        application: this.$t('nav.appLayerMonitor'),
        business: this.$t('nav.businessLayerMonitor'),
        p2p: this.$t('nav.p2p'),
      },
      loading: false,
    }
  },
  computed: {},
  watch: {
    searchCond: {
      handler(newVal, oldVal) {
        if (newVal) {
          // console.log('查询参数变化', newVal)
          if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
            // 查询参数不一样
            // console.log('查询参数不一样变化this.collapseOpen', this.collapseOpen)
            const { ctType, timeRange, dcId, area = null } = newVal
            // console.log('newVal', newVal)
            const monitorType = ctType
            const formData = { monitorType, timeRange, dcId, area }
            this.filterCond = formData
            if (this.collapseOpen) {
              // 查询参数变化且是打开状态，则请求数据
              this.alarmList = []
              this.needInitRequest = false
              // console.log('打开状态请求数据')
              this.$nextTick(() => {
                this.getAlarmList()
              })
            } else {
              this.needInitRequest = true
              this.alarmList = []
            }
          }
        }
      },
      deep: true,
      immediate: true,
    },
    collapseOpen: {
      handler(val) {
        // console.log('collapseOpen', val)
        if (val) {
          // 展开时
          if (this.needInitRequest) {
            // 需要请求数据
            this.alarmList = []
            this.needInitRequest = false
            this.$nextTick(() => {
              this.getAlarmList()
            })
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    getAlarmList: debounce(async function () {
      this.loading = true
      const { monitorType, timeRange, dcId } = this.filterCond
      const data = {
        monitorType,
        dcId,
        lang: getLocale(),
      }
      if (timeRange && timeRange.length) {
        data.startTime = timeRange[0]
        data.endTime = timeRange[1]
        data.startTimeTmp = window
          .moment(timeRange[0])
          .format('YYYY-MM-DD HH:mm:ss')
        data.endTimeTmp = window
          .moment(timeRange[1])
          .format('YYYY-MM-DD HH:mm:ss')
      }
      return getMonitorList(data)
        .then((res) => {
          this.loading = false
          if (res.basic && res.basic.code == 200) {
            if (res.data) {
              this.alarmList = res.data.map((item) => {
                const ctyClassify = ctyTypeClassify[item.monitorType]
                const systemMonitortType = this.monitorObj[ctyClassify]
                item.systemMonitortType = systemMonitortType
                return item
              })
            } else {
              this.alarmList = []
            }
          }
        })
        .catch((err) => {
          this.loading = false
          console.log(err)
        })
    }, 500),
    stampToStrLongMethod(time) {
      let intTime = parseInt(time) //接口给的字符串时间
      return stampToStrLong(intTime)
    },
    dealMonitorType(monitorType) {
      return this.monitorTypeName[monitorType] || ''
    },
    durationToStrLongMethod(time) {
      let intTime = parseInt(time) //接口给的字符串时间
      return formatDuring(intTime)
    },
  },
}
</script>
<style lang="scss" scoped>
.alarm-list-wrapper {
  width: 100%;
  height: 100%;
  min-height: 300px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 20px;
  .alarm-line-wrapper {
    width: calc(100%);
    height: 400px;
  }
  .table-wrapper {
    width: calc(100%);
    height: 400px;
  }
}
</style>
<style>
.alarm-line-wrapper .table-wrapper .el-table__body .cell {
  white-space: pre-line;
}
.alarm-line-wrapper .search-wrapper .el-date-editor .el-range-separator {
  display: inline-flex;
  align-items: center;
}
.alarm-line-wrapper .search-wrapper .el-date-editor .el-range__icon {
  line-height: 28px;
}

.alarm-line-wrapper .search-wrapper .el-date-editor .el-range__close-icon {
  line-height: 28px;
}
</style>
