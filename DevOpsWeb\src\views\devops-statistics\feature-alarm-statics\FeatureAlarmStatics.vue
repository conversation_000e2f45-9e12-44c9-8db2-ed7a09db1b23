<template>
  <!-- 特性告警统计新版页面 -->
  <div class="feature-alarm-statics">
    <!-- 头部查询区域 -->
    <div class="search-wrapper">
      <tvt-date-picker
        v-model="filterCond.dayTime"
        align="right"
        type="daterange"
        :placeholder="$t('microLogTab.dayTimeSelect')"
        :clearable="false"
        style="width: 250px"
        :picker-options="pickerOptions"
        @change="handleDate"
      />
      <div class="search-btn-box">
        <el-button type="primary" size="small" round @click="handleReset">
          {{ $t('reset') }}
        </el-button>
        <el-button type="primary" size="small" round @click="handleRefresh">
          {{ $t('refresh') }}
        </el-button>
        <el-button type="primary" size="small" round @click="handleExport">
          {{ $t('export') }}
        </el-button>
      </div>
    </div>

    <!-- 指标展示区域 -->
    <div class="metrics-wrapper">
      <el-collapse
        v-model="activeNames"
        class="metrics-collapse"
        @change="handleCollapseChange"
      >
        <!-- 只显示有统计数据的指标 -->
        <el-collapse-item
          v-for="metric in visibleMetricsConfig"
          :key="metric.type"
          :name="metric.type"
          class="metric-card"
        >
          <template slot="title">
            <div class="collapse-title-wrapper">
              <span class="collapse-title">{{ metric.title }}</span>
              <span class="collapse-stats">{{
                statisticsDataObj[metric.type]
              }}</span>
            </div>
          </template>

          <!-- 表格区域 -->
          <div v-show="isExpanded(metric.type)" class="metric-table-wrapper">
            <tvt-table
              v-if="shouldRenderTable(metric.type)"
              v-myLoading="loadingStates[metric.type]"
              :data="getTableDataSlice(metric.type)"
              :columns="getTableColumns(metric.type)"
              :border="true"
              :border-bottom="true"
              :span-method="getSpanMethod(metric.type)"
              :pagination="false"
              :auto-height="false"
              :lazy="true"
              :virtual-scroll="true"
              :row-height="40"
              @onFetchData="() => fetchTableData(metric.type)"
            >
              <template #bodyCell="{ row, column }">
                <span>{{ row[column.prop] }}</span>
              </template>
            </tvt-table>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import { commonShortcuts } from '@/utils/common'
import { mapState } from 'vuex'
import {
  getAlarmStatisticsSummary,
  getAlarmStatisticsDetail,
  exportAlarmStatisticsData,
} from '@/api/devopsStatistics'
import { hanldeDownload } from '@/utils/common'
import errorCode from '@/api/errorCode.js'
import { getLocale } from '@/lang'

export default {
  name: 'FeatureAlarmStaticsNew',
  components: {},
  data() {
    const pageVM = this
    return {
      // 查询条件
      filterCond: {
        dayTime: this.getInitDate(),
      },
      // 时间选择器配置
      startDate: null,
      pickerOptions: {
        onPick: (time) => {
          if (time.minDate && !time.maxDate) {
            pageVM.startDate = time.minDate
          }
          if (time.maxDate) {
            pageVM.startDate = null
          }
        },
        disabledDate(time) {
          if (pageVM.startDate) {
            const start = moment(pageVM.startDate)
            const end = moment(time)
            return (
              time.getTime() > Date.now() ||
              end < start.clone().add(-29, 'd') ||
              end > start.clone().add(29, 'd')
            )
          } else {
            return time.getTime() > Date.now()
          }
        },
        shortcuts: commonShortcuts,
      },
      // 展开的指标卡片 - 初始为空，根据数据动态设置
      activeNames: [],

      // 六种指标类型配置
      metricsConfig: [
        {
          type: 'Host',
          title: this.$t('devopsStatisticsTab.hostAlarm'),
        },
        {
          type: 'Microservice',
          title: this.$t('devopsStatisticsTab.microServiceAlarm'),
        },
        {
          type: 'OpenSource',
          title: this.$t('devopsStatisticsTab.openSourceAlarm'),
        },
        {
          type: 'DomainCert',
          title: this.$t('devopsStatisticsTab.domainCert'),
        },
        {
          type: 'Business',
          title: this.$t('devopsStatisticsTab.businessMonitor'),
        },
        {
          type: 'p2p20',
          title: 'P2P2.0',
        },
      ],
      tableColumnsConfig: {
        Host: [
          {
            prop: 'monitorTypeDesc',
            label: this.$t('devopsStatisticsTab.monitorType'),
            width: 150,
          },
          {
            prop: 'ctIndexFieldDesc',
            label: this.$t('devopsStatisticsTab.ctIndexField'),
            minWidth: 200,
          },
          {
            prop: 'host',
            label: this.$t('devopsStatisticsTab.host'),
            minWidth: 200,
          },
          {
            prop: 'alarmCount',
            label: this.$t('devopsStatisticsTab.alarmCount'),
            width: 120,
          },
        ],
        Microservice: [
          {
            prop: 'monitorTypeDesc',
            label: this.$t('devopsStatisticsTab.monitorType'),
            width: 150,
          },
          {
            prop: 'ctIndexFieldDesc',
            label: this.$t('devopsStatisticsTab.ctIndexField'),
            minWidth: 200,
          },
          {
            prop: 'microService',
            label: this.$t('microService'),
            minWidth: 200,
          },
          {
            prop: 'alarmCount',
            label: this.$t('devopsStatisticsTab.alarmCount'),
            width: 120,
          },
        ],
        OpenSource: [
          {
            prop: 'monitorTypeDesc',
            label: this.$t('devopsStatisticsTab.monitorType'),
            width: 150,
          },
          {
            prop: 'ctIndexFieldDesc',
            label: this.$t('devopsStatisticsTab.ctIndexField'),
            minWidth: 200,
          },
          { prop: 'node', label: this.$t('resourceTab.node'), minWidth: 200 },
          {
            prop: 'alarmCount',
            label: this.$t('devopsStatisticsTab.alarmCount'),
            width: 120,
          },
        ],
        DomainCert: [
          {
            prop: 'monitorTypeDesc',
            label: this.$t('devopsStatisticsTab.monitorType'),
            width: 150,
          },
          {
            prop: 'ctIndexFieldDesc',
            label: this.$t('devopsStatisticsTab.ctIndexField'),
            minWidth: 180,
          },
          { prop: 'name', label: this.$t('ossSearchTab.name'), minWidth: 150 },
          {
            prop: 'customerName',
            label: this.$t('domainCertTab.customerName'),
            minWidth: 120,
          },
          {
            prop: 'alarmCount',
            label: this.$t('devopsStatisticsTab.alarmCount'),
            width: 120,
          },
        ],
        Business: [
          {
            prop: 'monitorTypeDesc',
            label: this.$t('devopsStatisticsTab.monitorType'),
            width: 150,
          },
          {
            prop: 'ctIndexFieldDesc',
            label: this.$t('devopsStatisticsTab.ctIndexField'),
            minWidth: 200,
          },
          { prop: 'name', label: this.$t('ossSearchTab.name'), minWidth: 200 },
          {
            prop: 'alarmCount',
            label: this.$t('devopsStatisticsTab.alarmCount'),
            width: 120,
          },
        ],
        p2p20: [
          {
            prop: 'monitorTypeDesc',
            label: this.$t('devopsStatisticsTab.monitorType'),
            width: 150,
          },
          {
            prop: 'ctIndexFieldDesc',
            label: this.$t('devopsStatisticsTab.ctIndexField'),
            minWidth: 200,
          },
          { prop: 'name', label: this.$t('ossSearchTab.name'), minWidth: 200 },
          {
            prop: 'alarmCount',
            label: this.$t('devopsStatisticsTab.alarmCount'),
            width: 120,
          },
        ],
      },

      // 表格数据
      tableData: {
        Host: [],
        Microservice: [],
        OpenSource: [],
        DomainCert: [],
        Business: [],
        p2p20: [],
      },
      // 表格渲染控制
      renderedTables: new Set(), // 已渲染的表格

      // 分片渲染配置
      sliceConfig: {
        defaultSliceSize: 100, // 默认分片大小
        maxSliceSize: 200, // 最大分片大小
      },

      // 防抖定时器
      debounceTimers: {},

      // 加载状态
      loadingStates: {
        Host: false,
        Microservice: false,
        OpenSource: false,
        DomainCert: false,
        Business: false,
        p2p20: false,
      },
      // 统计数据
      statisticsDataObj: {},
      // 加载统计数据状态
      summaryLoading: false,
      // 有统计数据的类型列表
      availableTypes: [],
      // 添加数据缓存对象
      tableDataCache: {
        Host: null,
        Microservice: null,
        OpenSource: null,
        DomainCert: null,
        Business: null,
        p2p20: null,
      },
      // 添加请求参数缓存，用于判断是否需要重新请求
      requestParamsCache: {
        Host: null,
        Microservice: null,
        OpenSource: null,
        DomainCert: null,
        Business: null,
        p2p20: null,
      },
      // 记录上一次展开的项，用于对比新展开的项
      previousActiveNames: [],
      isExporting: false, // 导出中标志，防止重复点击
    }
  },
  computed: {
    ...mapState('params', ['envDcInfo']),
    // 计算可见的指标配置 - 只显示有统计数据的指标
    visibleMetricsConfig() {
      return this.metricsConfig.filter((metric) =>
        this.availableTypes.includes(metric.type)
      )
    },
  },
  async mounted() {
    await this.loadSummaryData()
    this.loadInitialData()
  },
  beforeDestroy() {
    // 清理所有防抖定时器
    Object.values(this.debounceTimers).forEach((timer) => {
      if (timer) clearTimeout(timer)
    })
    this.debounceTimers = {}
  },

  methods: {
    // 获取初始日期
    getInitDate() {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24)
      return [date, date]
    },

    // 处理日期变化
    async handleDate(val) {
      this.filterCond.dayTime = val
      this.clearCache()
      await this.loadSummaryData()
      this.refreshActiveData()
    },
    // 处理重置
    async handleReset() {
      this.filterCond.dayTime = this.getInitDate()
      this.clearCache()
      await this.loadSummaryData()
      this.refreshActiveData()
    },
    // 处理刷新
    async handleRefresh() {
      this.clearCache()
      await this.loadSummaryData()
      this.refreshActiveData()
    },
    // 处理导出
    async handleExport() {
      // 判断是否正在导出中
      if (this.isExporting) {
        this.$message.warning(this.$t('microLogTab.downloadFreq'))
        return
      }
      this.isExporting = true

      // 添加loading效果
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, .5)',
        text: this.$t('LoadingDownload'),
      })

      const params = {
        dayTimeStart: moment(this.filterCond.dayTime[0]).format('YYYY-MM-DD'),
        dayTimeEnd: moment(this.filterCond.dayTime[1]).format('YYYY-MM-DD'),
        lang: this.$i18n.locale,
      }

      try {
        const resBlob = await exportAlarmStatisticsData(params)
        const fileName = `${this.$t('nav.featureAlarmStatics')}_${
          params.dayTimeStart
        }_${params.dayTimeEnd}.xlsx`

        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            // 尝试解析返回内容
            const result = JSON.parse(e.target.result)
            if (result.basic && result.basic.code) {
              const errorObj = errorCode[getLocale()]
              // 解析成功，提示错误信息
              this.$message.error(
                errorObj[result.basic.code] || this.$t('exportFail')
              )
              loading.close()
              this.isExporting = false
              return
            }
          } catch (parseError) {
            // 如果无法解析为JSON，说明是正常的文件内容，直接下载
            hanldeDownload(resBlob, fileName)
            loading.close()
            this.isExporting = false
          }
        }
        reader.onerror = () => {
          this.$message.error(this.$t('exportFail'))
          loading.close()
          this.isExporting = false
        }
        reader.readAsText(resBlob)
      } catch (error) {
        console.error('导出数据失败:', error)
        this.$message.error(this.$t('exportFail'))
        loading.close()
        this.isExporting = false
      }
    },
    // 判断表格是否应该渲染（懒加载）
    shouldRenderTable(type) {
      return this.renderedTables.has(type) || this.isExpanded(type)
    },

    // 判断是否展开
    isExpanded(type) {
      return this.activeNames.includes(type)
    },

    // 获取表格数据切片（分批渲染）
    getTableDataSlice(type) {
      const data = this.tableData[type] || []
      const sliceSize = this.getSliceSize(data.length)
      // 如果数据量小，直接返回全部
      if (data.length <= sliceSize) {
        return data
      }
      // 返回切片数据
      return data.slice(0, sliceSize)
    },
    // 根据数据量计算切片大小
    getSliceSize(dataLength) {
      if (dataLength <= 50) return dataLength
      if (dataLength <= 100) return this.sliceConfig.defaultSliceSize
      return this.sliceConfig.maxSliceSize
    },
    // 防抖装饰器
    debounce(key, func, delay = 100) {
      return (...args) => {
        if (this.debounceTimers[key]) {
          clearTimeout(this.debounceTimers[key])
        }
        this.debounceTimers[key] = setTimeout(() => {
          func.apply(this, args)
          delete this.debounceTimers[key]
        }, delay)
      }
    },
    // 异步渲染表格
    async renderTableAsync(type) {
      await this.$nextTick()
      // 使用 requestAnimationFrame 确保在下一帧渲染
      return new Promise((resolve) => {
        requestAnimationFrame(() => {
          this.renderedTables.add(type)
          resolve()
        })
      })
    },
    // 优化后的获取表格列配置方法
    getTableColumns(type) {
      return this.tableColumnsConfig[type] || []
    },

    // 通用的span方法生成器
    createSpanMethod(type) {
      // 缓存span方法，避免重复创建
      if (!this._spanMethodCache) {
        this._spanMethodCache = new Map()
      }
      if (this._spanMethodCache.has(type)) {
        return this._spanMethodCache.get(type)
      }
      const spanMethod = ({ rowIndex, columnIndex }) => {
        if (columnIndex === 0) {
          return this.getSpanMethodForMonitorType(type, rowIndex)
        } else if (columnIndex === 1) {
          return this.getSpanMethodForMonitorIndex(type, rowIndex)
        }
        return { rowspan: 1, colspan: 1 }
      }
      this._spanMethodCache.set(type, spanMethod)
      return spanMethod
    },
    // 获取表格跨行方法
    getSpanMethod(type) {
      return this.createSpanMethod(type)
    },
    // 通用的监控类型合并方法
    getSpanMethodForMonitorType(tableType, rowIndex) {
      const data = this.tableData[tableType] || []
      if (data.length === 0 || !data[rowIndex]) {
        return { rowspan: 1, colspan: 1 }
      }
      const currentMonitorType = data[rowIndex].monitorType
      let startIndex = rowIndex
      // 向前查找相同监控类型的起始位置
      while (
        startIndex > 0 &&
        data[startIndex - 1]?.monitorType === currentMonitorType
      ) {
        startIndex--
      }
      if (startIndex !== rowIndex) {
        return { rowspan: 0, colspan: 0 }
      }
      // 向后查找相同监控类型的结束位置
      let endIndex = startIndex
      while (
        endIndex < data.length &&
        data[endIndex]?.monitorType === currentMonitorType
      ) {
        endIndex++
      }
      return { rowspan: endIndex - startIndex, colspan: 1 }
    },
    // 监控指标合并方法
    getSpanMethodForMonitorIndex(tableType, rowIndex) {
      const data = this.tableData[tableType] || []
      if (data.length === 0 || !data[rowIndex]) {
        return { rowspan: 1, colspan: 1 }
      }
      const currentRow = data[rowIndex]
      const { monitorType, monitorIndex } = currentRow
      let startIndex = rowIndex
      // 向前查找相同监控类型和指标的起始位置
      while (
        startIndex > 0 &&
        data[startIndex - 1]?.monitorType === monitorType &&
        data[startIndex - 1]?.monitorIndex === monitorIndex
      ) {
        startIndex--
      }
      if (startIndex !== rowIndex) {
        return { rowspan: 0, colspan: 0 }
      }
      // 向后查找相同监控类型和指标的结束位置
      let endIndex = startIndex
      while (
        endIndex < data.length &&
        data[endIndex]?.monitorType === monitorType &&
        data[endIndex]?.monitorIndex === monitorIndex
      ) {
        endIndex++
      }
      return { rowspan: endIndex - startIndex, colspan: 1 }
    },
    // 加载初始数据
    loadInitialData() {
      // 只加载有数据的类型，并设置为默认展开
      this.activeNames = [...this.availableTypes]
      // 异步加载数据，避免阻塞UI
      this.$nextTick(() => {
        this.activeNames.forEach((type) => {
          this.loadTableDataAsync(type)
        })
      })
      this.previousActiveNames = [...this.activeNames]
    },
    // 刷新当前展开的数据
    refreshActiveData() {
      // 过滤掉不再可用的类型
      this.activeNames = this.activeNames.filter((name) =>
        this.availableTypes.includes(name)
      )
      // 异步刷新数据
      this.$nextTick(() => {
        this.activeNames.forEach((type) => {
          this.loadTableDataAsync(type)
        })
      })

      this.previousActiveNames = [...this.activeNames]
    },
    // 获取表格数据
    fetchTableData(type) {
      this.loadTableDataAsync(type)
    },
    // 处理伸缩框展开变化
    handleCollapseChange: function (activeNames) {
      // 使用防抖处理频繁的展开收缩操作
      return this.debounce(
        'collapseChange',
        (activeNames) => {
          // 确保只包含可用的类型
          const validActiveNames = activeNames.filter((name) =>
            this.availableTypes.includes(name)
          )
          const newlyOpenedItems = validActiveNames.filter(
            (name) => !this.previousActiveNames.includes(name)
          )
          // 异步加载新展开的项，避免阻塞UI
          if (newlyOpenedItems.length > 0) {
            this.$nextTick(() => {
              newlyOpenedItems.forEach((type) => {
                this.loadTableDataAsync(type)
                // 标记表格需要渲染
                this.renderTableAsync(type)
              })
            })
          }
          this.previousActiveNames = [...validActiveNames]
          // 如果过滤后的activeNames与传入的不同，更新activeNames
          if (validActiveNames.length !== activeNames.length) {
            this.$nextTick(() => {
              this.activeNames = validActiveNames
            })
          }
        },
        50
      )(activeNames) // 50ms 防抖延迟
    },
    // 检查是否有有效缓存
    hasValidCache(type) {
      const currentParams = this.buildRequestParams(type)
      const cachedParams = this.requestParamsCache[type]
      return (
        this.tableDataCache[type] &&
        cachedParams &&
        JSON.stringify(currentParams) === JSON.stringify(cachedParams)
      )
    },
    // 构建请求参数
    buildRequestParams(type) {
      const baseParams = {
        dayTimeStart: moment(this.filterCond.dayTime[0]).format('YYYY-MM-DD'),
        dayTimeEnd: moment(this.filterCond.dayTime[1]).format('YYYY-MM-DD'),
        lang: this.$i18n.locale,
      }
      return { ...baseParams, type: type }
    },
    // 异步数据加载方法
    async loadTableDataAsync(type) {
      // 使用 setTimeout 将数据加载放到下一个事件循环
      return new Promise((resolve) => {
        setTimeout(async () => {
          await this.loadTableData(type)
          resolve()
        }, 0)
      })
    },
    // 统一数据加载方法
    async loadTableData(type) {
      const loadingKeys = [type]
      return this.loadDataWithCache(type, type, loadingKeys, (data) => {
        const processedData = this.processTableData(type, data)
        return { [type]: processedData }
      })
    },
    // 统一的缓存数据加载方法
    async loadDataWithCache(type, cacheKey, loadingKeys, processFunc) {
      // 检查缓存
      if (this.hasValidCache(type)) {
        const cachedData = this.tableDataCache[cacheKey].data || []
        const result = processFunc(cachedData)
        this.updateTableDataFromResult(result)
        return
      }
      // 设置加载状态
      this.setLoadingStates(loadingKeys, true)
      try {
        const params = this.buildRequestParams(type)
        const response = await getAlarmStatisticsDetail(params)
        if (response?.data) {
          // 更新缓存
          this.tableDataCache[cacheKey] = {
            data: response.data,
            total: response.data.length,
            timestamp: Date.now(),
          }
          this.requestParamsCache[cacheKey] = { ...params }
          // 处理数据
          const result = processFunc(response.data)
          this.updateTableDataFromResult(result)
        } else {
          this.resetTableData(loadingKeys)
        }
      } catch (error) {
        console.error(`加载 ${type} 数据失败:`, error)
        this.resetTableData(loadingKeys)
      } finally {
        this.setLoadingStates(loadingKeys, false)
      }
    },

    // 更新表格数据
    updateTableDataFromResult(result) {
      // 使用 requestAnimationFrame 确保在合适的时机更新DOM
      requestAnimationFrame(() => {
        Object.keys(result).forEach((key) => {
          this.tableData[key] = result[key]
        })
      })
    },
    // 设置加载状态
    setLoadingStates(keys, loading) {
      keys.forEach((key) => {
        this.loadingStates[key] = loading
      })
    },
    // 重置表格数据
    resetTableData(keys) {
      keys.forEach((key) => {
        this.tableData[key] = []
      })
    },
    // 处理表格数据
    processTableData(type, rawData) {
      const fieldMapper = this.getFieldMapper(type)
      return this.processAlarmData(rawData, fieldMapper)
    },
    // 获取字段映射配置
    getFieldMapper(type) {
      const mappers = {
        Host: {
          defaultMonitorType: '主机监控',
          mapFields: (item) => ({
            host: item.item_0 || `${item.host || ''}(${item.ip || ''})`,
          }),
        },
        Microservice: {
          mapFields: (item) => ({ microService: item.item_0 }),
        },
        OpenSource: {
          mapFields: (item) => ({ node: item.item_0 }),
        },
        DomainCert: {
          mapFields: (item) => ({
            name: item.item_0,
            customerName: item.customerName || '',
          }),
        },
        Business: {
          mapFields: (item) => ({ name: item.item_0 }),
        },
        p2p20: {
          mapFields: (item) => ({ name: item.item_0 }),
        },
      }
      return mappers[type] || { mapFields: () => ({}) }
    },
    // 通用告警数据处理方法
    processAlarmData(rawData, fieldMapper) {
      if (!rawData || !Array.isArray(rawData)) {
        return []
      }
      const groupedData = new Map()
      rawData.forEach((item) => {
        const {
          monitorType = fieldMapper.defaultMonitorType || '',
          monitorTypeDesc = '',
          ctIndexField = '',
          ctIndexFieldDesc,
          item_0 = '',
          count = 0,
        } = item
        const groupKey = `${monitorType}_${ctIndexField}_${item_0}`
        if (!groupedData.has(groupKey)) {
          const baseData = {
            monitorType,
            monitorTypeDesc,
            monitorIndex: ctIndexField,
            ctIndexFieldDesc,
            alarmCount: 0,
            _groupKey: groupKey,
            _items: [],
            ...fieldMapper.mapFields(item),
          }
          groupedData.set(groupKey, baseData)
        }
        const group = groupedData.get(groupKey)
        group.alarmCount += parseInt(count) || 0
        group._items.push(item)
      })
      return this.sortGroupedData(Array.from(groupedData.values()))
    },

    // 通用排序方法
    sortGroupedData(allData) {
      // 直接对所有数据进行多级排序
      return allData.sort((a, b) => {
        // 第一级：按监控类型升序排序
        const monitorTypeComparison = (a.monitorType || '').localeCompare(
          b.monitorType || ''
        )
        if (monitorTypeComparison !== 0) {
          return monitorTypeComparison
        }

        // 第二级：按监控指标升序排序
        const monitorIndexComparison = (a.monitorIndex || '').localeCompare(
          b.monitorIndex || ''
        )
        if (monitorIndexComparison !== 0) {
          return monitorIndexComparison
        }

        // 第三级：按告警次数降序排序
        return b.alarmCount - a.alarmCount
      })
    },

    // 清空缓存
    clearCache() {
      // 清空span方法缓存
      if (this._spanMethodCache) {
        this._spanMethodCache.clear()
      }
      // 清空已渲染表格记录
      this.renderedTables.clear()
      // 清空数据缓存
      this.tableDataCache = {
        Host: null,
        Microservice: null,
        OpenSource: null,
        DomainCert: null,
        Business: null,
        p2p20: null,
      }
      this.requestParamsCache = {
        Host: null,
        Microservice: null,
        OpenSource: null,
        DomainCert: null,
        Business: null,
        p2p20: null,
      }
    },
    async loadSummaryData() {
      if (this.summaryLoading) return
      this.summaryLoading = true
      try {
        const params = {
          dayTimeStart: moment(this.filterCond.dayTime[0]).format('YYYY-MM-DD'),
          dayTimeEnd: moment(this.filterCond.dayTime[1]).format('YYYY-MM-DD'),
          lang: this.$i18n.locale,
        }
        const response = await getAlarmStatisticsSummary(params)
        if (response?.data) {
          // 处理统计数据
          this.statisticsDataObj = response.data.reduce((obj, item) => {
            const {
              type,
              totalCount,
              topOneIndex,
              topOneCount,
              topTwoIndex,
              topTwoCount,
              otherCount,
            } = item
            const parts = [
              `${this.$t('devopsStatisticsTab.count')}: ${totalCount}`,
              topOneIndex ? `${topOneIndex}: ${topOneCount}` : null,
              topTwoIndex ? `${topTwoIndex}: ${topTwoCount}` : null,
              `${this.$t('scheduleTaskLogTab.other')}: ${otherCount}`,
            ].filter(Boolean)

            obj[type] = parts.join(' ')
            return obj
          }, {})
          // 更新可用类型列表 - 只显示有数据的类型
          this.availableTypes = response.data.map((item) => item.type)
          // 过滤掉不再可用的activeNames
          this.activeNames = this.activeNames.filter((name) =>
            this.availableTypes.includes(name)
          )
        } else {
          // 如果没有数据，清空相关状态
          this.statisticsDataObj = {}
          this.availableTypes = []
          this.activeNames = []
        }
      } catch (error) {
        console.error('获取告警统计概要数据失败:', error)
        // 出错时也要清空状态
        this.statisticsDataObj = {}
        this.availableTypes = []
        this.activeNames = []
      } finally {
        this.summaryLoading = false
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.feature-alarm-statics {
  margin: 24px;
  width: calc(100% - 48px);
  height: calc(100vh - 48px);
  position: relative;
  display: flex;
  flex-direction: column;
}

.search-wrapper {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
  z-index: 10;

  .search-btn-box {
    margin-left: 20px;
    display: flex;
    gap: 10px;
  }
}

.metrics-wrapper {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  height: 0;
  padding-bottom: 20px;
}

.metrics-collapse {
  border-bottom: none;

  .metric-card {
    margin-bottom: 20px;

    // 优化：添加过渡动画
    ::v-deep .el-collapse-item__header {
      height: 24px;
      padding: 8px 16px;
      background-color: #fff;
      border-bottom: 1px solid #e4e7ed;
      font-size: 14px;
      font-weight: 500;
      display: flex;
      align-items: center;
      transition: all 0.2s ease-in-out; // 添加过渡效果

      &.is-active {
        .collapse-title {
          color: #429efd;
        }
      }

      .collapse-title-wrapper {
        display: flex;
        align-items: center;
        width: 100%;

        .collapse-title {
          color: #333;
          margin-right: 8px;
          transition: color 0.2s ease-in-out; // 添加颜色过渡
        }

        .collapse-stats {
          color: #ff9800;
          font-weight: normal;
        }
      }
    }

    // 优化：添加内容区域过渡
    ::v-deep .el-collapse-item__wrap {
      border-bottom: none;
      transition: max-height 0.3s ease-in-out; // 添加高度过渡
    }

    ::v-deep .el-collapse-item__content {
      padding: 0;
    }
  }
}

.metric-table-wrapper {
  background: white;
  width: 100%;
  box-sizing: border-box;
  // 优化：添加透明度过渡
  transition: opacity 0.2s ease-in-out;

  .table-header {
    padding: 16px 20px 0 20px;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }

  ::v-deep .tvt-table {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;

    .el-table {
      width: 100%;
      // 优化：提升表格渲染性能
      table-layout: fixed;

      // 优化：减少重绘
      .el-table__body-wrapper {
        transform: translateZ(0); // 启用硬件加速
      }
    }
  }
}

// 优化：添加加载状态样式
.loading-overlay {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 999;
  }
}
</style>
<style lang="scss">
.feature-alarm-statics {
  .el-table__body tr.hover-row > td.el-table__cell {
    background-color: unset !important;
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
    background-color: unset !important;
  }

  .metrics-collapse .el-collapse-item__header .el-collapse-item__arrow {
    margin: 0 8px 0 0;
  }
}
</style>
