/**
 * 请求管理器 - 提供可取消的API请求管理
 * 每个组件实例应该创建独立的RequestManager实例
 */
export class RequestManager {
  constructor() {
    this.controllers = new Map()
  }

  /**
   * 执行可取消的请求
   * @param {string} requestType - 请求类型标识
   * @param {Function} apiCall - API调用函数，接收signal参数
   * @returns {Promise} 请求结果，如果被取消返回null
   */
  async executeRequest(requestType, apiCall) {
    // 取消同类型的旧请求
    if (this.controllers.has(requestType)) {
      this.controllers.get(requestType).abort()
    }

    const controller = new AbortController()
    this.controllers.set(requestType, controller)

    try {
      const result = await apiCall(controller.signal)
      if (controller.signal.aborted) return null
      return result
    } catch (error) {
      if (error.name === 'AbortError') return null
      throw error
    } finally {
      this.controllers.delete(requestType)
    }
  }

  /**
   * 取消指定类型的请求
   * @param {string} requestType - 请求类型标识
   */
  cancelRequest(requestType) {
    const controller = this.controllers.get(requestType)
    if (controller) {
      controller.abort()
      this.controllers.delete(requestType)
    }
  }

  /**
   * 取消所有未完成的请求
   */
  cancelAllRequests() {
    this.controllers.forEach((controller) => controller.abort())
    this.controllers.clear()
  }

  /**
   * 获取当前活跃的请求类型列表（调试用）
   * @returns {Array} 活跃请求类型数组
   */
  getActiveRequests() {
    return Array.from(this.controllers.keys())
  }
}
