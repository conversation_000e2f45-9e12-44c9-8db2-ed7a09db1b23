<template>
  <tvt-dialog
    :title="$t('edit')"
    :show.sync="showFlag"
    width="1350px"
    custom-class="alarm-config-dialog"
    :cancel-text="$t('cancel')"
    :submit-text="$t('confirm')"
    :close-on-click-modal="false"
    @close="closeDialog"
    @Cancel="closeDialog"
    @Submit="btnSave"
  >
    <div class="alarm-edit-wrap">
      <el-form
        ref="configForm"
        :inline="true"
        :model="alarmConfigForm"
        label-position="left"
        :rules="alarmRules"
        label-width="115px"
      >
        <div class="common-config-wrap">
          <el-row>
            <el-col :span="12">
              <el-form-item
                prop="ctType"
                :label="$t('alarmConfigTab.alarmType')"
                class="disable-form-item"
              >
                <tvt-select
                  v-model="alarmConfigForm.ctType"
                  :options="ctyTypeOptions"
                  :placeholder="$t('alarmConfigTab.alarmTypeSelect')"
                  clearable
                  disabled
                  style="width: 470px"
                />
              </el-form-item>
            </el-col>
            <!-- 隐藏采集周期 -->
            <!-- <el-col :span="12">
              <el-form-item
                prop="ctPeriod"
                :label="$t('alarmConfigTab.ctPeriod')"
              >
                <tvt-input
                  v-model.number="alarmConfigForm.ctPeriod"
                  :placeholder="$t('alarmConfigTab.ctPeriodInput')"
                  style="width: 470px"
                >
                  <template slot="append">
                    {{ alarmConfigForm.ctPeriodUnit }}
                  </template>
                </tvt-input>
              </el-form-item>
            </el-col> -->
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item
                prop="alarmReceiver"
                :label="$t('alarmConfigTab.alarmReceiver')"
              >
                <!-- <tvt-input
                  v-model="alarmConfigForm.alarmEmail"
                  type="textarea"
                  rows="2"
                  :placeholder="$t('alarmConfigTab.alarmEmailInput')"
                  style="width: 850px"
                /> -->
                <el-button
                  type="primary"
                  style="border-radius: 16px; height: 36px"
                  @click="handleAdd"
                  >{{ $t('add') }}</el-button
                >
                <tvt-table
                  ref="tvtTableRef"
                  v-loading="tableLoading"
                  :data="alarmConfigForm.alarmReceivers"
                  :columns="alarmReceiverColumns"
                  :border="true"
                  :border-bottom="true"
                  height="180"
                  style="width: 1085px"
                  :header-cell-style="{ 'text-align': 'center' }"
                  :pagination="false"
                >
                  <!--可编辑列-->
                  <template #bodyCell="{ column, $index }">
                    <template v-if="column.type === 'input'">
                      <el-form-item :prop="`${column.prop}_${$index}`">
                        <tvt-input
                          v-model="
                            alarmConfigForm.alarmReceivers[$index][column.prop]
                          "
                          :placeholder="`${
                            column.prop === 'email'
                              ? $t('alarmConfigTab.alarmEmailInput')
                              : $t('alarmConfigTab.alarmSmsInput')
                          }`"
                          maxlength="100"
                          style="width: 316px"
                        />
                      </el-form-item>
                    </template>
                    <template v-else-if="column.type === 'select'">
                      <el-form-item :prop="`${column.prop}_${$index}`">
                        <tvt-select
                          v-model="
                            alarmConfigForm.alarmReceivers[$index][column.prop]
                          "
                          :options="langOptions"
                          :placeholder="`${$t(
                            'alarmConfigTab.alarmLangSelect'
                          )}`"
                          clearable
                          style="width: 300px"
                        />
                      </el-form-item>
                    </template>
                    <template v-else>
                      <el-button
                        type="danger"
                        style="border-radius: 16px; height: 36px"
                        @click="handleDelete($index)"
                        >{{ $t('delete') }}</el-button
                      >
                    </template>
                  </template>
                </tvt-table>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="24">
              <el-form-item
                prop="alarmSms"
                :label="$t('alarmConfigTab.alarmSms')"
              >
                <tvt-input
                  v-model="alarmConfigForm.alarmSms"
                  type="textarea"
                  rows="2"
                  :placeholder="$t('alarmConfigTab.alarmSmsInput')"
                  style="width: 850px"
                />
              </el-form-item>
            </el-col> -->
          </el-row>
        </div>
        <el-row>
          <el-col :span="24">
            <div class="alarm-config-table">
              <tvt-table
                ref="tvtTableRef"
                v-loading="tableLoading"
                :data="alarmConfigForm.editConfigs"
                :columns="columns"
                :border="true"
                :border-bottom="true"
                height="350"
                style="width: 1200px"
                :header-cell-style="{ 'text-align': 'center' }"
                :pagination="false"
              >
                <!--可编辑列-->
                <template #bodyCell="{ row, column, $index }">
                  <!-- 普通阈值、告警阈值需要区分数值输入还是选择 -->
                  <template
                    v-if="['threshold', 'alarmThreshold'].includes(column.prop)"
                  >
                    <template
                      v-if="['Double', 'Long'].includes(row.alarmIndexType)"
                    >
                      <el-form-item :prop="`${column.prop}_${$index}`">
                        <el-input-number
                          v-model.number="
                            alarmConfigForm.editConfigs[$index][column.prop]
                          "
                          :placeholder="$t('input')"
                          :min="
                            Number(
                              alarmConfigForm.editConfigs[$index][
                                'minThreshold'
                              ] || 0
                            )
                          "
                          :max="
                            Number(
                              alarmConfigForm.editConfigs[$index][
                                'maxThreshold'
                              ] || Infinity
                            )
                          "
                          controls-position="right"
                          style="width: 160px"
                        />
                      </el-form-item>
                    </template>
                    <template v-else>
                      <el-form-item :prop="`${column.prop}_${$index}`">
                        <tvt-select
                          v-model="
                            alarmConfigForm.editConfigs[$index][column.prop]
                          "
                          :options="getStatusOptions(row.alarmIndexType)"
                          :placeholder="$t('choose')"
                          clearable
                          style="width: 100%"
                        />
                      </el-form-item>
                    </template>
                  </template>
                  <!-- 采集时长、发送限制直接用数值输入框 -->
                  <template v-else>
                    <el-form-item :prop="`${column.prop}_${$index}`">
                      <el-input-number
                        v-model.number="
                          alarmConfigForm.editConfigs[$index][column.prop]
                        "
                        :placeholder="$t('input')"
                        :min="
                          ['hourLimits', 'dayLimits'].includes(column.prop)
                            ? 1
                            : 0
                        "
                        :max="
                          ['hourLimits', 'dayLimits'].includes(column.prop)
                            ? 100
                            : Infinity
                        "
                        controls-position="right"
                        style="width: 160px"
                      />
                    </el-form-item>
                  </template>
                </template>
              </tvt-table>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div slot="footer" class="alarm-dialog-footer">
      <el-button size="medium" @click="closeDialog">{{
        $t('cancel')
      }}</el-button>
      <el-button
        size="medium"
        :loading="loading"
        type="primary"
        @click="btnSave"
        >{{ $t('confirm') }}</el-button
      >
    </div>
  </tvt-dialog>
</template>

<script>
import { ctyTypeOptions } from '@/views/system-alarm/config.js'
import { langOptions } from '@/lang'
import { deepCopy } from '@/utils/common'
export default {
  name: 'AlarmConfigEdit',
  data() {
    return {
      langOptions,
      ctyTypeOptions, //包标签下拉框
      showFlag: false,
      columns: [
        {
          label: this.$t('alarmConfigTab.alarmIndexDesc'),
          prop: 'alarmIndexDesc',
          'min-width': 180,
        },
        {
          label: this.$t('alarmConfigTab.threshold'),
          prop: 'threshold',
          slotName: 'bodyCell',
          'min-width': 150,
        },
        {
          label: this.$t('alarmConfigTab.alarmThreshold'),
          prop: 'alarmThreshold',
          slotName: 'bodyCell',
          'min-width': 150,
        },
        {
          label: this.$t('alarmConfigTab.collectTime'),
          prop: 'collectTime',
          slotName: 'bodyCell',
          'min-width': 150,
        },
        {
          label: this.$t('alarmConfigTab.hourLimits'),
          prop: 'hourLimits',
          slotName: 'bodyCell',
          'min-width': 150,
        },
        {
          label: this.$t('alarmConfigTab.dayLimits'),
          prop: 'dayLimits',
          slotName: 'bodyCell',
          'min-width': 150,
        },
      ],
      alarmReceiverColumns: [
        {
          label: this.$t('alarmConfigTab.alarmEmail'),
          prop: 'email',
          slotName: 'bodyCell',
          type: 'input',
          width: 332,
        },
        {
          label: this.$t('alarmConfigTab.alarmSms'),
          prop: 'sms',
          slotName: 'bodyCell',
          type: 'input',
          width: 332,
        },
        {
          label: this.$t('alarmConfigTab.alarmLang'),
          prop: 'lang',
          slotName: 'bodyCell',
          type: 'select',
          width: 320,
        },
        {
          label: this.$t('operation'),
          prop: 'operation',
          slotName: 'bodyCell',
          width: 100,
        },
      ],
      alarmConfigForm: {
        ctType: '',
        ctPeriod: '',
        alarmEmail: '',
        alarmSms: '',
        editConfigs: [],
        alarmReceivers: [],
      },
      alarmRules: {},
      tableLoading: false,
      alarmConfigList: [],
      statusOptions: [
        { label: 'success', value: 'success' },
        { label: 'fail', value: 'fail' },
      ],
      loading: false,
    }
  },
  methods: {
    open(record) {
      this.alarmConfigForm = deepCopy(record)
      // console.log('record', record)
      this.showFlag = true
    },
    getStatusOptions(alarmIndexType) {
      if (alarmIndexType.indexOf(',') > -1) {
        return alarmIndexType
          .split(',')
          .map((item) => ({ label: item, value: item }))
      }
      return this.statusOptions
    },
    btnSave() {
      // this.$emit('btnSave', this.filterCond.packageTag, this.multipleSelection, this.deployType)
      this.$refs.configForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.$emit('btnSave', this.alarmConfigForm)
        }
      })
    },
    closeDialog() {
      this.showFlag = false
      this.loading = false
      this.alarmConfigForm = {
        ctType: '',
        ctPeriod: '',
        alarmEmail: '',
        alarmSms: '',
        editConfigs: [],
        alarmReceivers: [],
      }
    },
    handleAdd() {
      const { alarmReceivers = [] } = this.alarmConfigForm
      if (alarmReceivers.length >= 10) {
        this.$message.warning(this.$t('alarmConfigTab.alarmReceiverLimit'))
        return
      }
      this.alarmConfigForm = {
        ...this.alarmConfigForm,
        alarmReceivers: [
          ...alarmReceivers,
          { alarmEmail: '', alarmSms: '', alarmLang: '' },
        ],
      }
    },
    handleDelete(index) {
      const alarmReceivers = [...(this.alarmConfigForm.alarmReceivers || [])]
      alarmReceivers.splice(index, 1)
      this.alarmConfigForm = {
        ...this.alarmConfigForm,
        alarmReceivers: [...alarmReceivers],
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.alarm-edit-wrap {
  flex: 1;
  padding: 20px;
  .common-config-wrap {
    width: 100%;
  }
}

.alarm-config-table {
  width: 100%;
}

::v-deep .el-dialog__wrapper {
  align-items: start;
  .el-dialog {
    margin-top: 15vh !important;
  }
}

::v-deep .alarm-config-dialog .common-config-wrap .el-form-item__content {
  line-height: 34px;
}

::v-deep .alarm-config-dialog .common-config-wrap .el-form-item {
  margin-bottom: 18px;
}

::v-deep .alarm-config-dialog .common-config-wrap .el-form-item__label {
  text-align: justify; // label标签占满
  text-align-last: justify;
}

::v-deep
  .alarm-config-dialog
  .el-table
  td.el-table__cell
  div
  .el-form-item__content {
  line-height: 34px;
}

::v-deep .alarm-config-dialog .el-table td.el-table__cell div .el-form-item {
  margin-top: 10px;
  margin-bottom: 10px;
}

::v-deep
  .alarm-config-dialog
  .el-table
  td.el-table__cell
  div
  .el-form-item__error {
  top: 70%;
}

::v-deep .alarm-config-dialog .el-table .el-table__body .el-table__cell {
  padding: 0px;
}
::v-deep .alarm-config-dialog .disable-form-item .tvt-select .tvt-select-inner {
  background-color: #f5f7fa;
}
::v-deep .alarm-config-dialog .alarm-dialog-footer {
  display: inline-block;
  width: 100%;
  height: 52px;
  text-align: right;
  border-top: 1px solid #e0e0e0;
  padding: 10px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-size: 14px;
  padding: 5px 20px;
  button {
    border-radius: 16px;
    height: 36px;
  }
}
</style>
<style>
.alarm-config-dialog .el-table__body tr.hover-row > td.el-table__cell {
  background-color: unset !important;
}
.alarm-config-dialog
  .el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell {
  background-color: unset !important;
}
.alarm-config-dialog .el-table__body .cell {
  white-space: pre-line;
}
</style>
