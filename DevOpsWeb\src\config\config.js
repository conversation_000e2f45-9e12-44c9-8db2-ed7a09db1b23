import Vue from 'vue'

export const homeSearchConfig = (handle) => ({
  condList: [
    {
      type: 'TvtSelect',
      paramKey: 'area',
      options: [{ label: '全部', value: '全部' }],
      label: '',
      placeholder: Vue.prototype.$translate('areaSelect'),
    },
    {
      type: 'DatePicker',
      paramKey: 'date',
      dateType: 'date',
      label: '',
      placeholder: Vue.prototype.$translate('selectTime'),
    },
  ],
  buttonList: [
    {
      type: 'primary',
      btnFuncType: 'search',
      text: Vue.prototype.$translate('search'),
      handle: handle,
    },
  ],
})

export const monitorSearchConfig = (handle) => ({
  condList: [
    {
      type: 'TvtSelect',
      paramKey: 'area',
      options: [{ label: '全部', value: '全部' }],
      label: '',
      placeholder: Vue.prototype.$translate('areaSelect'),
    },
    {
      type: 'DatePicker',
      paramKey: 'date',
      dateType: 'date',
      label: '',
      placeholder: Vue.prototype.$translate('selectTime'),
    },
  ],
  buttonList: [
    {
      type: 'primary',
      btnFuncType: 'search',
      text: Vue.prototype.$translate('search'),
      handle: handle,
    },
  ],
})
