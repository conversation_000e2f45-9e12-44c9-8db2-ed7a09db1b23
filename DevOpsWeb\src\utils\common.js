import store from '../store/index'
import i18n from '../lang/index.js'
//翻译拦截处理
export function getTranslateValue(key, obj) {
  let value = i18n.t(key, obj)
  if (value == '' || value == key) {
    //翻译缺失时的替换语言
    value = i18n.t(key, store.state.defaultLang, obj)
  }
  return value
}

// 防抖函数
export function debounce(fn, delay = 1000) {
  let timeout = null
  return function (...args) {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 节流函数
export function throttle(fn, wait = 1000) {
  let pre = Date.now()
  return function (...args) {
    const context = this
    const now = Date.now()
    // console.log('触发重新计算布局11', pre, now)
    if (now - pre >= wait) {
      // console.log('真正触发变化',  pre, now, wait)
      fn.apply(context, args)
      pre = Date.now()
    }
  }
}

/**
 * 密码强度为中和高，限制字符数为8-16个，由（大小写，数字，和特殊字符）组合，
 * 强度为中时最少为两个类型组合，
 * 强度为高时最少为四个类型组合。
 */
export function checkPwdStrongStrength(value) {
  var reg = /^(.){8,16}$/
  var reg1 = /[a-z]+/
  var reg2 = /[A-Z]+/
  var reg3 = /[0-9]+/
  var reg4 = /[!"#$%&'()*+,\-./:;<=>?@[\]^`{|}~_]+/
  var sum = 0
  if (reg.test(value)) {
    if (reg1.test(value)) {
      sum += 1
    }
    if (reg2.test(value)) {
      sum += 1
    }
    if (reg3.test(value)) {
      sum += 1
    }
    if (reg4.test(value)) {
      sum += 1
    }
  }
  if (sum >= 2) {
    return true
  } else {
    return false
  }
}

// 遍历对象里面的值是否为空
export function addObjKey(obj) {
  let param = {}
  var arr = Object.getOwnPropertyNames(obj)
  if (arr.length == 0) {
    return obj
  } else {
    for (let key in obj) {
      if (obj[key]) {
        param[key] = obj[key]
      }
    }
    return param
  }
}

// 时间戳转字符串时间  2022-08-09 19:55:40
export function stampToStrLong(date) {
  if (!date) {
    return '--'
  }
  let time = new Date(date)
  let y = time.getFullYear()
  let m =
    time.getMonth() + 1 <= 9 ? '0' + (time.getMonth() + 1) : time.getMonth() + 1
  let d = time.getDate() <= 9 ? '0' + time.getDate() : time.getDate()
  let hour = time.getHours() <= 9 ? '0' + time.getHours() : time.getHours()
  let minute =
    time.getMinutes() <= 9 ? '0' + time.getMinutes() : time.getMinutes()
  let second =
    time.getSeconds() <= 9 ? '0' + time.getSeconds() : time.getSeconds()
  return y + '-' + m + '-' + d + ' ' + hour + ':' + minute + ':' + second
}

// [{label: '', value: ''}] 转 {value: label}
export function transArrToObj(objArr) {
  if (!objArr || !Array.isArray(objArr)) return {}
  let obj = {}
  objArr.forEach((item) => {
    if (item && item.value) {
      obj[item.value] = item.label
    }
  })
  return obj
}

// 对象深拷贝
export function deepCopy(obj) {
  let target = null
  if (typeof obj === 'object') {
    if (Array.isArray(obj)) {
      //数组
      target = []
      obj.forEach((item) => {
        target.push(deepCopy(item))
      })
    } else if (obj) {
      target = {}
      let objKeys = Object.keys(obj)
      objKeys.forEach((key) => {
        target[key] = deepCopy(obj[key])
      })
    } else {
      target = obj
    }
  } else {
    target = obj
  }
  return target
}

// 生成一天的起止时间 00:00:00 —— 23:59:59
export const dateTimeRange = (date, num = 0, format) => {
  const endTime = (date ? window.moment(date) : window.moment()).set({
    hour: 23,
    minute: 59,
    second: 59,
  })
  const startTime = (date ? window.moment(date) : window.moment())
    .subtract(num, 'd')
    .set({ hour: 0, minute: 0, second: 0 })
  if (!format) {
    return [startTime.toDate(), endTime.toDate()]
  } else {
    return [startTime.format(format), endTime.format(format)]
  }
}

// 生成一天起止时间的时间戳 00:00:00 —— 23:59:59
export const dateTimeRangeStamp = (date, num = 0) => {
  const endTime = (date ? window.moment(date) : window.moment()).set({
    hour: 23,
    minute: 59,
    second: 59,
  })
  const startTime = (date ? window.moment(date) : window.moment())
    .subtract(num, 'd')
    .set({ hour: 0, minute: 0, second: 0 })
  return [startTime.valueOf(), endTime.valueOf()]
}

// 过去一小时
export const dateTimeRangeHour = (num = 1) => {
  const endTime = window.moment()
  const startTime = window.moment().subtract(num, 'hours')
  return [startTime.valueOf(), endTime.valueOf()]
}

// 按钮失焦
export const btnBlur = (e) => {
  // 按钮失焦
  let target = e.target
  if (target.nodeName === 'SPAN') {
    target = e.target.parentNode
  }
  target.blur()
}

// 兼容IE的下载方法
export const hanldeDownload = (blob, fileName) => {
  if (window.navigator.msSaveOrOpenBlob) {
    // if browser is IE
    // filename文件名包括扩展名，下载路径为浏览器默认路径
    navigator.msSaveBlob(blob, fileName)
  } else {
    let link = document.createElement('a')
    let url = window.URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', fileName)
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    setTimeout(function () {
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    }, 1000)
  }
}

// 处理折线图数据的方法
export const dealLineChartData = (
  params,
  res,
  clusterCodeNameObj,
  refreshTime
) => {
  if (res.basic && res.basic.code == 200) {
    if (!res.data) return
    const { metrics } = params
    // 折线图逻辑
    // 遍历res.data按照clusterCode分成多个对象
    const areas = []
    const dataObj = {}
    res.data.forEach((clusterItem) => {
      const { clusterCode, monitorDataOutVo } = clusterItem
      // 解析返回结果，生成按照采集项分类的数据
      const { fields, datas, topData = [], topDisplayNum } = monitorDataOutVo
      // 遍历fields找到每个指标的下标
      const fieldIndexObj = {}
      fields.forEach((item, index) => {
        fieldIndexObj[item] = index
      })
      let seriesName = Object.keys(datas).sort() // 用作series分类依据
      if (topData && topData.length > 0) {
        // 如果有排序，则按照排序顺序来
        seriesName = topData
      }
      // 先遍历所有数据取出st （时间） 用作x轴 有些是用其他用作x轴
      const xAxisField = 'st'
      const stIndex = fieldIndexObj[xAxisField]
      const stSet = seriesName.reduce((pre, next) => {
        const nextData = datas[next] || []
        nextData.forEach((item2) => {
          const st =
            xAxisField === 'st'
              ? stampToStrLong(parseInt(item2[stIndex]) * 1000)
              : item2[stIndex] // 转换成YYYY-MM-DD HH:mm:ss的形式
          item2[stIndex] = st
          pre.add(st)
        })
        return pre
      }, new Set())
      // console.log('stSet', stSet)
      const stSortArr = Array.from(stSet).sort() // 升序排列
      // const stArr = stSortArr.map((item) => item.split(' ')[1]) // 取出HH:mm:ss
      // this.xAxis = ctSortArr
      // 遍历ctSortArr确定每个时刻的index
      const stIndexObj = {}
      stSortArr.forEach((item, index) => {
        stIndexObj[item] = index
      })
      const seriesData = [] // 整体series的对象，每个指标项对应一个key  [{ name: '主机1'， data: [...] }, { name: '主机2'， data: [...] }]
      // 遍历seriesName,生成数据
      seriesName.forEach((name) => {
        // 找到
        if (metrics.length > 1) {
          // 说明是类似'loadOne', 'loadFive', 'loadFifteen'这种三个放在一块的，需要在series层面额外加一级 [{ name: '主机1-loadOne'， data: [...] }, { name: '主机1-loadFive'， data: [...] }, { name: '主机1-loadFifteen'， data: [...] }]
          metrics.forEach((metric) => {
            const temp = {
              name: name + '-' + metric,
              data: new Array(stSortArr.length).fill(undefined), // 按照所有时刻生成对应的数据数组
            }
            seriesData.push(temp)
          })
        } else {
          const temp = {
            name,
            data: new Array(stSortArr.length).fill(undefined), // 按照所有时刻生成对应的数据数组
          }
          seriesData.push(temp)
        }
      })
      // 遍历seriesName，找到每个原始数据，再遍历之后将数据生成图所需要的数据

      seriesName.forEach((name, index) => {
        const seriseData = datas[name] || []
        seriseData.forEach((item) => {
          if (metrics.length > 1) {
            // 说明是类似'loadOne', 'loadFive', 'loadFifteen'这种三个放在一块的
            const len = metrics.length
            metrics.forEach((metric, index2) => {
              const stSeriesIndex = stIndexObj[item[stIndex]]
              const value = metric
              const quotaIndex = fieldIndexObj[value] // 具体指标数值所在的下标
              seriesData[index * len + index2].data[stSeriesIndex] =
                item[quotaIndex]
            })
          } else {
            const stSeriesIndex = stIndexObj[item[stIndex]]
            const value = metrics[0]
            const quotaIndex = fieldIndexObj[value] // 具体指标数值所在的下标
            // console.log('value', value, 'fieldIndexObj', fieldIndexObj, 'quotaIndex', quotaIndex, 'item[quotaIndex]', item[quotaIndex])
            // value对应指标（CPU利用率） index对应主机的下标，ctSeriesIndex对应ct在xAxis中的下标
            seriesData[index].data[stSeriesIndex] = item[quotaIndex]
          }
        })
      })
      // 如果metrics长度大于0，需要把series上的名称rdc-dev40-01-loadOne换成国际化的名称rdc-dev40-01-1分钟平均负载
      if (metrics.length > 1) {
        const len = metrics.length
        seriesName.forEach((name, index) => {
          metrics.forEach((metric, index2) => {
            seriesData[index * len + index2].name = name + '-' + i18n.t(metric)
          })
        })
      }
      const newSeriesData = [...seriesData]
      dataObj[clusterCode] = {
        xAxis: stSortArr,
        seriesData: newSeriesData,
        topData,
        refreshTime,
        topDisplayNum,
      }
      // 更新areas选项
      if (clusterCode) {
        areas.push({
          value: clusterCode,
          label: clusterCodeNameObj[clusterCode],
        })
      } else {
        areas.push(undefined)
      }
    })
    // console.log('dataObj', dataObj, 'time', Date.now())
    // this.dataObj = dataObj
    // this.areas = areas
    return { dataObj, areas }
  }
}

/**
 *
 * @param 要转换的毫秒数
 * @return 该毫秒数转换为 * days * hours * minutes * seconds 后的格式
 */
export const formatDuring = (mss) => {
  const days = parseInt(mss / (1000 * 60 * 60 * 24))
  const hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = parseInt((mss % (1000 * 60)) / 1000)
  let timeStr = ''
  if (days) {
    timeStr += `${days}${days > 1 ? i18n.t('days') : i18n.t('day')}`
  }
  if (hours) {
    timeStr += `${hours}${hours > 1 ? i18n.t('hours') : i18n.t('hour')}`
  }
  if (minutes) {
    timeStr += `${minutes}${minutes > 1 ? i18n.t('minutes') : i18n.t('minute')}`
  }
  if (seconds) {
    timeStr += `${seconds}${seconds > 1 ? i18n.t('seconds') : i18n.t('second')}`
  }
  return timeStr
}

export const initTvtLoading = (Vue, i18n, TvtLoading) => {
  function getTranslateValue(key, obj) {
    let value = i18n.t(key, obj)

    return value
  }
  const LoadingConstructor = Vue.extend(TvtLoading)

  const instance = new LoadingConstructor({
    el: document.createElement('div'),
    data() {
      return {
        text: '', // 文本内容
        imgSrc: '', // 加载效果图片
      }
    },
  })

  instance.show = false
  const loading = {
    show(obj) {
      instance.show = true
      if (obj) {
        instance.imgSrc = obj.imgSrc
        instance.text = obj.text
        instance.id = obj.id
      } else {
        instance.text = getTranslateValue('LoadingMsg')
      }
      document.body.appendChild(instance.$el)
    },
    hide() {
      instance.show = false
    },
  }

  Vue.prototype.$TvtLoading = loading
}

// 时间控件公共方法
export const commonShortcuts = [
  {
    text: i18n.t('yesterday'),
    onClick(picker) {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24)
      picker.$emit('pick', [date, date])
    },
  },
  {
    text: i18n.t('weekAgo'),
    onClick(picker) {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
      picker.$emit('pick', [date, date])
    },
  },
  {
    text: i18n.t('last7d'),
    onClick(picker) {
      const startDate = new Date()
      const endData = new Date()
      startDate.setTime(startDate.getTime() - 3600 * 1000 * 24 * 6)
      picker.$emit('pick', [startDate, endData])
    },
  },
  {
    text: i18n.t('last30d'),
    onClick(picker) {
      const startDate = new Date()
      const endData = new Date()
      startDate.setTime(startDate.getTime() - 3600 * 1000 * 24 * 29)
      picker.$emit('pick', [startDate, endData])
    },
  },
]
