<template>
  <div>
    <tvt-dialog
      :title="title"
      :show.sync="showFlag"
      width="1280px"
      :cancel-text="$t('cancel')"
      :submit-text="$t('confirm')"
      :close-on-click-modal="false"
      @close="closeDialog"
      @Cancel="closeDialog"
      @Submit="btnSave"
    >
      <div slot="footer" class="dialog-footer footer-border">
        <el-button @click="closeDialog">
          {{ $t('cancel') }}
        </el-button>
        <el-button type="primary" :loading="showLoading" @click="btnSave">
          {{ $t('confirm') }}
        </el-button>
      </div>
      <div class="host-initialization">
        <div class="search-div">
          <search-input
            v-model="filterCond.hostNameOrIp"
            :placeholder="$t('pleaseEnter', { type: filterName })"
            clearable
            maxlength="32"
            style="width: 250px; margin-right: 20px"
            @change="getHostList()"
          />
        </div>
        <div class="table-wrapper1">
          <tvt-table
            ref="tableSelection"
            v-myLoading="loading"
            :data="hostList"
            :columns="columns"
            :border-bottom="true"
            max-height="500"
            :pagination="false"
            @select-all="handleSelectAll"
            @select="handleSelect"
          >
            <template #bodyCell="{ row, column }">
              <template v-if="column.prop === 'hostInit'">
                <span>{{ dealStatus(row.hostInit) }}</span>
              </template>
              <template v-if="column.prop === 'nfsInit'">
                <span>{{ dealNfsStatus(row.nfsInit) }}</span>
              </template>
              <template v-if="column.prop === 'installStatus'">
                <span>{{ dealNfsStatus(row.installStatus) }}</span>
              </template>
              <template v-if="column.prop === 'updateTime'">
                <span>{{ stampToStrLongMethod(row.updateTime) }}</span>
              </template>
            </template>
          </tvt-table>
        </div>
      </div>
    </tvt-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getInitHostList, excuteInitHost } from '@/api/installDeploy.js'
import { addObjKey, debounce, stampToStrLong } from '@/utils/common'
import { getLocale } from '@/lang'

const lang = getLocale() || 'zh-CN'

export default {
  name: 'ExecuteDeployment',
  data() {
    return {
      title: '',
      filterName: '',
      showFlag: false,
      packageTagList: [],
      hostList: [],
      columns: [],
      total: 0,
      filterCond: {
        packageTag: '',
        projectName: '',
      },
      loading: false,
      showLoading: false,
      multipleSelection: [],
    }
  },
  computed: {
    ...mapState('params', ['environmentCode', 'dcName']),
  },
  methods: {
    open() {
      this.showFlag = true
      this.filterCond = {
        packageTag: '',
      }
      this.packageTagList = []
      this.multipleSelection = []
      this.hostList = []
      this.columns = [
        {
          type: 'selection',
          width: 60,
          fixed: true,
          selectable: this.selectHandle,
        },
        {
          label: this.$t('hostTab.hostName'),
          prop: 'hostName',
          'min-width': lang === 'zh-CN' ? 150 : 140,
        },
        {
          label: this.$t('hostTab.hostIp'),
          prop: 'hostIp',
          width: lang === 'zh-CN' ? 150 : 120,
        },
        {
          label: this.$t('hostTab.hostInit'),
          prop: 'hostInit',
          slotName: 'bodyCell',
          width: lang === 'zh-CN' ? 150 : 200,
        },
        {
          label: this.$t('hostTab.nfsInit'),
          prop: 'nfsInit',
          slotName: 'bodyCell',
          width: lang === 'zh-CN' ? 150 : 180,
        },
        {
          label: this.$t('hostTab.installStatus'),
          prop: 'installStatus',
          slotName: 'bodyCell',
          width: lang === 'zh-CN' ? 150 : 220,
        },
        {
          label: this.$t('hostTab.agentVersion'),
          prop: 'agentVersion',
          width: lang === 'zh-CN' ? 160 : 160,
        },
        {
          label: this.$t('hostTab.updateTime'),
          prop: 'updateTime',
          slotName: 'bodyCell',
          width: lang === 'zh-CN' ? 170 : 160,
        },
      ]
      this.title = this.$t('hostTab.hostInitialization')
      this.filterName = this.$t('hostTab.searchName')
      this.getHostList()
    },
    getHostList: debounce(function () {
      this.loading = true
      let data = addObjKey(this.filterCond)
      this.multipleSelection = []
      getInitHostList(data)
        .then((res) => {
          this.loading = false
          if (res.basic && res.basic.code == 200) {
            if (res.data) {
              // 展示所有数据
              this.hostList = res.data
              this.total = parseInt(res.data.total)
            } else {
              this.hostList = []
            }
          }
        })
        .catch((err) => {
          this.loading = false
          console.log(err)
        })
    }, 500),
    // 表格行勾选限制
    selectHandle(row) {
      return row.initFinish === false
    },
    // 表格全选
    handleSelectAll() {
      const count = this.multipleSelection.length
      if (count === 0) {
        // 选中的为空，全选
        this.multipleSelection = this.hostList
          .slice()
          .filter((row) => row.initFinish === false)
      } else {
        this.multipleSelection = []
      }
      this.hostList.forEach((row) => {
        // initFinish为false的可以选中或去掉选中
        if (row.initFinish === false) {
          console.log('row', row, 'flag', !count)
          this.$refs.tableSelection.$refs.tvtTableElTableRef.toggleRowSelection(
            row,
            !count
          )
        }
      })
    },
    handleSelect(_, row) {
      let newSelection = this.multipleSelection.slice()
      const { id } = row
      let flag = true
      if (newSelection.findIndex((item) => item.id == id) > -1) {
        // 全部去除勾选
        this.multipleSelection = []
        flag = false
      } else {
        // 全部勾选
        this.multipleSelection = this.hostList
          .slice()
          .filter((row) => row.initFinish === false)
        flag = true
      }
      this.hostList.forEach((row) => {
        // initFinish为false的可以选中或去掉选中
        if (row.initFinish === false) {
          // console.log('row', row, 'flag', flag)
          this.$refs.tableSelection.$refs.tvtTableElTableRef.toggleRowSelection(
            row,
            flag
          )
        }
      })
    },
    btnSave() {
      if (this.multipleSelection.length == 0) {
        this.$message.error(this.$t('versionTab.needChooseList'))
        return false
      }
      this.showLoading = true
      const ipList = this.multipleSelection.map((item) => item.hostIp)
      excuteInitHost(ipList)
        .then(() => {
          // 执行主机初始化
          // console.log('res', res)
          // 重新请求列表
          this.multipleSelection = []
          setTimeout(() => {
            this.showLoading = false
          }, 200)
          this.getHostList()
        })
        .catch((err) => {
          setTimeout(() => {
            this.showLoading = false
          }, 200)
          console.log(err)
        })
    },
    closeDialog() {
      this.showFlag = false
    },
    // 回显状态
    dealStatus(status) {
      const statusObj = {
        N: this.$t('hostTab.statusN'),
        Y: this.$t('hostTab.statusY'),
      }
      return statusObj[status]
    },
    dealNfsStatus(status) {
      const statusObj = {
        N: this.$t('hostTab.nfsStatusN'),
        Y: this.$t('hostTab.nfsStatusY'),
      }
      return statusObj[status]
    },
    stampToStrLongMethod(time) {
      let intTime = parseInt(time) //接口给的字符串时间
      return stampToStrLong(intTime)
    },
  },
}
</script>
<style lang="scss" scoped>
.host-initialization {
  width: 100%;
  flex: 1;
  padding: 20px;
  .search-div {
    width: 100%;
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}
::v-deep .el-dialog__wrapper {
  align-items: start;
  .el-dialog {
    margin-top: 15vh !important;
  }
}
</style>
