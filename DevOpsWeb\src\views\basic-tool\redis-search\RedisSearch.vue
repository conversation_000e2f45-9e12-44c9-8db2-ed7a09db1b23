<template>
  <!-- redis日志 -->
  <div class="page-wrapper">
    <div class="search-wrapper">
      <tvt-select
        v-model="filterCond.redisInstance"
        :options="redisInstanceOptions"
        :mockplaceholder="$t('redisSearchTab.instanceSelect')"
        :clearable="false"
        style="width: 250px; margin-right: 20px"
        @change="handleRedisInstance"
      />
      <!-- 暂时去除关键字查询 -->
      <!-- <search-input
        v-model="filterCond.redisKey"
        :placeholder="$t('kafkaSearchTab.keywordInput')"
        clearable
        size="small"
        maxlength="32"
        style="width: 200px; margin-right: 20px"
        @change="getRedisKeyList"
      /> -->
      <div class="search-btn-box">
        <el-button type="primary" size="small" round @click="handleReset">{{
          $t('reset')
        }}</el-button>
        <el-button type="primary" size="small" round @click="handleRefresh">{{
          $t('refresh')
        }}</el-button>
      </div>
      <div class="search-left-wrapper"></div>
    </div>
    <!-- redis日志表格 -->
    <div class="table-wrapper row-table-wrapper">
      <div class="tree-content">
        <el-tree
          v-loading="treeLoading"
          :data="treeData"
          :props="defaultProps"
          :load="loadNode"
          lazy
          @node-click="handleNodeClick"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <i
              v-if="!data.isLeaf"
              class="el-icon-folder"
              style="color: #429efd"
            ></i>
            <span>{{ node.label }}</span>
          </span>
        </el-tree>
      </div>
      <div class="table-content">
        <!-- 展示常规表格 -->
        <template v-if="!hasSecondTable">
          <tvt-table
            ref="myTable"
            v-myLoading="loading"
            :data="redisList"
            :columns="columns.slice(1)"
            :border="true"
            :border-bottom="true"
            :pagination="false"
            @onFetchData="() => {}"
          >
            <template #bodyCell="{ row, column }">
              <template v-if="column.prop === 'content'">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="row[column.prop]"
                  placement="left-start"
                >
                  <span class="tooltip-ellipsis-box">{{
                    row[column.prop]
                  }}</span>
                </el-tooltip>
              </template>
            </template>
          </tvt-table>
        </template>
        <!-- 展示二级表格 -->
        <template v-else>
          <!-- 新增一个搜索框 -->
          <div class="search-wrapper sub-search-wrapper">
            <search-input
              v-model="filterCond.fieldSearchKey"
              :placeholder="$t('kafkaSearchTab.fieldSearchKeyInput')"
              clearable
              size="small"
              maxlength="32"
              style="width: 250px; margin-right: 20px"
            />
            <div class="search-btn-box">
              <el-button
                type="primary"
                size="small"
                round
                @click="handleSearch"
                >{{ $t('search') }}</el-button
              >
            </div>
          </div>
          <tvt-table-pro
            v-loading="loading"
            :data="redisList"
            stripe
            row-key="id"
            :expand-row-keys="expandRowKeys"
            :border="true"
            :border-bottom="true"
            :primary="{
              pagination: false,
              columns,
              customClass: 'redis-search',
            }"
            :second="{
              pagination: false,
              columns: subColumns,
              'max-height': 500,
            }"
            @onFetchData="() => {}"
          >
            <template #bodyCell="{ row, column }">
              <template v-if="column.prop === 'content'">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="row[column.prop]"
                  placement="left-start"
                >
                  <span class="tooltip-ellipsis-box">{{
                    row[column.prop]
                  }}</span>
                </el-tooltip>
              </template>
            </template>
          </tvt-table-pro>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getRedisInstanceList,
  getRedisKeyList,
  getRedisValueList,
} from '@/api/basicTool'
export default {
  name: 'RedisSearch',
  components: {},
  data() {
    return {
      total: 0,
      filterCond: {
        redisInstance: '',
        redisKey: '',
        fieldSearchKey: '',
      },
      loading: false,
      treeLoading: false,
      redisInstanceOptions: [], // 桶下拉选项
      expandRowKeys: [], // 展开的行
      redisList: [],
      columns: [
        { type: 'expand', slotName: 'expand', align: 'left' },
        { label: this.$t('index'), type: 'index', width: 100 },
        {
          label: 'key',
          prop: 'key',
          minWidth: 150,
        },
        {
          label: 'value',
          prop: 'content',
          slotName: 'bodyCell',
          minWidth: 150,
        },
        {
          label: 'type',
          prop: 'type',
          width: 150,
        },
        {
          label: 'ttl',
          prop: 'ttl',
          width: 150,
        },
      ],
      subColumns: [],
      hashColumns: [
        { label: this.$t('index'), type: 'index', width: 60 },
        {
          label: 'field',
          prop: 'field',
          width: 180,
        },
        {
          label: 'value',
          prop: 'value',
          minWidth: 200,
        },
      ],
      listColumns: [
        { label: this.$t('index'), type: 'index', width: 60 },
        {
          label: 'value',
          prop: 'value',
          minWidth: 200,
        },
      ],
      zetColumns: [
        { label: this.$t('index'), type: 'index', width: 60 },
        {
          label: 'score',
          prop: 'score',
          width: 160,
        },
        {
          label: 'value',
          prop: 'value',
          minWidth: 200,
        },
      ],
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: 'isLeaf',
      },
      treeData: [],
      hasSecondTable: false,
      controller: null,
    }
  },
  async mounted() {
    await this.getInstanceList()
    this.getRedisKeyList()
  },
  methods: {
    // 获取redis实例列表
    async getInstanceList() {
      try {
        const { data = [] } = await getRedisInstanceList({})
        this.redisInstanceOptions = data.map((item) => {
          return {
            label: item,
            value: item,
          }
        })
        // 桶列表默认选中第一个
        this.filterCond.redisInstance = data[0]
      } catch (error) {
        console.error(error)
      }
    },
    // 获取redis树形结构
    async getRedisKeyList(keyword = '', callback = null) {
      if (!keyword) {
        // 直接更新整体树
        this.treeLoading = true
        // 更新整棵树则需要取消
        if (this.controller) {
          // 说明上次请求还没返回，则直接取消
          this.controller.abort()
        }
        this.controller = new AbortController() // 重新赋值
      }
      try {
        const { redisInstance, fieldSearchKey } = this.filterCond
        const params = {}
        if (redisInstance) {
          params.redisInstance = redisInstance
        }
        // 判断是用输入条件还是用点击节点传入的
        // 有输入条件则带上输入条件
        // if (!keyword) {
        //   params.redisKey = redisKey
        // } else {
        //   params.redisKey = `${redisKey}:${keyword}`
        // }
        if (keyword) {
          params.redisKey = keyword
        }
        if (fieldSearchKey) {
          params.fieldSearchKey = fieldSearchKey
        }
        const { data = [] } = await getRedisKeyList(
          params,
          this.controller
            ? {
                signal: this.controller.signal,
              }
            : null
        )
        // console.log('data', data)
        // 创建树形结构
        const resData = data.slice().map((item) => {
          const { curLevelKey, count } = item
          const node = {
            curLevelKey,
            label: curLevelKey,
            count,
            isLeaf: count <= 1,
            path: keyword ? `${keyword}:${curLevelKey}` : curLevelKey,
          }
          return node
        })
        if (keyword) {
          // 说明是点击节点进入的，直接插入对应节点下
          callback && callback(resData)
        } else {
          // 否则直接更新整体树
          this.treeData = resData
          this.treeLoading = false
          this.controller = null // 重置
        }
      } catch (error) {
        console.error(error)
        if (!keyword) {
          // 直接更新整体树
          this.treeLoading = false
          this.controller = null // 重置
        }
      }
    },
    // 获取redis列表
    async getRedisValueList() {
      this.loading = true
      try {
        const { redisInstance, redisKey, fieldSearchKey } = this.filterCond
        const params = {}
        if (redisInstance) {
          params.redisInstance = redisInstance
        }
        if (redisKey) {
          params.redisKey = redisKey
        }
        if (fieldSearchKey) {
          params.fieldSearchKey = fieldSearchKey
        }
        const { data = {} } = await getRedisValueList(params)
        const { type, key, value, ttl } = data
        const tableData = {
          type,
          key,
          value,
          ttl,
          id: key,
          content: value ? JSON.stringify(value) : '',
        }
        this.expandRowKeys = [key]
        let subList = []
        switch (type) {
          case 'list':
          case 'set':
            this.subColumns = this.listColumns.slice()
            tableData.subList = value.map((value2) => ({
              value: value2,
            }))
            this.hasSecondTable = true
            break
          case 'hash':
            this.subColumns = this.hashColumns.slice()
            tableData.subList = value.map((item) => {
              const key = Object.keys(item)[0]
              return {
                field: key,
                value: item[key],
              }
            })
            this.hasSecondTable = true
            break
          case 'zset':
            this.subColumns = this.zetColumns.slice()
            // zset结果，业务redis和natRedis返回不一样，统一成一致
            tableData.subList = value.slice().map((item) => ({
              ...item,
              value: item.value || item.element,
            }))
            this.hasSecondTable = true
            break
          case 'stream':
            this.subColumns = this.hashColumns.slice()
            value.forEach((item) => {
              Object.entries(item.value).forEach(([key2, value2]) => {
                subList.push({
                  field: key2,
                  value: value2,
                })
              })
            })
            tableData.subList = subList
            this.hasSecondTable = true
            break
          default:
            this.subColumns = []
            this.hasSecondTable = false
            break
        }
        this.redisList = [tableData]
        this.loading = false
      } catch (error) {
        console.error(error)
        this.loading = false
      }
    },
    // 请求节点
    loadNode(node, resolve) {
      // console.log('node', node)
      const { isLeaf, path } = node.data
      const { redisInstance } = this.filterCond
      if (!isLeaf && redisInstance) {
        // console.log('开始请求', Date.now())
        this.getRedisKeyList(path, resolve)
      }
    },
    handleRedisInstance() {
      // 清空树和表格
      this.getRedisKeyList()
      this.redisList = []
    },
    // 重置
    async handleReset() {
      this.filterCond = {
        redisInstance: this.redisInstanceOptions[0].value,
        redisKey: '',
        fieldSearchKey: '',
      }
      this.getRedisKeyList()
      this.redisList = []
    },
    // 刷新
    handleRefresh() {
      this.getRedisKeyList()
      this.filterCond = {
        ...this.filterCond,
        redisKey: '',
        fieldSearchKey: '',
      }
      this.hasSecondTable = false
      this.redisList = []
    },
    // 搜索
    handleSearch() {
      this.getRedisValueList()
    },
    // 点击节点重新请求表格
    handleNodeClick(data) {
      if (data.isLeaf) {
        // 是叶子节点才请求表格 -- 重置redisKey，并请求fieldSearchKey搜索
        this.filterCond = {
          ...this.filterCond,
          redisKey: data.path,
          fieldSearchKey: '',
        }
        this.getRedisValueList()
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.row-table-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

.tree-content {
  width: 400px;
  height: calc(100vh - 155px);
  overflow: auto;
  background: white;
  margin-right: 20px;
}

.table-content {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

.sub-search-wrapper {
  padding: 0px;
}

::v-deep .tooltip-ellipsis-box {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
