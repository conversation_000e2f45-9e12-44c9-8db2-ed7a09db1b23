<template>
  <!-- 资源层监控 -->
  <div class="resource-wrapper">
    <div class="resource-search-wrapper">
      <tvt-select
        v-if="areaOptions.length > 1"
        v-model="filterCond.dcId"
        :options="getfilterAreaOptions"
        style="width: 250px"
        @focus="handleFocus"
      />
      <tvt-select-tree
        v-model="filterCond.searchType"
        :options="getfilterTreeOptions"
        :default-expanded-keys="defaultExpandedKeys"
        style="width: 250px"
        @focus="handleFocus"
      />
      <tu-search-time
        v-if="showSearchTime"
        ref="tuSearchTimeRef"
        v-model="filterCond.timeRange"
        class="search-time"
        :default="defaultTimeRange"
      />
      <el-button type="primary" size="small" round @click="handleReset">
        {{ $t('reset') }}
      </el-button>
      <el-button type="primary" size="small" round @click="handleRefresh">{{
        $t('refresh')
      }}</el-button>
    </div>
    <div class="resource-left-wrapper">
      <template v-if="showChart">
        <div class="top-echart-wrapper">
          <div class="echart-wrapper">
            <monitor-chart
              ref="monitorChartRef"
              :search-cond="{ ...filterCond, areas: getAreas }"
              @focus="handleFocus"
            />
          </div>
        </div>
        <div
          class="table-wrapper"
          :style="{
            // 'min-height': 'calc(100% - 380px)'
            'min-height': 'calc(100% - 470px)',
          }"
        >
          <monitor-table ref="monitorTableRef" :search-cond="filterCond" />
        </div>
      </template>
      <template v-else>
        <div
          class="table-wrapper"
          :style="{ 'min-height': 'calc(100% - 45px)' }"
        >
          <monitor-table ref="monitorTableRef" :search-cond="filterCond" />
        </div>
      </template>
    </div>
  </div>
</template>
<script>
import TuSearchTime from '@/components/common/TuSearchTime.vue'
// import TvtSelectTree from '@/components/common/TvtSelectTree.vue'
import MonitorTable from './components/MonitorTable.vue'
import { formOptionsObj, metricsOptionsObj, pageMonitorObj } from './config.js'
import { getDcInfoListApi } from '@/api/home.js'
import { getLocale } from '@/lang'
import { mapState } from 'vuex'
import MonitorChart from './components/MonitorChart.vue'
import { debounce, btnBlur } from '@/utils/common'
import selfLocaLStorage from '@/utils/selfLocalStorage'

export default {
  name: 'CommonMonitor',
  components: {
    MonitorTable,
    TuSearchTime,
    MonitorChart,
  },
  props: {},
  data() {
    return {
      formOptionsObj,
      metricsOptionsObj,
      treeOptions: [], // 指标树形选择
      areaOptions: [], // DC树形选择
      defaultExpandedKeys: null, // 指标默认展开
      formOptions: [],
      echartSelectList: [],
      defaultTimeRange: [],
      formData: {},
      xAxis: [],
      seriesData: null,
      seriesFilterData: null, // 对最终生成的结果进行过滤，针对微服务这种返回多实例的，需要默认勾选TOP5展示
      checkedIntance: [], // 默认勾选展示的实例,在图上展示 默认为TOP5
      topData: [], // 排序的实例
      chartTitle: '',
      ctSubType: null,
      loading: false,
      monitorColumns: [],
      monitorTableDatas: [],
      total: 0, // 表格数据总量
      pagination: {
        pageNum: 1,
        pageSize: 20,
      },
      // 实时查询条件
      filterCond: {
        searchType: null,
        dcId: null,
        timeRange: [],
      },
      // 上次查询的条件
      preFilterCond: {
        searchType: null,
        dcId: null,
        timeRange: [],
      },
      searchCond: {
        // 图表查询条件
        monitorType: 'monitor.nat',
        ctSubtype: 'NatServer',
        metrics: ['nat_dev_online'],
        timeRange: [],
      },
      showChart: true, // 是否展示图  进程的指标不展示图，只展示表格,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: this.$t('today'),
            onClick(picker) {
              picker.$emit('pick', new Date())
            },
          },
          {
            text: this.$t('yesterday'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24)
              picker.$emit('pick', date)
            },
          },
          {
            text: this.$t('weekAgo'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', date)
            },
          },
        ],
      },
      dcResCodeList: [], // 包含当前DC的resCode列表
    }
  },
  computed: {
    ...mapState('params', ['envDcInfo']),
    ...mapState('monitor', ['dcInfoList']),
    getAreas() {
      // P2P、主机、微服务才展示不同集群，其余指标就展示一个图
      const nodeSearchType = [
        'monitor.host-CPU/内存/主机流量',
        'monitor.host.disk-磁盘',
        'monitor.host.process-进程',
        'monitor.nat',
        'monitor.udt', // RedirectServer 只展示一张图
        'monitor.relay',
        'monitor.microservice',
      ] // 主机、P2P、微服务的searchType
      const { searchType } = this.filterCond
      // console.log('getAreas中 searchType', searchType)
      if (searchType && nodeSearchType.includes(searchType)) {
        const areaItem = this.areaOptions.find(
          (item) => item.value === this.filterCond.dcId
        )
        return areaItem ? areaItem.children : [undefined]
      } else {
        return [undefined] // 默认放一个
      }
    },
    // 过滤指标树选项
    getfilterTreeOptions() {
      return (this.treeOptions || []).filter((item) => item.show !== false)
    },
    // 过滤DC选项
    getfilterAreaOptions() {
      return (this.areaOptions || []).filter((item) => item.show !== false)
    },
    // 是否展示时间搜索
    showSearchTime() {
      // 磁盘、Https证书、域名均不展示时间
      return ![
        // 'monitor.host.disk-磁盘',
        // 'monitor.domain',
        // 'monitor.cert',
      ].includes(this.filterCond.searchType)
    },
  },
  watch: {
    'filterCond.searchType': {
      handler(val) {
        const monitorArr = [
          // 'monitor.host.process-进程',
          'monitor.devopswebsocket',
          'monitor.dpikey',
          'monitor.microservice.web',
        ]
        if (val && monitorArr.includes(val)) {
          // 进程、websokcet、dpikey、web微服务不请求数据，也不展示echart图
          this.showChart = false
        } else {
          this.showChart = true
        }
      },
      deep: true,
      immediate: true,
    },
    'filterCond.dcId': {
      // 如果是DDC，则不显示域名和证书，如果之前选中了域名和证书，则重新选项--只不显示证书，域名放开
      handler(val, oldVal) {
        if (val && val !== oldVal) {
          // 根据dcId更新下指标选项
          this.updateAuthTreeOptions(val)
        }
        // 存储当前选中的dcId
        if (val) {
          sessionStorage.setItem('dcId', val)
        }
      },
      deep: true,
      immediate: true,
    },
    // 监听filterCond整体的变化，手动触发图和表格数据的更新--基于需要图、表请求分步的需求调整
    filterCond: {
      handler(val) {
        // console.log('filterCond变化', val)
        if (val && val.dcId) {
          this.refreshData()
        }
      },
      deep: true,
      immediate: true,
    },
  },
  async created() {
    // 获取对应页面的dcCodeList
    this.getDcResCodeList()
    // 获取DC下拉
    await this.getAreaOptions()
    // 过滤出来指标的下拉选项
    const treeOptions = this.formOptionsObj[this.$route.name]
    this.treeOptions = treeOptions
    if (this.filterCond.dcId) {
      // 根据dcId更新下指标选项
      this.updateAuthTreeOptions(this.filterCond.dcId)
    }
    const { searchType } = this.filterCond
    if (['monitor.devopswebsocket', 'monitor.dpikey'].includes(searchType)) {
      // devopswebsocket、dpikey不请求数据，也不展示echart图
      this.showChart = false
    } else {
      this.showChart = true
    }
    if (this.$route.name === 'resLayerMonitor') {
      // 资源层默认选中 CPU/内存/主机流量，默认展开主机监控
      this.defaultExpandedKeys = ['monitor.host']
    } else {
      this.defaultExpandedKeys = null
    }
  },
  mounted() {},
  beforeDestroy() {
    this.$EventBus.$off('chartTableData')
  },
  methods: {
    // 请求图、表数据
    refreshData: debounce(function (refreshTime = null) {
      // 确保filterCond等条件已经传递给子组件
      this.$nextTick(async () => {
        if (this.showChart) {
          if (this.$refs.monitorChartRef) {
            new Promise((resolve) => {
              // 先请求图
              this.$refs.monitorChartRef.refreshRequest(resolve, refreshTime)
              // 图的请求最多等待5s
              setTimeout(() => {
                resolve()
              }, 5000)
            })
              .then(() => {
                // 再请求表
                this.$refs.monitorTableRef &&
                  this.$refs.monitorTableRef.refreshRequest()
              })
              .catch(() => {
                this.$refs.monitorTableRef &&
                  this.$refs.monitorTableRef.refreshRequest()
              })
          }
        } else {
          if (this.$refs.monitorTableRef) {
            // 请求表
            this.$refs.monitorTableRef.refreshRequest()
          }
        }
      })
    }, 500),
    // 获取DC下拉选项
    async getAreaOptions() {
      let data = []
      if (this.dcInfoList) {
        // 有dcInfo信息，则不用请求
        data = this.dcInfoList.slice()
      } else {
        // 否则走请求逻辑
        const res = await getDcInfoListApi(getLocale())
        data = res.data.slice()
        // 更新Vuex中存储的dcInfo
        this.$store.commit('monitor/DC_INFO_LIST', data)
      }
      const areaOptions = []
      if (data.length > 0) {
        let dcList = data
        // if (this.$route.name === 'p2p') {
        //   // p2p的页面不展示rdc选项
        //   dcList = data.filter((item) => item.dcId !== '1')
        // }
        // p2p的页面不展示rdc选项 -- 根据dcResCodeList判断
        dcList = data.filter((item) => this.dcResCodeList.includes(item.dcId))
        dcList.forEach((item) => {
          const _item = {}
          _item.label = item.dcName
          _item.value = item.dcId
          if (item.areas) {
            const areasTmp = item.areas.map((areasItem) => {
              return {
                value: areasItem.code,
                label: areasItem.name,
                type: areasItem.type,
              }
            })
            if (this.$route.name === 'p2p') {
              // RDC时展示全部，DDC时展示type为2的
              if (item.dcId === '1') {
                // RDC
                _item.children = areasTmp
              } else {
                // P2P的集群只展示type为2的   java集群：1 ，p2p集群：2
                const areasTmp2 = areasTmp.filter(
                  (areaItem) => areaItem.type === '2'
                )
                _item.children = areasTmp2
              }
            } else {
              _item.children = areasTmp
            }
            _item.children = areasTmp
          }
          if (_item.children && _item.children.length) {
            areaOptions.push(_item)
          }
        })
        this.areaOptions = areaOptions
        // DC的默认勾选
        // const { dcId } = this.envDcInfo || {}
        // 如果暂存过dcId，则默认选dcId，否则用当前环境DC
        const sessionDcId = sessionStorage.getItem('dcId')
        const dcId =
          sessionDcId || (this.envDcInfo ? this.envDcInfo.dcId : null)
        // if (this.$route.name === 'p2p' && dcId === '1') {
        // P2P的页面不展示rdc选项 -- 根据dcResCodeList判断
        if (!this.dcResCodeList.includes(dcId)) {
          // 如果当前是RDC环境，且页面是p2p, 则不能默认勾选当前DC(rdc)，而是选择第一个DC。P2P的指标不展示RDC
          const newDcId = areaOptions[0].value || null
          sessionStorage.setItem('dcId', newDcId)
          this.filterCond = {
            ...this.filterCond,
            dcId: newDcId,
          }
        } else {
          const dcNode = sessionDcId
            ? areaOptions.find((item) => item.value == sessionDcId)
            : null
          if (dcNode) {
            // 如果当前暂存的dcId能在区域选项中找到，则对查询条件中dcId赋值
            this.filterCond = {
              ...this.filterCond,
              dcId: sessionDcId,
            }
          } else {
            // 调整默认值--默认勾选当前DC，如果当前DC有集群，则勾选第一个集群
            const dcNode = dcId
              ? areaOptions.find((item) => item.value == dcId)
              : null
            let newDcId = areaOptions[0].value || null
            if (dcNode) {
              newDcId = dcNode.value
            }
            this.filterCond = {
              ...this.filterCond,
              dcId: newDcId,
            }
            console.log('更新dcId', newDcId)
          }
        }
      }
    },
    // 获取对应页面的dcCodeList
    getDcResCodeList() {
      const monitor = pageMonitorObj[this.$route.name]
      const dcResCodeObj = selfLocaLStorage.getItem('dcResCodeObj') || {}
      const dcMonitorObj = dcResCodeObj[monitor] || {}
      const dcResCodeList = Object.keys(dcMonitorObj)
      this.dcResCodeList = dcResCodeList
    },
    // 根据dcID的权限更新指标选项
    updateAuthTreeOptions(val) {
      const monitor = pageMonitorObj[this.$route.name]
      const dcResCodeObj = selfLocaLStorage.getItem('dcResCodeObj') || {}
      const dcResCode = dcResCodeObj[monitor][val] || []
      // 根据dcResCode更新treeOptions
      const treeOptions = this.formOptionsObj[this.$route.name].slice()
      treeOptions.forEach((item) => {
        if (item.children && item.children.length) {
          // 有子节点的情况
          const hasValidChild = item.children.some(
            (child) =>
              child.quotaAuthList &&
              child.quotaAuthList.some((auth) => dcResCode.includes(auth))
          )
          item.show = hasValidChild
          // 更新子节点的显示状态
          item.children.forEach((child) => {
            child.show =
              child.quotaAuthList &&
              child.quotaAuthList.some((auth) => dcResCode.includes(auth))
          })
        } else {
          // 无子节点的情况
          item.show =
            item.quotaAuthList &&
            item.quotaAuthList.some((auth) => dcResCode.includes(auth))
        }
      })
      this.treeOptions = treeOptions
      // 检查当前searchType是否有效
      const { searchType } = this.filterCond
      const isValidSearchType = treeOptions.some((item) => {
        if (item.children && item.children.length) {
          return item.children.some(
            (child) => child.value === searchType && child.show !== false
          )
        }
        return item.value === searchType && item.show !== false
      })
      // 如果当前searchType无效，则重置为第一个可用选项
      if (!isValidSearchType) {
        const firstValidOption = treeOptions.find((item) => item.show !== false)
        if (firstValidOption) {
          const newSearchType =
            firstValidOption.children && firstValidOption.children.length
              ? firstValidOption.children.find((child) => child.show !== false)
                  ?.value
              : firstValidOption.value

          if (newSearchType) {
            this.filterCond = {
              ...this.filterCond,
              searchType: newSearchType,
            }
          }
        }
      }
    },
    // 处理刷新
    handleRefresh: debounce(function (e) {
      this.preFilterCond = { ...this.filterCond }
      const { searchType } = this.filterCond
      if (['monitor.devopswebsocket', 'monitor.dpikey'].includes(searchType)) {
        // devopswebsocket、dpikey不请求数据，也不展示echart图
        this.showChart = false
      } else {
        this.showChart = true
      }
      // 强制刷新
      this.refreshData(Date.now())
      // 按钮失焦
      btnBlur(e)
    }, 500),
    handleReset: debounce(function (e) {
      // 调整默认值
      const searchType = this.treeOptions[0].children
        ? this.treeOptions[0].children[0].value
        : this.treeOptions[0].value
      const dcId = this.areaOptions[0].value || null
      this.filterCond = {
        searchType,
        dcId,
        timeRange: [],
      }
      this.preFilterCond = {
        searchType,
        dcId,
        timeRange: [],
      }
      if (searchType === 'monitor.dpikey') {
        // dpikey不请求数据，也不展示echart图
        this.showChart = false
      } else {
        this.showChart = true
      }
      // 按钮失焦
      btnBlur(e)
    }, 500),
    handleCheck(val) {
      this.getSeriesFilterData(val, this.seriesData)
    },
    // 根据勾选情况过滤seriesData
    getSeriesFilterData(val, seriesData = null) {
      const data = seriesData || this.seriesData
      // 根据勾选的实例过滤图数据
      const instanceSet = new Set(val)
      this.seriesFilterData = data.filter((item) => instanceSet.has(item.name))
    },
    // 选择框获取焦点时
    handleFocus() {
      // 手动关闭时间弹窗
      if (this.$refs.tuSearchTimeRef) {
        this.$refs.tuSearchTimeRef.$refs.dateTimePicker.handleClose()
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.resource-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}
.resource-search-wrapper {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
  & > div {
    margin-right: 20px;
  }
}
.resource-left-wrapper {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .top-echart-wrapper {
    position: relative;
    background: #fff;
    width: 100%;
    min-height: 320px;
    box-sizing: border-box;
    .echart-wrapper {
      width: 100%;
      height: 100%;
    }
  }
  .table-wrapper {
    width: 100%;
    margin-top: 4px;
    min-height: calc(100% - 470px);
    margin-bottom: 40px;
  }
}
</style>
<style lang="scss">
.resource-search-wrapper {
  .el-input__inner {
    border: 1px solid #dcdfe6;
    border-radius: 16px;
    height: 32px;
    line-height: 32px;
  }
  .el-input__icon {
    line-height: 34px;
  }

  .tvt-select .tvt-field-set {
    border: none;
  }

  .btn-search {
    border-radius: 16px;
  }

  .el-form--inline .el-form-item {
    margin-right: 20px;
  }

  .el-form--inline .el-form-item .el-radio:not(:last-child) {
    margin-right: 20px;
  }

  .search-form-box .btn-box button {
    height: 32px !important;
  }
}
.top-echart-wrapper {
  .el-input__inner {
    height: 32px;
    line-height: 32px;
    border: 1px solid #dcdfe6;
    border-radius: 16px;
  }
  .el-input__icon {
    line-height: 34px;
  }
}
</style>
