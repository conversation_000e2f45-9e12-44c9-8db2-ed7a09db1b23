<template>
  <!-- 特性访问量 -->
  <div class="page-wrapper">
    <div class="search-wrapper">
      <el-cascader
        v-model="filterCond.resource"
        class="customer-cascader"
        :options="resourceCascadeOptions"
        :placeholder="$t('devopsStatisticsTab.functionSelect')"
        clearable
        style="width: 250px; margin-right: 20px"
        @change="getApiLogList"
      />
      <tvt-date-picker
        v-model="filterCond.dayTime"
        align="right"
        type="daterange"
        :placeholder="$t('microLogTab.dayTimeSelect')"
        :clearable="false"
        style="width: 250px; margin-right: 20px"
        :picker-options="pickerOptions"
        @change="handleDate"
      ></tvt-date-picker>
      <div class="search-btn-box">
        <el-button type="primary" size="small" round @click="handleReset">{{
          $t('reset')
        }}</el-button>
        <el-button type="primary" size="small" round @click="handleRefresh">{{
          $t('refresh')
        }}</el-button>
      </div>
      <div class="search-left-wrapper"></div>
    </div>
    <!-- 特性访问量表格 -->
    <div class="table-wrapper">
      <el-table
        row-key="id"
        :data="microLogList"
        :border="true"
        :border-bottom="true"
        :pagination="false"
        :max-height="850"
        lazy
        :load="load"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column
          prop="name"
          :label="$t('devopsStatisticsTab.product')"
          min-width="150"
        >
        </el-table-column>
        <el-table-column
          prop="requestCount"
          :label="$t('devopsStatisticsTab.requestCount')"
          width="150"
        >
        </el-table-column>
        <el-table-column
          prop="successCount"
          :label="$t('devopsStatisticsTab.successCount')"
          width="150"
        >
        </el-table-column>
        <el-table-column
          prop="failedCount"
          :label="$t('devopsStatisticsTab.failedCount')"
          width="150"
        >
        </el-table-column>
        <el-table-column
          prop="maxCost"
          :label="$t('devopsStatisticsTab.maxCost')"
          width="150"
        ></el-table-column>
        <el-table-column
          prop="avgCost"
          :label="$t('devopsStatisticsTab.avgCost')"
          width="150"
        ></el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { debounce, stampToStrLong, commonShortcuts } from '@/utils/common'
import {
  getResourceCascade,
  getApiLogStatisticsList,
} from '@/api/devopsStatistics.js'
export default {
  name: 'LowFrequencyAccess',
  components: {},
  data() {
    const pageVM = this
    return {
      defaultTimeRange: [],
      filterCond: {
        dayTime: this.getInitDate(),
        resource: '',
      },
      startDate: null,
      pickerOptions: {
        // 点击时，选择的是开始时间，也就是minDate
        onPick: (time) => {
          // 记录首次选择的日期（自然月计算基准）
          if (time.minDate && !time.maxDate) {
            pageVM.startDate = time.minDate
          }
          if (time.maxDate) {
            pageVM.startDate = null
          }
        },
        disabledDate(time) {
          if (pageVM.startDate) {
            const start = moment(pageVM.startDate)
            const end = moment(time)
            // 限制结束日期在[start, start+1月]范围内,且不能超过当前日期
            return (
              time.getTime() > Date.now() ||
              end < start.clone().add(-29, 'd') ||
              end > start.clone().add(29, 'd')
            )
          } else {
            return time.getTime() > Date.now()
          }
        },
        shortcuts: commonShortcuts,
      },
      loading: false,
      microTypeOptions: [],
      resourceCascadeOptions: [],
      microLogList: [],
      columns: [
        { type: 'expand', slotName: 'expand', align: 'left' },
        { label: this.$t('index'), type: 'index', width: 120 },
        {
          label: this.$t('devopsStatisticsTab.product'),
          prop: 'product',
          minWidth: 150,
          slotName: 'bodyCell',
        },
        {
          label: this.$t('devopsStatisticsTab.module'),
          prop: 'module',
          minWidth: 150,
          slotName: 'bodyCell',
        },
        {
          label: this.$t('devopsStatisticsTab.function'),
          prop: 'function',
          minWidth: 150,
          slotName: 'bodyCell',
        },
        {
          label: this.$t('devopsStatisticsTab.requestCount'),
          prop: 'requestCount',
          minWidth: 120,
        },
        {
          label: this.$t('devopsStatisticsTab.successCount'),
          prop: 'successCount',
          minWidth: 120,
        },
        {
          label: this.$t('devopsStatisticsTab.failedCount'),
          prop: 'failedCount',
          minWidth: 120,
        },
      ],
      subColumns: [
        { label: this.$t('index'), type: 'index', width: 60 },
        {
          label: this.$t('devopsStatisticsTab.requestPath'),
          prop: 'requestPath',
          minWidth: 150,
        },
        {
          label: this.$t('devopsStatisticsTab.clientType'),
          prop: 'clientType',
          width: 130,
        },
        {
          label: this.$t('devopsStatisticsTab.accountCount'),
          prop: 'accountCount',
          width: 130,
        },
        {
          label: this.$t('devopsStatisticsTab.requestCount'),
          prop: 'requestCount',
          width: 130,
        },
        {
          label: this.$t('devopsStatisticsTab.successCount'),
          prop: 'successCount',
          width: 130,
        },
        {
          label: this.$t('devopsStatisticsTab.failedCount'),
          prop: 'failedCount',
          width: 130,
        },
        {
          label: this.$t('devopsStatisticsTab.maxCost'),
          prop: 'maxCost',
          width: 130,
        },
        {
          label: this.$t('devopsStatisticsTab.avgCost'),
          prop: 'avgCost',
          width: 130,
        },
      ],
      microColumns: [
        {
          label: this.$t('devopsStatisticsTab.product'),
          prop: 'name',
          minWidth: 150,
          slotName: 'bodyCell',
        },
        {
          label: this.$t('devopsStatisticsTab.requestCount'),
          prop: 'requestCount',
          width: 150,
        },
        {
          label: this.$t('devopsStatisticsTab.successCount'),
          prop: 'successCount',
          width: 150,
        },
        {
          label: this.$t('devopsStatisticsTab.failedCount'),
          prop: 'failedCount',
          width: 150,
        },
        {
          label: this.$t('devopsStatisticsTab.maxCost'),
          prop: 'maxCost',
          width: 150,
        },
        {
          label: this.$t('devopsStatisticsTab.avgCost'),
          prop: 'avgCost',
          width: 150,
        },
      ],
      treeLoading: false,
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: 'isLeaf',
      },
    }
  },
  async mounted() {
    await this.getResourceCascade()
    this.getApiLogList()
  },
  methods: {
    getInitDate() {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24)
      return [date, date]
    },
    stampToStrLongMethod(time) {
      let intTime = parseInt(time) //接口给的字符串时间
      return stampToStrLong(intTime)
    },
    // 获取资源级联下拉选项
    async getResourceCascade() {
      try {
        const { data = [] } = await getResourceCascade({})
        this.resourceCascadeOptions = this.createCascade(data)
      } catch (error) {
        console.error(error)
        this.resourceCascadeOptions = []
      }
    },
    // 生成级联数
    createCascade(data) {
      if (data.length === 0) {
        return []
      }
      const firstLevel = data.filter((item) => item.resLevel === 0)
      const secondLevel = data.filter((item) => item.resLevel === 1)
      const thirdLevel = data.filter((item) => item.resLevel === 2)
      const treeData = []
      firstLevel.forEach((item) => {
        const children = []
        secondLevel.forEach((secondItem) => {
          if (secondItem.parentId === item.id) {
            const thirdChildren = []
            thirdLevel.forEach((thirdItem) => {
              if (thirdItem.parentId === secondItem.id) {
                thirdChildren.push({
                  label: this.$t(`authConfig.${thirdItem.resCode}`),
                  value: thirdItem.resCode,
                  id: thirdItem.id,
                })
              }
            })
            children.push({
              label: this.$t(`authConfig.${secondItem.resCode}`),
              value: secondItem.resCode,
              id: secondItem.id,
              children: thirdChildren,
            })
          }
        })
        treeData.push({
          label: this.$t(`authConfig.${item.resCode}`),
          value: item.resCode,
          id: item.id,
          children,
        })
      })
      return treeData
    },
    getName(data) {
      if (data.requestPath) {
        return data.requestPath
      }
      if (data['function']) {
        return this.$t(`authConfig.${data['function']}`)
      }
      if (data.module) {
        return this.$t(`authConfig.${data['module']}`)
      }
      if (data.product) {
        return this.$t(`authConfig.${data['product']}`)
      }
    },
    getApiLogList: debounce(async function () {
      this.loading = true
      this.treeLoading = true
      try {
        const { dayTime, resource } = this.filterCond
        const params = {
          type: 'USAGE_LOW_FREQUENCY', // 功能使用频次：USAGE_FREQUENCY  功能低使用频次：USAGE_LOW_FREQUENCY
        }
        if (resource && resource.length > 0) {
          params.product = resource[0]
          params.module = resource[1]
          params['function'] = resource[2]
        }
        if (dayTime && dayTime.length > 0) {
          params.dayTimeStart = window.moment(dayTime[0]).format('yyyy-MM-DD')
          params.dayTimeEnd = window.moment(dayTime[1]).format('yyyy-MM-DD')
        }
        const { data = [] } = await getApiLogStatisticsList(params)
        const microLogList = data.map((item) => {
          const isLeaf = !!item.requestPath
          return {
            ...item,
            name: this.getName(item),
            id: `${item.product}_${item.module}_${item.function}_${
              item.requestPath
            }_${new Date().getTime()}`,
            subList: item.details || [],
            isLeaf: isLeaf,
            hasChildren: !isLeaf,
          }
        })
        this.microLogList = microLogList
        this.loading = false
        this.treeLoading = false
      } catch (error) {
        console.error(error)
        this.loading = false
        this.treeLoading = false
      }
    }, 500),
    handleReset() {
      // 条件重置
      this.filterCond = {
        ...this.filterCond,
        dayTime: this.getInitDate(),
        resource: '',
      }
      this.$nextTick(() => {
        this.getApiLogList()
      })
    },
    handleRefresh() {
      this.getApiLogList()
    },
    handleDate(val) {
      this.filterCond.dayTime = val
      this.getApiLogList()
    },
    // 获取树形节点数据
    getApiTreeData: debounce(async function (node, callback = null) {
      this.loading = true
      try {
        const { dayTime, clientType } = this.filterCond
        const params = {
          type: 'USAGE_LOW_FREQUENCY', // 功能使用频次：USAGE_FREQUENCY  功能低使用频次：USAGE_LOW_FREQUENCY
        }
        if (dayTime && dayTime.length > 0) {
          params.dayTimeStart = window.moment(dayTime[0]).format('yyyy-MM-DD')
          params.dayTimeEnd = window.moment(dayTime[1]).format('yyyy-MM-DD')
        }
        if (clientType) {
          params.clientType = clientType
        }
        if (node) {
          console.log('node', node)
          params.product = node.product
          params.module = node.module
          params['function'] = node['function']
        }
        const { data = [] } = await getApiLogStatisticsList(params)
        // 创建树形结构
        const resData = data.map((item) => {
          const isLeaf = !!item.requestPath
          return {
            ...item,
            name: this.getName(item),
            id: `${item.product}_${item.module}_${item.function}_${
              item.requestPath
            }_${new Date().getTime()}`,
            subList: item.details || [],
            isLeaf,
            hasChildren: !isLeaf,
          }
        })
        callback && callback(resData)
      } catch (error) {
        console.error(error)
      }
    }, 500),
    load(tree, treeNode, resolve) {
      console.log('tree', tree, 'treeNode', treeNode)
      const { isLeaf } = tree
      if (!isLeaf) {
        // console.log('开始请求', Date.now())
        this.getApiTreeData(tree, resolve)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.btn-text {
  color: #429efd !important;
  cursor: pointer;
  + .btn-text {
    margin-left: 20px;
  }
}
.customer-cascader {
  line-height: 34px;
}
</style>
