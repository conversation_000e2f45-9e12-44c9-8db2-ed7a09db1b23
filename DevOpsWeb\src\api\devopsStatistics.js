import request from '@/api/request'
const baseUrl = '/dev-ops'

// 获取资源级联下拉数据
export const getResourceCascade = (data) =>
  request.post(`${baseUrl}/basic/function-res`, data)

// 获取Api日志统计分析结果列表
export const getApiLogStatisticsList = (data) =>
  request.post(`${baseUrl}/basic/api-log-statistics`, data)

// 获取特性告警统计列表
export const getFeatureAlarmStatisticsList = (data) =>
  request.post(`${baseUrl}/basic/monitor-alert-statistics`, data)

// 查询告警统计信息概要
export const getAlarmStatisticsSummary = (data) =>
  request.post(`${baseUrl}/basic/alert-statistics`, data)

// 查询告警统计信息详情(表格)
export const getAlarmStatisticsDetail = (data) =>
  request.post(`${baseUrl}/basic/alert-statistics-detail`, data)

// 告警统计信息表格导出
export const exportAlarmStatisticsData = (data) =>
  request.post(`${baseUrl}/basic/alert-statistics-export`, data, {
    responseType: 'blob',
    ContentType: 'text/plain',
  })
