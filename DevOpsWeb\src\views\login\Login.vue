<template>
  <div class="all-login">
    <div class="LoginPage" :style="{ height: loginHeight }">
      <lang-select
        ref="langSelect"
        class="lang-select"
        @changeLang="langChange"
      />
      <img v-if="bgImg" :src="bgImg" class="bg-img" />
      <div class="login_container">
        <div class="client_logo">
          <img :src="logoSrc" />
        </div>
        <div class="client_logo_title">Cloud DevOps</div>
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          class="main_container"
        >
          <el-form-item label="" prop="username">
            <el-input
              v-model.trim="ruleForm.devopsUser"
              :placeholder="$t('login.userNamePHD')"
              class="input_style"
              :clearable="true"
              autocomplete="off"
              style="border-radius: 4px"
              maxlength="32"
            >
              <i slot="prefix" class="el-input__icon">
                <svg-icon :viewBox="viewbox" :width="16" :height="16">
                  <icon-user />
                </svg-icon>
              </i>
            </el-input>
          </el-form-item>
          <el-form-item label="" prop="password">
            <el-input
              v-model.trim="ruleForm.devopsPwd"
              :placeholder="$t('login.passwordPHD')"
              class="input_style"
              :type="flag ? 'text' : 'password'"
              :clearable="false"
              style="border-radius: 4px"
              maxlength="16"
            >
              <i slot="prefix" class="el-input__icon">
                <svg-icon :viewBox="viewbox" :width="16" :height="16">
                  <icon-password />
                </svg-icon>
              </i>
              <i
                slot="suffix"
                class="el-input__icon"
                style="margin-right: 8px"
                @click="flag = !flag"
              >
                <svg-icon :viewBox="viewbox" :width="16" :height="16">
                  <icon-eye v-if="flag" />
                  <icon-eye-open v-if="!flag" />
                </svg-icon>
              </i>
            </el-input>
          </el-form-item>
          <el-form-item v-if="idCode" prop="verifyImg">
            <div class="verify-img-wrap">
              <el-input
                v-model.trim="ruleForm.verifyImg"
                type="text"
                maxlength="4"
                :placeholder="$t('enterImgCode')"
                class="input-item"
                :clearable="false"
              />
              <img
                class="verify-img"
                :src="verifyImgSrc"
                @click="getVerifyImg"
              />
            </div>
          </el-form-item>
          <el-button
            v-waves
            class="login_in"
            type="primary"
            @mousedown.native.prevent="handleClickLogin"
          >
            {{ $t('login.login') }}
          </el-button>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import MD5 from 'js-md5'
import {
  devopsLogin,
  devopsUserAuthFunc,
  checkImgCode,
  getVerifyImg,
} from '@/api/user'
import { debounce } from '@/utils/common'
import SvgIcon from '@/components/svg-icon/SvgIcon.vue'
import IconUser from '@/components/svg-icon/icon/IconUser'
import IconPassword from '@/components/svg-icon/icon/IconPassword'
import IconEye from '@/components/svg-icon/icon/IconEye'
import IconEyeOpen from '@/components/svg-icon/icon/IconEyeOpen'
import LangSelect from '@/components/common/LangSelect'
import { getNonce, getBasic } from '@/utils/basic' // 生成basic
import AES from '@/utils/secret/aes.js' // AES加密
import SHA512 from '@/utils/secret/sha512.js' // sha512加密
import { mapMutations } from 'vuex'
import { getLocale } from '@/lang'
import selfLocaLStorage from '@/utils/selfLocalStorage'

export default {
  name: 'LogIn',
  components: {
    SvgIcon,
    IconUser,
    IconPassword,
    IconEye,
    IconEyeOpen,
    LangSelect,
  },
  props: {},
  data() {
    return {
      flag: false,
      logoSrc: require('@img/logo.svg'),
      ruleForm: {
        devopsUser: '',
        devopsPwd: '',
      },
      rules: {
        devopsUser: [{ validator: this.validateEmail, trigger: 'blur' }],
        devopsPwd: [{ validator: this.validatePassword, trigger: 'blur' }],
        verifyImg: [{ validator: this.validateVerifyCode, trigger: 'blur' }],
      },
      modifyPassVisible: false,
      initUsername: '',
      screenHeight: window.innerHeight,
      inviteActiveVisible: false,
      userId: '',
      uuid: '',
      isLogining: false,
      bgImg: require(`@img/login_bg.png`),
      viewbox: '0 0 1024 1024',
      verifyImgSrc: '',
      idCode: '',
    }
  },
  computed: {
    loginHeight() {
      return this.screenHeight
    },
  },
  watch: {
    // 'ruleForm.username':{
    //   handler(newVal,oldVal){
    //     console.log(newVal,oldVal)
    //   },
    //   deep:true,
    //   immediate:true,
    // }
  },
  created() {},
  mounted() {
    let that = this
    window.onresize = () => {
      return (() => {
        that.screenHeight = window.innerHeight + 'px'
      })()
    }
  },
  methods: {
    ...mapMutations(['SAVE_USERINFO']),
    // 邮箱格式校验
    validateEmail(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('login.userNameRequired')))
      } else {
        callback()
      }
    },
    // 密码格式校验
    validatePassword(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('login.passwordRequired')))
      } else {
        callback()
      }
    },
    validateVerifyCode(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('emptyImgCode')))
      } else {
        callback()
      }
    },
    // 模拟clearable
    clearInputUserName() {
      let that = this
      that.ruleForm.devopsUser = ''
      that.$nextTick(() => {
        that.$refs['ruleForm'].validateField('devopsUser', (valid) => {
          console.log(valid)
        })
      })
    },
    handleClickLogin() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.idCode) {
            checkImgCode({
              idCode: this.idCode,
              imgCode: this.ruleForm.verifyImg,
            })
              .then((res) => {
                if (res) {
                  this.loginIn()
                } else {
                  this.$message.error(this.$t('errorImgcode'))
                  this.getVerifyImg()
                }
              })
              .catch((error) => {
                this.$message.error(error.msg)
              })
          } else {
            this.loginIn()
          }
        }
      })
    },
    loginIn: debounce(function () {
      this.$refs['ruleForm'].validate((valid) => {
        if (!valid) {
          return
        }
        const nonce = getNonce()
        const time = new Date().getTime()
        const md5Password = SHA512.encrypt(
          `${nonce}#${time}#${this.ruleForm.devopsUser}#${MD5(
            this.ruleForm.devopsPwd
          )}`
        )
        let paramData = {
          // devopsUser: this.ruleForm.devopsUser,
          // devopsPwd: MD5(this.ruleForm.devopsPwd),
          basic: getBasic({ time, nonce, token: '' }),
          data: {
            userName: this.ruleForm.devopsUser,
            password: md5Password,
            lang: getLocale(),
            type: 1,
            idCode: this.idCode || '',
            imgCode: this.ruleForm.verifyImg || '',
          },
        }
        devopsLogin(paramData)
          .then((res) => {
            if (res.basic && res.basic.code == 200) {
              // console.log('登录返回结果', res.data)
              // 登录成功了存下登录名
              selfLocaLStorage.setItem('loginName', this.ruleForm.devopsUser)
              if (res.data.sid) {
                res.data.sid = AES.decrypt(
                  res.data.sid,
                  MD5(this.ruleForm.devopsPwd)
                )
              }
              // 在SAVE_USERINFO中处理
              this.SAVE_USERINFO(res.data)
              // 获取当前登录用户的权限
              this.getUserInfo(res.data)
            } else {
              console.error(res)
            }
          })
          .catch((error) => {
            console.error(error)
            if (
              error.resData &&
              error.resData.imgData &&
              error.resData.idCode
            ) {
              this.verifyImgSrc = error.resData.imgData
              this.idCode = error.resData.idCode
            } else if (this.idCode) {
              this.getVerifyImg()
            } else {
              this.verifyImgSrc = ''
              this.idCode = ''
            }
          })
      })
    }, 1000),
    langChange(val) {
      // 中英切换时 先要去掉校验 注释
      let that = this
      that.loginLang = val
      that.$nextTick(() => {
        that.$refs['ruleForm'].clearValidate()
        // 给插件发送消息，通知语言
        // setLang() //给插件传翻译
        location.reload() // 参照用户配置那里切换语言直接刷新
      })
    },
    getVerifyImg() {
      getVerifyImg({ type: 'noData' })
        .then((res) => {
          const { data } = res
          this.verifyImgSrc = data.imgCodeImgData
          this.idCode = data.idCode
          this.ruleForm.verifyImg = ''
        })
        .catch((error) => {
          this.$message.error(error.msg)
        })
    },
    // 获取当前登录用户的权限
    getUserInfo(res) {
      devopsUserAuthFunc({ querySelf: true, containAreaExtraData: true })
        .then((res2) => {
          const { data } = res2
          // const authList = data.filter((item) => item.resType !== 'area')
          // const authResList = authList.map((item) => item.resCode)
          // 根据新的权限数据生成数组
          const authResSet = new Set()
          const dcResCodeObj = {}
          data.forEach((item) => {
            const { menuCode, resCodeList, dcResCodeMap } = item
            authResSet.add(`${menuCode}_qry`)
            resCodeList.forEach((item) => {
              authResSet.add(`${menuCode}_${item}`)
            })
            if (Object.keys(dcResCodeMap).length > 0) {
              dcResCodeObj[menuCode] = dcResCodeMap
            }
          })
          const authResList = Array.from(authResSet)
          this.SAVE_USERINFO({
            ...res,
            authResList,
            dcResCodeObj,
          })
          // 根据路由配置和用户权限找到可访问的页面
          this.redirectToAuthorizedPage(authResList)
        })
        .catch((err) => {
          console.error(err)
        })
    },

    // 根据用户权限查找可访问的页面并跳转
    redirectToAuthorizedPage(authResList) {
      // 获取路由配置
      const routes = this.$router.options.routes

      // 查找主路由
      const mainRoute = routes.find((route) => route.path === '/main')
      if (
        !mainRoute ||
        !mainRoute.children ||
        mainRoute.children.length === 0
      ) {
        // 不做跳转
        this.$message.error(this.$t('noPermissionPage'))
        return
      }

      // 遍历一级路由
      for (const route of mainRoute.children) {
        // 跳过隐藏的路由
        if (!route.show) continue

        // 检查一级路由是否有权限要求
        if (route.meta && route.meta.pageAuthList) {
          // 检查用户是否有权限访问此一级路由
          const hasPermission = route.meta.pageAuthList.some((auth) =>
            authResList.includes(auth)
          )

          if (hasPermission) {
            // 如果有子路由，查找第一个有权限的子路由
            if (route.children && route.children.length > 0) {
              for (const childRoute of route.children) {
                // 跳过隐藏的子路由
                if (!childRoute.show) continue

                // 检查子路由是否有权限要求
                if (childRoute.meta && childRoute.meta.pageAuthList) {
                  // 检查用户是否有权限访问此子路由
                  const hasChildPermission = childRoute.meta.pageAuthList.some(
                    (auth) => authResList.includes(auth)
                  )

                  if (hasChildPermission) {
                    // 找到第一个有权限的子路由，跳转到该页面
                    this.$router.push(childRoute.path)
                    return
                  }
                } else {
                  // 如果子路由没有权限要求，直接跳转
                  this.$router.push(childRoute.path)
                  return
                }
              }
            } else {
              // 如果没有子路由，直接跳转到一级路由
              this.$router.push(route.path)
              return
            }
          }
        }
      }

      // 如果没有找到任何有权限的路由，提示用户无权限
      this.$message.error(this.$t('noPermissionPage'))
    },
  },
}
</script>
<style lang="scss">
input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  -webkit-text-fill-color: #ededed !important; //字体颜色

  box-shadow: 0 0 0px 1000px transparent inset !important;

  background-color: transparent;

  background-image: none;

  transition: background-color 50000s ease-in-out 0s; //背景色透明  生效时长  过渡效果  启用时延迟的时间
}

input {
  background-color: transparent;
}

.LoginPage .el-input__inner {
  height: 44px;
  line-height: 30px;
}

.LoginPage .el-input__icon {
  line-height: 44px;
}

.LoginPage .el-form-item {
  margin-bottom: 36px;
}

.LoginPage .el-form-item__error {
  line-height: 14px;
  padding-top: 4px;
  left: 40px;
}

.LoginPage {
  position: relative;
  .bg-img {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
}
.lang-select {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 99;
}
</style>
<style lang="scss" scoped>
// 改变input框背景颜色
:deep(.el-input__inner) {
  background-color: transparent !important;
  border: 1px solid #429efd;
  color: #fff;
}

:deep(.el-input--prefix .el-input__inner) {
  padding-left: 50px;
}

:deep(.el-input__prefix) {
  left: 16px;
}

.el-dialog {
  margin-top: 25vh !important;
}

.all-login {
  width: 100%;
  height: 100%;

  .LoginPage {
    background-color: #f5f5f5;
    position: relative;
    min-height: 600px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .login_container {
      width: 450px;
      height: 600px;
      position: relative;
      .client_logo {
        width: 80%;
        text-align: center;
        margin: 60px auto 20px;
        img {
          width: auto;
          max-height: 80px;
        }
      }
      .client_logo_title {
        color: white;
        font-size: 39px;
        text-align: center;
      }

      .main_container {
        text-align: center;
        padding-top: 20px;
        position: absolute;
        left: 0;
        right: 0;
        margin: auto;

        .input_style {
          width: 368px;
          height: 44px;
          line-height: 30px;
          background-color: transparent !important;
        }

        .prefix-front {
          vertical-align: middle;
          width: 16px;
          height: 16px;
        }

        .login_in {
          width: 368px;
          height: 44px;
          margin: 12px auto 12px auto;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 4px;
          color: #fff;
        }

        .error_info {
          color: #f56c6c;
          font-size: 12px;
          padding: 12px 0;
        }
      }
    }
  }
}
.verify-img-wrap {
  display: flex;
  justify-content: center;
  .el-input {
    margin-top: 0;
    margin-right: 20px;
    width: 256px;
  }
}
</style>
