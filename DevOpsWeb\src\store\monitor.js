/**
 * 全局 环境 和 dcName
 */
import { getLocale } from '@/lang'
import { getDcInfoListApi } from '@/api/home.js'
import { getMonitorConfList } from '@/api/systemAlarm.js'

export default {
  namespaced: true,
  state: () => ({
    dcInfoList: null,
    clusterNameObj: {},
    clusterCodeNameObj: {},
    monitorConfList: [],
  }),
  mutations: {
    DC_INFO_LIST(state, data) {
      state.dcInfoList = data
      // 遍历dc-info,得到所有集群中文名字和国际化翻译的对象
      const obj = {}
      const codeObj = {}
      data.forEach((dcItem) => {
        const { areas = [] } = dcItem
        areas.forEach((areaItem) => {
          obj[areaItem.name] = areaItem.name
          codeObj[areaItem.code] = areaItem.name
        })
      })
      state.clusterNameObj = obj
      state.clusterCodeNameObj = codeObj
    },
    MONITOR_CONF_LIST(state, data) {
      state.monitorConfList = data
    },
  },
  actions: {
    // 查询DC区域列表
    fetchDcInfoListRequest({ commit }) {
      return getDcInfoListApi(getLocale()).then((res) => {
        if (res && res.data) {
          commit('DC_INFO_LIST', res.data)
        }
      })
    },
    // 查询指标配置
    fetchMonitorConfRequest({ commit }) {
      return getMonitorConfList().then((res) => {
        if (res && res.data) {
          commit('MONITOR_CONF_LIST', res.data)
        }
      })
    },
  },
}
