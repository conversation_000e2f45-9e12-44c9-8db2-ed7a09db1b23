<template>
  <div
    :id="`barchart-${echartKey}`"
    class="echart-wrapper"
    style="background-color: #fff"
  />
</template>

<script>
import { throttle } from '@/utils/common'

const highColorList = [
  'rgba(145, 204, 117, 0.8)',
  'rgba(250, 200, 88, 0.8)',
  'rgba(238, 102, 102, 0.8)',
  'rgba(115, 192, 222, 0.8)',
  'rgba(59, 162, 144, 0.8)',
  'rgba(252, 132, 82, 0.8)',
  'rgba(154, 96, 180, 0.8)',
  'rgba(234, 124, 204, 0.8)',
  'rgba(84, 112, 198, 0.8)',
]
const lowColorList = [
  'rgba(145, 204, 117, 0.3)',
  'rgba(250, 200, 88, 0.3)',
  'rgba(238, 102, 102, 0.3)',
  'rgba(115, 192, 222, 0.3)',
  'rgba(59, 162, 144, 0.3)',
  'rgba(252, 132, 82, 0.3)',
  'rgba(154, 96, 180, 0.3)',
  'rgba(234, 124, 204, 0.3)',
  'rgba(84, 112, 198, 0.3)',
]
export default {
  props: {
    echartKey: {
      type: String,
      default: '',
    },
    seriesObj: {
      type: Object,
      default: () => null,
    },
    title: {
      type: String,
      default: '',
    },
    showFullScreen: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      chartInstance: null, // echarts实例
      resizeObserver: null, // 监听尺寸变化的实例
    }
  },
  watch: {
    seriesObj: {
      handler(val) {
        if (val) {
          if (!this.chartInstance) {
            this.$nextTick(() => {
              // 确保dom元素已经加载完成
              this.initCharts()
            })
          } else {
            this.$nextTick(() => {
              // 确保dom元素已经加载完成
              // this.chartInstance.clear()
              const option = this.getOption(val)
              this.chartInstance.setOption(option, { notMerge: true })
              // 无需手点缩放,自动触发
              this.chartInstance.dispatchAction({
                type: 'takeGlobalCursor',
                key: 'dataZoomSelect',
                dataZoomSelectActive: true,
              })
              // this.chartResize()
            })
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    if (!this.chartInstance) {
      this.initCharts()
    }
  },
  beforeDestroy() {
    const dom = document.getElementById(`barchart-${this.echartKey}`)
    if (this.resizeObserver && dom) {
      this.resizeObserver.unobserve(dom)
      this.resizeObserver = null
    }
    if (this.chartInstance) {
      this.chartInstance.dispose()
      this.chartInstance = null
    }
  },
  methods: {
    // 生成图的options配置
    getOption(seriesObj) {
      const series = seriesObj || this.seriesObj
      // invertXY表示是否翻转x轴和y轴，即柱状图是纵向柱状图还是横向柱状图
      const { xAxis = [], seriesData = [], invertXY = false } = series || {}
      // console.log('seriesData', seriesData)
      const option = {
        title: {
          left: 'center',
          text: this.title,
        },
        grid: {
          top: 60, // 默认60
          bottom: 50, // 默认60
          left: 60, // 默认10%
          right: 60, // 默认10%
          containLabel: true, // 默认显示全文字
        },
        toolbox: {
          itemSize: 14, // 图标大小
          feature: {
            saveAsImage: {
              title: this.$t('save'),
              iconStyle: {
                borderWidth: 1.5,
              },
            },
            myFull: {
              // 全屏
              show: this.showFullScreen,
              title: this.$t('fullScreen'),
              icon: 'path://M160 96h192q14.016 0.992 23.008 10.016t8.992 22.496-8.992 22.496T352 160H160v192q0 14.016-8.992 23.008T128 384t-23.008-8.992T96 352V96h64z m0 832H96v-256q0-14.016 8.992-23.008T128 640t23.008 8.992T160 672v192h192q14.016 0 23.008 8.992t8.992 22.496-8.992 22.496T352 928H160zM864 96h64v256q0 14.016-8.992 23.008T896 384t-23.008-8.992T864 352V160h-192q-14.016 0-23.008-8.992T640 128.512t8.992-22.496T672 96h192z m0 832h-192q-14.016-0.992-23.008-10.016T640 895.488t8.992-22.496T672 864h192v-192q0-14.016 8.992-23.008T896 640t23.008 8.992T928 672v256h-64z',
              onclick: () => {
                this.$emit('fullScreen')
              },
            },
          },
        },
        legend: {
          show: seriesData.length > 1,
          type: 'scroll', //过多类型时自适应分页显示
          orient: 'horizontal', //方向horizontal水平显示，vertical垂直显示
          top: 30,
          data: (seriesData || []).map((item) => item.name),
        },
        tooltip: {
          trigger: 'axis',
          appendToBody: false,
          position: function (point, params, dom, rect, size) {
            // point: 鼠标位置 [x, y]。
            // params: 与当前鼠标位置相关的数据信息对象。
            // dom: tooltip 的 DOM 节点。
            // rect: 绑定了 tooltip 的图形的包围盒。
            // size:  包括 size.contentSize 和 size.viewSize，其中每个都是 [width, height] 形式的数组。
            // 返回值是 [x, y] 形式的数组，表示 tooltip 的位置。
            // 以下代码确保 tooltip 不会超出边界
            var result = [point[0], point[1]]
            if (result[0] + size.contentSize[0] > size.viewSize[0]) {
              result[0] = size.viewSize[0] - size.contentSize[0]
            }
            if (result[1] + size.contentSize[1] > size.viewSize[1]) {
              result[1] = size.viewSize[1] - size.contentSize[1]
            }
            return result
          },
          // extraCssText: 'z-index:999'
        },
        xAxis: {
          type: invertXY ? 'value' : 'category',
          // boundaryGap: true, // x轴两侧不留白
          data: invertXY ? null : xAxis.slice(),
        },
        yAxis: {
          type: invertXY ? 'category' : 'value',
          boundaryGap: invertXY ? true : false, // x轴两侧不留白,
          data: invertXY ? xAxis.slice() : null,
          name: invertXY && seriesData[0] ? seriesData[0].name : '',
          nameLocation: 'start',
          nameTextStyle: {
            padding: [10, 0, 0, 0],
          },
        },
        series: seriesData.map((item, index) => {
          return {
            type: 'bar',
            name: item.name,
            barMinHeight: 0, // 柱条最小高度，可用于防止某数据项的值过小而影响交互。
            barMaxWidth: 40, // 设置柱子的最大宽度为10
            itemStyle: {
              color: highColorList[index],
            },
            areaStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: highColorList[index % highColorList.length],
                },
                {
                  offset: 1,
                  color: lowColorList[index % highColorList.length],
                },
              ]),
            },
            // data: new Array(25).fill(0).map(() => parseInt(Math.random() * 100)),
            data: item.data.slice(),
          }
        }),
      }
      return option
    },
    // 初始化图表
    initCharts() {
      this.chartInstance = this.$echarts.init(
        document.getElementById(`barchart-${this.echartKey}`)
      )
      const option = this.getOption()
      this.chartInstance.setOption(option, { notMerge: true })
      this.chartResize()
      const dom = document.getElementById(`barchart-${this.echartKey}`)
      if (this.resizeObserver) {
        // const $el = document.getElementById(`barchart-${this.echartKey}`)
        this.resizeObserver.unobserve(dom)
      }
      // console.log('fn', this.chartResize)
      const resizeThrottled = throttle(this.chartResize, 100)
      this.resizeObserver = new ResizeObserver(() => resizeThrottled())
      // this.resizeObserver = new ResizeObserver(() => this.chartResize())
      this.resizeObserver.observe(
        document.getElementById(`barchart-${this.echartKey}`)
      )
      // 先取消监听dataZoom事件
      this.chartInstance.off('dataZoom', this.chartDataZoom)
      // 监听dataZoom事件
      this.chartInstance.on('dataZoom', this.chartDataZoom)
    },
    // 监听父元素尺寸变化
    chartResize() {
      // 根据容器大小自动响应图表大小
      // console.log('找到父元素尺寸变化')
      const dom = document.getElementById(`barchart-${this.echartKey}`)
      if (dom) {
        let { clientWidth: width, clientHeight: height } = dom.parentElement
        // console.log('width', width, 'height', height)
        this.chartInstance && this.chartInstance.resize({ width, height })
      }
    },
    // 监听区域缩放事件
    chartDataZoom(event) {
      // console.log('dataZoom', Date.now(), event); // 打印事件详细信息
      // 你可以在这里根据event做进一步的处理
      const { batch } = event
      const { startValue, endValue, start, end } = batch[0]
      if (start === 0 && end === 100) {
        // 说明是还原
        this.$emit('timeZoom', {})
      } else {
        // 说明是缩放
        // console.log('startValue', startValue, 'endValue', endValue)
        // 找到在xAxis中对应的时间
        const { xAxis } = this.seriesObj
        const startTime = xAxis[startValue]
        const endTime = xAxis[endValue]
        // console.log('startTime', startTime, 'endTime', endTime)
        this.$emit('timeZoom', { startTime, endTime })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.echart-wrapper {
  width: 100%;
  height: 100%;
}
</style>
