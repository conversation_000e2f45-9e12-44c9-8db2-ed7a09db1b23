<template>
  <div class="deploy-patch">
    <tvt-dialog
      ref="deployPatchRef"
      :title="$t('deployPlanTab.deployPatch')"
      :show.sync="showFlag"
      width="1000px"
      :cancel-text="$t('cancel')"
      :submit-text="$t('confirm')"
      :close-on-click-modal="false"
      @close="closeDialog"
      @Cancel="closeDialog"
      @Submit="btnSave"
    >
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item prop="patchDescription">
          <tvt-input
            v-model="form.patchDescription"
            clearable
            :label="$t('deployPlanTab.remark')"
            placeholder=""
            maxlength="80"
          />
        </el-form-item>
        <el-form-item prop="packageName">
          <tvt-select-tree
            id="patchPath"
            ref="selectTree"
            v-model="form.packageName"
            :options="ossPathList"
            :default-props="defaultProps"
            multiple
            clearable
            required
            :label="$t('deployPlanTab.patchPackageSelect')"
            class="selectTree"
            @change="handleCheckChange"
          >
          </tvt-select-tree>
        </el-form-item>
      </el-form>
    </tvt-dialog>
  </div>
</template>

<script>
import { deployPatch, getOssPackage } from '@/api/installDeploy.js'
export default {
  name: 'DeployPatch',
  data() {
    return {
      showFlag: false,
      ossPathList: [],
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'path',
      },
      form: {
        patchDescription: '',
        packageNameNodes: [],
        packageName: [],
      },
      rules: {
        patchDescription: [],
        packageName: [
          {
            required: true,
            message: this.$t('noEmpty', {
              name: this.$t('deployPlanTab.patchPackageSelect'),
            }),
            trigger: 'change',
          },
          { validator: this.checkOpenSourceName, trigger: 'change' },
          { validator: this.checkNoOnlyOpenSource, trigger: 'change' },
          { validator: this.checkNoRepeatName, trigger: 'change' },
          { validator: this.checkNoRepeatProject, trigger: 'change' },
        ],
      },
    }
  },
  methods: {
    // 选择java服务时，检查是否包含OpenSourceDependency
    checkOpenSourceName(rule, value, callback) {
      let packageNames = this.form.packageName.join()
      if (
        packageNames.includes('Service') &&
        !packageNames.includes('OpenSourceDependency')
      ) {
        callback(new Error(this.$t('versionTab.checkPackageName')))
      } else {
        callback()
      }
    },
    // 检查是否只选择了openSourceDependency的包
    checkNoOnlyOpenSource(rule, value, callback) {
      const selectedNodes = this.form.packageNameNodes

      // 检查是否所有选中的包都是 openSourceDependency 类型
      if (selectedNodes.length > 0) {
        const allOpenSourceDep = selectedNodes.every(
          (item) =>
            item.name &&
            item.name.toLowerCase().includes('opensourcedependency')
        )

        if (allOpenSourceDep) {
          callback(
            new Error(this.$t('deployPlanTab.cannotOnlySelectOpenSource'))
          )
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    checkNoRepeatName(rule, value, callback) {
      const set = new Set(
        this.form.packageNameNodes.map(function (item) {
          return item.parentPath
            .concat(';')
            .concat(item.businessParams.projectName)
        })
      )
      if (set.size < this.form.packageNameNodes.length) {
        callback(new Error(this.$t('versionTab.checkNoRepeatName')))
      } else {
        callback()
      }
    },
    // 检查是否存在同个服务多个版本
    checkNoRepeatProject(rule, value, callback) {
      var set = new Set(
        this.form.packageNameNodes.map(function (item) {
          return item.businessParams.projectName
        })
      )
      if (set.size < this.form.packageNameNodes.length) {
        callback(new Error(this.$t('versionTab.checkNoRepeatProject')))
      } else {
        callback()
      }
    },
    // 弹框打开时 清除第一次的自动校验回调
    open() {
      this.showFlag = true
      this.$nextTick(() => {
        this.ossPathList = []
        this.$refs['form'].resetFields()
        this.form.packageName = []
        this.form.patchDescription = ''
        this.form.packageNameNodes = []
        // 默认加载所有类型的补丁包
        this.getOssPackageData()
      })
    },
    // 获取部署配套选择项
    getOssPackageData() {
      getOssPackage('patch')
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            this.ossPathList = res.data
          } else {
            this.ossPathList = []
          }
        })
        .catch((err) => {
          this.ossPathList = []
          console.log(err)
        })
    },
    btnSave() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.$TvtLoading.show({ text: this.$t('LoadingMsg') })
          let packages = this.form.packageNameNodes.map((item) => {
            return {
              name: item.name,
              path: item.path,
              packageName:
                item.businessParams && item.businessParams.packageName,
              projectName:
                item.businessParams && item.businessParams.projectName,
              version: item.businessParams && item.businessParams.version,
            }
          })

          // 处理备注信息，添加时间戳
          const timestamp = Math.floor(Date.now() / 1000)
          let finalRemark = ''
          if (this.form.patchDescription && this.form.patchDescription.trim()) {
            // 如果备注信息存在，则在后面加上-patch-${timestamp}
            finalRemark = `${this.form.patchDescription.trim()}-patch-${timestamp}`
          } else {
            // 如果备注信息不存在，则默认为patch-${timestamp}
            finalRemark = `patch-${timestamp}`
          }

          // 这里可以调用专门的补丁部署API，目前使用相同的API
          deployPatch({
            packages: packages,
            packageTag: finalRemark,
            serviceType: 'patch',
          })
            .then((res) => {
              this.$TvtLoading.hide()
              this.$refs['form'].resetFields()
              if (res.basic && res.basic.code == 200) {
                this.$emit('btnSave')
              }
            })
            .catch((err) => {
              this.$TvtLoading.hide()
              console.log(err)
            })
          this.showFlag = false
        }
      })
    },
    // 关闭弹框
    closeDialog() {
      this.showFlag = false
    },
    handleCheckChange() {
      this.form.packageNameNodes =
        this.$refs.selectTree.$refs['patchPath-tree'].getCheckedNodes(true)
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-select-dropdown__wrap {
  max-height: 500px;
}
::v-deep .el-dialog__wrapper {
  align-items: start;
  .el-dialog {
    margin-top: 15vh !important;
  }
}
</style>
