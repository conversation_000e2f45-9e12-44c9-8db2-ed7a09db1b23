<template>
  <tvt-dialog
    :title="$t('detail')"
    :show.sync="showFlag"
    width="800px"
    custom-class="applog-detail-dialog"
    :cancel-text="$t('cancel')"
    :submit-text="$t('confirm')"
    :close-on-click-modal="false"
    @close="closeDialog"
    @Cancel="closeDialog"
  >
    <div class="applog-detail-wrap">
      <ul class="applog-detail-list">
        <li class="applog-detail-line">
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.logid') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.logid }}
            </div>
          </div>
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.logver') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.logver }}
            </div>
          </div>
        </li>
        <li class="applog-detail-line">
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.time') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.time }}
            </div>
          </div>
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.timezone') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.timezone }}
            </div>
          </div>
        </li>
        <li class="applog-detail-line">
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.errors') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.errors }}
            </div>
          </div>
        </li>
        <li class="applog-detail-line">
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">APPID</div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.appid }}
            </div>
          </div>
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">UUID</div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.uuid }}
            </div>
          </div>
        </li>
        <li class="applog-detail-line">
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.hwmodel') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.hwmodel }}
            </div>
          </div>
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.mobileVer') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.firmver }}
            </div>
          </div>
        </li>
        <li class="applog-detail-line">
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.memory') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.memory }}
            </div>
          </div>
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.cpucore') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.cpucore }}
            </div>
          </div>
        </li>
        <li class="applog-detail-line">
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.appver') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.appver }}
            </div>
          </div>
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.logintime') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.logintime }}
            </div>
          </div>
        </li>
        <li class="applog-detail-line">
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.mcc') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.mcc }}
            </div>
          </div>
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.nettype') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.nettype }}
            </div>
          </div>
        </li>
        <li class="applog-detail-line">
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.conntype') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.conntype }}
            </div>
          </div>
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.sn') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.sn }}
            </div>
          </div>
        </li>
        <li class="applog-detail-line">
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.isreduce') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{
                record.isreduce
                  ? $t('alarmHistoryTab.yes')
                  : $t('alarmHistoryTab.no')
              }}
            </div>
          </div>
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.reduceformat') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.reduceformat }}
            </div>
          </div>
        </li>
        <li class="applog-detail-line">
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.isencrypt') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{
                record.isencrypt
                  ? $t('alarmHistoryTab.yes')
                  : $t('alarmHistoryTab.no')
              }}
            </div>
          </div>
          <div class="applog-detail-item">
            <div class="applog-detail-label ellipsis-text">
              {{ $t('appLogTab.encryformat') }}
            </div>
            <div class="applog-detail-value ellipsis-text">
              {{ record.encryformat }}
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div slot="footer" class="applog-dialog-footer">
      <el-button size="medium" type="primary" @click="closeDialog">{{
        $t('close')
      }}</el-button>
    </div>
  </tvt-dialog>
</template>

<script>
import { getAppLogDetail } from '@/api/basicTool'
export default {
  name: 'AppLogDetail',
  data() {
    return {
      showFlag: false,
      record: {},
      loading: false,
    }
  },
  methods: {
    // 获取APP日志详情
    async handleDetail(row) {
      // 展示详情弹窗
      try {
        const { logid = '' } = row
        const { data } = await getAppLogDetail({ logid })
        // console.log('app日志详情', row, data)
        this.record = data
      } catch (err) {
        console.error(err)
      }
    },
    open(row) {
      console.log('row', row)
      this.showFlag = true
      this.handleDetail(row)
    },
    closeDialog() {
      this.showFlag = false
      this.record = {}
    },
  },
}
</script>
<style lang="scss" scoped>
.applog-detail-wrap {
  flex: 1;
  .common-config-wrap {
    width: 100%;
  }
}

.alarm-config-table {
  width: 100%;
}

.applog-detail-line {
  width: 100%;
  height: 35px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.applog-detail-line:nth-child(even) {
  background: #eeeeee;
}

.applog-detail-item {
  flex: 1;
  height: 100%;
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
}

.applog-detail-label {
  width: 100px;
  height: 100%;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
}

.applog-detail-value {
  flex: 1;
  height: 100%;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
}

.ellipsis-text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .applog-detail-dialog .applog-dialog-footer {
  display: inline-block;
  width: 100%;
  height: 52px;
  text-align: right;
  border-top: 1px solid #e0e0e0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-size: 14px;
  padding: 5px 20px;
  button {
    border-radius: 16px;
    height: 36px;
  }
}
</style>
