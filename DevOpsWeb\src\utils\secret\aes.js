/**
 * AES加密工具
 */

const CryptoJS = require('crypto-js')

/**
 * 加密
 * @param word 明文
 * @param key 秘钥
 * mode和padding 需要与服务端相对应
 */
function encrypt(word, key) {
  let srcs = CryptoJS.enc.Utf8.parse(word)
  let aesKey = CryptoJS.enc.Utf8.parse(key)
  let encrypted = CryptoJS.AES.encrypt(srcs, aesKey, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.ZeroPadding,
  })

  return encrypted.toString()
}

/**
 * 解密
 * @param word 密文
 * @param key 秘钥
 * mode和padding 需要与服务端相对应
 */
function decrypt(word, key) {
  let aesKey = CryptoJS.enc.Utf8.parse(key)
  let decrypt = CryptoJS.AES.decrypt(word, aesKey, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.ZeroPadding,
  })
  let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
  return decryptedStr.toString()
}

export default {
  decrypt,
  encrypt,
}
