<template>
  <div>
    <el-select
      v-model="locale"
      :popper-append-to-body="false"
      size="small"
      @change="handleChange"
    >
      <el-option
        v-for="item in langOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { langOptions, getLocale } from '@/lang'
export default {
  name: 'LangSelect',
  data() {
    return {
      langOptions,
      locale: getLocale(),
    }
  },
  created() {
    this.$i18n.locale = this.locale
  },
  methods: {
    handleChange(locale) {
      sessionStorage.setItem('localeType', locale)
      this.$i18n.locale = locale
      this.$emit('changeLang', locale)
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .tvt-select-prefix {
  display: none;
}
::v-deep .el-input__inner {
  height: 32px;
  line-height: 32px;
}

::v-deep .el-input__icon {
  line-height: 32px;
}

::v-deep .el-select {
  border-radius: 4px;
  .el-input {
    background: rgba(0, 0, 0, 0);
  }
}
</style>
