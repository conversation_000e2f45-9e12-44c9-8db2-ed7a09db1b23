import request from '@/api/request'
const baseUrl = '/dev-ops'

// 获取微服务列表--用作微服务下拉选项
export const getServiceList = (data) =>
  request.post(`${baseUrl}/basic/service-list`, data)

// 获取Restfull接口日志
export const getApiLogList = (data) =>
  request.post(`${baseUrl}/basic/api-log-list`, data)

// 获取微服务运行日志
export const getServiceLogList = (data) =>
  request.post(`${baseUrl}/basic/service-log-list`, data)

// 获取kafka topic列表--用作topic下拉选项
export const getTopicList = (data) =>
  request.post(`${baseUrl}/basic/topic-list`, data)

// 获取kafka查询日志
export const getKafkaLogList = (data) =>
  request.post(`${baseUrl}/basic/kafka-list`, data)

// 获取桶列表
export const getOssBucketList = (data) =>
  request.post(`${baseUrl}/basic/oss-bucket-list`, data)

// 获取oss文件列表
export const getOssFileList = (data) =>
  request.post(`${baseUrl}/basic/oss-object-list`, data)

// 下载微服务文件 -- 废弃
const CONFIG = { responseType: 'blob' }
export const downloadFile = (data) =>
  request.post(`${baseUrl}/basic/download`, data, CONFIG)

// 下载微服务日志文件
export const downloadServiceLog = (data) =>
  request.post(`${baseUrl}/basic/service-log-download`, data, CONFIG)

// 下载App日志文件
const CONFIG2 = { responseType: 'blob', ContentType: 'text/plain' }
export const downloadAppLog = (data) => {
  return request.post(`${baseUrl}/basic/app-log-download`, data, CONFIG2)
}

// 下载设备日志文件
export const downloadDevLog = (data) =>
  request.post(`${baseUrl}/basic/dev-log-download`, data, CONFIG)

// 下载OSS文件
export const downloadOssObject = (data) =>
  request.post(`${baseUrl}/basic/oss-object-download`, data, CONFIG)

// 获取redis实例列表
export const getRedisInstanceList = (data) =>
  request.post(`${baseUrl}/basic/redis-instance-list`, data)

// 获取redis key
export const getRedisKeyList = (data, config = {}) =>
  request.post(`${baseUrl}/basic/redis-key-list`, data, config)

// 获取redis value
export const getRedisValueList = (data) =>
  request.post(`${baseUrl}/basic/redis-value`, data)

// 获取app日志列表
export const getAppLogList = (data) =>
  request.post(`${baseUrl}/basic/app-log-list`, data)

// 获取app日志详情
export const getAppLogDetail = (data) =>
  request.post(`${baseUrl}/basic/app-log-detail`, data)

// 获取P2P 的所有域名
export const getDomainList = (data) =>
  request.post(`${baseUrl}/config/domain-list`, data)

// 获取P2P Test表格
export const getP2PTest = (data) =>
  request.post(`${baseUrl}/basic/p2p-connect-test`, data)

// 获取主机名下拉选项
export const getHostNameList = (data) =>
  request.post(`${baseUrl}/basic/hostname-list`, data)

// 获取设备日志列表
export const getDeviceLogList = (data) =>
  request.post(`${baseUrl}/basic/dev-log-list`, data)

// 获取操作日志列表
export const getOperateLogList = (data) =>
  request.post(`${baseUrl}/basic/operation-log`, data)

// 获取运维服务操作日志类型列表
export const getOperateType = (data) =>
  request.post(`${baseUrl}/basic/operation-type`, data)

// 获取定时任务工程名和执行类下拉列表
export const getTimerClassList = (data) =>
  request.post(`${baseUrl}/basic/timer-class-list`, data)

// 获取定时任务日志列表
export const getScheduleTask = (data) =>
  request.post(`${baseUrl}/basic/timer-execute-list`, data)

// 获取mysql查询列表
export const getMysqlQuery = (data) =>
  request.post(`${baseUrl}/basic/mysql-query`, data)
