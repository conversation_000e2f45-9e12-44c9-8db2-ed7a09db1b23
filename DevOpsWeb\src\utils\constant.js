import i18n from '@/lang'

export const TESTING_TYPE_LIST = () => [
  // 类型
  // { label: 'OS', value: '1' },
  { label: i18n.t('envTesting.dataBase'), value: '数据库' },
  { label: i18n.t('configTab.projectName'), value: '微服务' },
]
export const TESTING_ITEM_LIST = () => [
  // 检测项下拉框
  // { label: 'jdk', value: '1'},
  // { label: 'rsync', value: '2'},
  // { label: 'CPU', value: '3'},
  // { label: i18n.t('resourceMonitor.memory'), value: '4'},
  // { label: i18n.t('resourceMonitor.diskSpace'), value: '5'},
  // { label: 'NFS', value: '6'},
  // { label: 'mysql', value: '7'},
  // { label: 'redis', value: '8'},
  // { label: 'kafka', value: '9'},
  // { label: 'eureka', value: '10'},
  // { label: 'mongo', value: '11'},
  // { label: 'nginx', value: '12'},
  { label: i18n.t('envTesting.dbTableAndField'), value: '字段检测' },
  { label: i18n.t('envTesting.paramConfig'), value: '配置参数检测' },
  // { label: i18n.t('envTesting.microServiceStatus'), value: '14'},
]
export const TESTING_RES_LIST = () => [
  // 检测结果
  // { label: i18n.t('envTesting.normal'), value: 'success' },
  { label: 'failed', value: 'failed' },
]

export const APPLICATION_TYPE = [
  // 应用类型
  { label: 'device', value: 'device' },
  { label: 'app', value: 'app' },
  { label: 'system', value: 'system' },
  { label: 'business', value: 'business' },
]

export const TEMPLATE_TYPE = [
  // 模板类型
  { label: 'rdc-tvt', value: 'rdc-tvt' },
  { label: 'rdc-customer', value: 'rdc-customer' },
  { label: 'ddc-ops', value: 'ddc-ops' },
  { label: 'ddc-bus', value: 'ddc-bus' },
  { label: 'ddc-app', value: 'ddc-app' },
  { label: 'ddc-parnter', value: 'ddc-parnter' },
  { label: 'ddc-user', value: 'ddc-user' },
]

export const MODULE_NAME = [
  { label: 'app-api', value: 'app-api' },
  { label: 'rdc-tvt', value: 'rdc-tvt' },
  { label: 'ddc-bus', value: 'ddc-bus' },
  { label: 'ddc-ops', value: 'ddc-ops' },
  { label: 'rdc-customer', value: 'rdc-customer' },
  { label: 'bus-partner', value: 'bus-partner' },
  { label: 'bus-user', value: 'bus-user' },
]
export const STATUS_OBJ = () => ({
  0: i18n.t('collecting'),
  1: i18n.t('collectSucc'),
  2: i18n.t('collectFail'),
})

export const DOMAIN_TYPE = [
  // 域名类型
  { label: 'rdc-tvt', value: 'rdc-tvt' },
  { label: 'rdc-customer', value: 'rdc-customer' },
  { label: 'ddc-user', value: 'ddc-user' },
  { label: 'ddc-parnter', value: 'ddc-parnter' },
  { label: 'ddc-ops', value: 'ddc-ops' },
  { label: 'ddc-bus', value: 'ddc-bus' },
  { label: 'ddc-app', value: 'ddc-app' },
  { label: 'ddc-udt-redirect', value: 'ddc-udt-redirect' },
]
