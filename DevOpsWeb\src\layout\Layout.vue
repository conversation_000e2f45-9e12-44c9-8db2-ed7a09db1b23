<template>
  <div v-loading="isRouteLoading" class="app-wrapper">
    <div class="left">
      <sidebar ref="sidebar" class="sidebar-container" @change="changeWidth" />
    </div>
    <div class="right" :style="`width: ${width}`">
      <navbar class="right-navbar" />
      <app-main class="app-main" />
    </div>
  </div>
</template>

<script>
import AppMain from './components/AppMain.vue'
import Navbar from './components/Navbar.vue'
import Sidebar from './components/Sidebar.vue'
export default {
  name: 'LayOut',
  components: {
    AppMain,
    Navbar,
    Sidebar,
  },
  data() {
    return {
      width: `calc(100vw - 240px)`,
      isRouteLoading: false,
      loadingTimer: null,
    }
  },
  computed: {},
  async mounted() {
    try {
      await this.$store.dispatch('params/fetchEnvDcNameRequest')
    } catch (err) {
      console.log(err)
    }

    // 监听路由变化，智能显示loading
    this.setupRouteLoading()
  },
  beforeDestroy() {
    // 清理定时器
    if (this.loadingTimer) {
      clearTimeout(this.loadingTimer)
    }
  },
  methods: {
    changeWidth() {
      this.width = `calc(100vw - ${this.$refs.sidebar.isCollapse ? 64 : 240}px)`
    },

    // 设置路由loading监听
    setupRouteLoading() {
      // 路由开始时智能显示loading
      this.$router.beforeEach((to, from, next) => {
        // 只有在真正的路由切换时才显示loading
        if (from.name !== null) {
          this.showRouteLoading()
        }
        next()
      })

      // 路由完成时隐藏loading
      this.$router.afterEach(() => {
        this.hideRouteLoading()
      })
    },

    // 智能显示loading（延迟200ms避免闪烁）
    showRouteLoading() {
      // 清除之前的定时器
      if (this.loadingTimer) {
        clearTimeout(this.loadingTimer)
      }

      // 延迟显示，避免快速加载时的闪烁
      this.loadingTimer = setTimeout(() => {
        this.isRouteLoading = true
      }, 200)
    },

    // 隐藏loading
    hideRouteLoading() {
      // 清除定时器
      if (this.loadingTimer) {
        clearTimeout(this.loadingTimer)
        this.loadingTimer = null
      }
      // 立即隐藏
      this.isRouteLoading = false
    },
  },
}
</script>
<style>
.sidebar-container .el-menu {
  border-right: none !important;
}
</style>
<style lang="scss" scoped>
@import '~@/styles/mixin.scss';
@import '~@/styles/variables.scss';

.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  overflow: hidden;
  .left {
    height: 100%;
    .sidebar-container {
      height: 100%;
      background-color: #36393f;
      overflow-y: auto;
    }
  }
  .right {
    flex: 1;
    background-color: #fafbfb;
    height: 100%;
    position: relative;
    .right-navbar {
      right: 0;
    }
    .app-main {
      height: calc(100% - 50px);
    }
  }
}
</style>
