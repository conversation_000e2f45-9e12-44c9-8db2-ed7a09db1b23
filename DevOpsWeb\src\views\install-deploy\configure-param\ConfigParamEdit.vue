<template>
  <tvt-dialog
    :title="$t('edit')"
    :show.sync="showFlag"
    width="730px"
    custom-class="config-param-dialog"
    :cancel-text="$t('cancel')"
    :submit-text="$t('confirm')"
    :close-on-click-modal="false"
    @close="closeDialog"
    @Cancel="closeDialog"
    @Submit="btnSave"
  >
    <div class="alarm-edit-wrap">
      <el-form
        ref="configForm"
        :inline="true"
        :model="configParamForm"
        label-position="left"
        :rules="configRules"
        :label-width="['Oss', 'S3'].includes(type) ? '200px' : '120px'"
      >
        <div class="common-config-wrap">
          <template v-if="type === 'Mysql'">
            <el-row>
              <el-form-item prop="mysql-ip-port" label="url">
                <tvt-input
                  v-model="configParamForm['mysql-ip-port']"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item prop="mysql-user" label="username">
                <tvt-input
                  v-model="configParamForm['mysql-user']"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item prop="mysql-cipher" label="password">
                <tvt-input
                  v-model="configParamForm['mysql-cipher']"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
          </template>
          <template v-if="type === 'Redis'">
            <el-row>
              <el-form-item prop="redis-nodes" label="url">
                <tvt-input
                  v-model="configParamForm['redis-nodes']"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item prop="redis-cipher" label="password">
                <tvt-input
                  v-model="configParamForm['redis-cipher']"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item prop="redis-mode" label="model">
                <tvt-input
                  v-model="configParamForm['redis-mode']"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
          </template>
          <template v-if="type === 'Mongo'">
            <el-row>
              <el-form-item prop="mongodb-ip-port" label="url">
                <tvt-input
                  v-model="configParamForm['mongodb-ip-port']"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item prop="mongodb-user" label="username">
                <tvt-input
                  v-model="configParamForm['mongodb-user']"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item prop="mongodb-cipher" label="password">
                <tvt-input
                  v-model="configParamForm['mongodb-cipher']"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
          </template>
          <template v-if="type === 'Nfs'">
            <el-row>
              <el-form-item prop="nfs-server" label="url">
                <tvt-input
                  v-model="configParamForm['nfs-server']"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
          </template>
          <template v-if="['Oss', 'S3'].includes(type)">
            <el-row>
              <el-form-item prop="storageUrl" label="storageUrl">
                <tvt-input
                  v-model="configParamForm.storageUrl"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item prop="endpoint" label="endpoint">
                <tvt-input
                  v-model="configParamForm.endpoint"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item prop="accessKeyId" label="accessKeyId">
                <tvt-input
                  v-model="configParamForm.accessKeyId"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item prop="accessKeySecret" label="accessKeySecret">
                <tvt-input
                  v-model="configParamForm.accessKeySecret"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item
                prop="accessDownloadKeyId"
                label="accessDownloadKeyId"
              >
                <tvt-input
                  v-model="configParamForm.accessDownloadKeyId"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item
                prop="accessDownloadKeySecret"
                label="accessDownloadKeySecret"
              >
                <tvt-input
                  v-model="configParamForm.accessDownloadKeySecret"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item prop="roleArn" label="roleArn">
                <tvt-input
                  v-model="configParamForm.roleArn"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item prop="roleSessionName" label="roleSessionName">
                <tvt-input
                  v-model="configParamForm.roleSessionName"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item prop="bucketName" label="bucketName">
                <tvt-input
                  v-model="configParamForm.bucketName"
                  maxlength="100"
                  style="width: 400px"
                />
              </el-form-item>
            </el-row>
          </template>
        </div>
      </el-form>
    </div>
    <div slot="footer" class="alarm-dialog-footer">
      <el-button size="medium" @click="closeDialog">{{
        $t('cancel')
      }}</el-button>
      <el-button
        size="medium"
        :loading="loading"
        type="primary"
        @click="btnSave"
        >{{ $t('confirm') }}</el-button
      >
    </div>
  </tvt-dialog>
</template>
<script>
import { deepCopy } from '@/utils/common'
export default {
  name: 'AlarmConfigEdit',
  data() {
    return {
      showFlag: false,
      configParamForm: {},
      configRules: {
        'mysql-ip-port': [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'url' }),
            trigger: 'change',
          },
        ],
        'mysql-user': [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'username' }),
            trigger: 'change',
          },
        ],
        'mysql-cipher': [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'password' }),
            trigger: 'change',
          },
        ],
        'redis-nodes': [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'url' }),
            trigger: 'change',
          },
        ],
        'redis-cipher': [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'password' }),
            trigger: 'change',
          },
        ],
        'redis-mode': [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'model' }),
            trigger: 'change',
          },
        ],
        'mongodb-ip-port': [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'url' }),
            trigger: 'change',
          },
        ],
        'mongodb-user': [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'username' }),
            trigger: 'change',
          },
        ],
        'mongodb-cipher': [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'password' }),
            trigger: 'change',
          },
        ],
        'nfs-server': [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'url' }),
            trigger: 'change',
          },
        ],
        storageUrl: [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'storageUrl' }),
            trigger: 'change',
          },
        ],
        endpoint: [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'endpoint' }),
            trigger: 'change',
          },
        ],
        accessKeyId: [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'accessKeyId' }),
            trigger: 'change',
          },
        ],
        accessKeySecret: [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'accessKeySecret' }),
            trigger: 'change',
          },
        ],
        accessDownloadKeyId: [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'accessDownloadKeyId' }),
            trigger: 'change',
          },
        ],
        accessDownloadKeySecret: [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'accessDownloadKeySecret' }),
            trigger: 'change',
          },
        ],
        roleArn: [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'roleArn' }),
            trigger: 'change',
          },
        ],
        roleSessionName: [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'roleSessionName' }),
            trigger: 'change',
          },
        ],
        bucketName: [
          {
            required: true,
            message: this.$t('noEmpty', { name: 'bucketName' }),
            trigger: 'change',
          },
        ],
      },
      loading: false,
      type: null,
    }
  },
  methods: {
    open(record) {
      this.type = record.datasourceType
      const { configMap } = record
      this.configParamForm = deepCopy(configMap)
      // console.log('record', record)
      this.showFlag = true
    },
    btnSave() {
      // this.$emit('btnSave', this.filterCond.packageTag, this.multipleSelection, this.deployType)
      this.$refs.configForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.$emit('btnSave', this.configParamForm)
        }
      })
    },
    closeDialog() {
      this.showFlag = false
      this.loading = false
      this.configParamForm = {}
      this.$emit('onClose')
    },
  },
}
</script>
<style lang="scss" scoped>
.alarm-edit-wrap {
  flex: 1;
  padding: 20px;
  .common-config-wrap {
    width: 100%;
  }
}

.alarm-config-table {
  width: 100%;
}

::v-deep .el-dialog__wrapper {
  align-items: start;
  .el-dialog {
    margin-top: 15vh !important;
  }
}

::v-deep .config-param-dialog .common-config-wrap .el-form-item__content {
  line-height: 34px;
}

::v-deep .config-param-dialog .common-config-wrap .el-form-item {
  margin-bottom: 18px;
}

::v-deep .config-param-dialog .common-config-wrap .el-form-item__label {
  text-align: justify; // label标签占满
  text-align-last: justify;
}

::v-deep
  .config-param-dialog
  .el-table
  td.el-table__cell
  div
  .el-form-item__content {
  line-height: 34px;
}

::v-deep .config-param-dialog .el-table td.el-table__cell div .el-form-item {
  margin-top: 10px;
  margin-bottom: 10px;
}

::v-deep
  .config-param-dialog
  .el-table
  td.el-table__cell
  div
  .el-form-item__error {
  top: 70%;
}

::v-deep .config-param-dialog .el-table .el-table__body .el-table__cell {
  padding: 0px;
}
::v-deep .config-param-dialog .disable-form-item .tvt-select .tvt-select-inner {
  background-color: #f5f7fa;
}
::v-deep .config-param-dialog .alarm-dialog-footer {
  display: inline-block;
  width: 100%;
  height: 52px;
  text-align: right;
  border-top: 1px solid #e0e0e0;
  padding: 10px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-size: 14px;
  padding: 5px 20px;
  button {
    border-radius: 16px;
    height: 36px;
  }
}
</style>
<style>
.config-param-dialog .el-table__body tr.hover-row > td.el-table__cell {
  background-color: unset !important;
}
.config-param-dialog
  .el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell {
  background-color: unset !important;
}
.config-param-dialog .el-table__body .cell {
  white-space: pre-line;
}
</style>
