const normal = {
  presets: [
    [
      '@babel/preset-env',
      {
        useBuiltIns: 'usage',
        corejs: 3,
        targets: {
          browsers: ['> 1%', 'last 2 versions', 'not ie <= 8'],
        },
      },
    ],
  ],
  plugins: [
    'transform-vue-jsx',
    '@babel/plugin-transform-runtime',
    [
      'component',
      {
        libraryName: 'element-ui',
        styleLibraryName: 'theme-chalk',
      },
      'element-ui',
    ],
  ],
}

const test = {
  presets: ['@babel/preset-env'],
}

module.exports = function (api) {
  api.cache(true)

  if (process.env['ENV'] === 'prod') {
    return test
  }

  return normal
}
