import Vue from 'vue'

export const ctyTypeOptions = [
  // {label: '全部', value: 'All'}, Vue.prototype.$translate('')
  { label: Vue.prototype.$translate('hostMonitor'), value: 'monitor.host' },
  {
    label: Vue.prototype.$translate('javaMicroService'),
    value: 'monitor.microservice.java',
  },
  {
    label: Vue.prototype.$translate('cplusMicroService'),
    value: 'monitor.microservice.cplus',
  },
  {
    label: Vue.prototype.$translate('webMicroService'),
    value: 'monitor.microservice.web',
  },
  {
    label: Vue.prototype.$translate('mysqlInstance'),
    value: 'monitor.mysql.instance',
  },
  { label: Vue.prototype.$translate('mysqlDb'), value: 'monitor.mysql.db' },
  { label: 'Redis', value: 'monitor.redis' },
  { label: 'Kafka', value: 'monitor.kafka' },
  { label: 'Mongo', value: 'monitor.mongo' },
  { label: 'Nginx', value: 'monitor.nginx' },
  { label: 'OSS/S3', value: 'monitor.oss' },
  { label: Vue.prototype.$translate('httpsDomain'), value: 'monitor.domain' },
  { label: Vue.prototype.$translate('certificate'), value: 'monitor.cert' },
  { label: 'NatServer', value: 'monitor.nat' },
  { label: 'RedirectServer', value: 'monitor.udt' },
  { label: 'RelayServer', value: 'monitor.relay' },
  { label: 'StunServer', value: 'monitor.stun' },
  { label: 'AccessServer', value: 'monitor.access' },
  { label: 'StunRelayServer', value: 'monitor.stunRelay' },
  { label: 'NatCluster', value: 'monitor.nat.cluster' },
  { label: 'RelayCluster', value: 'monitor.relay.cluster' },
  { label: 'stunCluster', value: 'monitor.stun.cluster' },
  { label: 'StunRelayCluster', value: 'monitor.stun.relay.cluster' },
  { label: 'Websocket', value: 'monitor.websocket' },
  { label: 'Dpikey', value: 'monitor.dpikey' },
  { label: Vue.prototype.$translate('email'), value: 'monitor.email' },
  { label: Vue.prototype.$translate('textMessage'), value: 'monitor.sms' },
  { label: Vue.prototype.$translate('account'), value: 'monitor.account' },
  { label: Vue.prototype.$translate('push2'), value: 'monitor.push' },
]

// 指标选择树的选项--用作首页的下拉
export const homeMetricsOptions = [
  {
    label: Vue.prototype.$translate('warnTab.RealTimeAlarm'),
    value: 'alarm',
  },
  // {
  //   label: 'P2P2.0',
  //   value: 'monitor.nat',
  //   // 包含原来的NatServer、RedirectServer、RelayServer
  //   children: [
  //     {
  //       label: Vue.prototype.$translate('natTab.nat_dev_online'),
  //       value: 'monitor.nat-NatServer-nat_dev_online',
  //     },
  //     {
  //       label: Vue.prototype.$translate('natTab.nat_dev_reg'),
  //       value: 'monitor.nat-NatServer-nat_dev_reg',
  //     },
  //     {
  //       label: Vue.prototype.$translate('redirectTab.redirect_dev'),
  //       value: 'monitor.udt-RedirectServer-redirect_dev',
  //     },
  //     {
  //       label: Vue.prototype.$translate('relayTab.relay_dev_req'),
  //       value: 'monitor.relay-RelayServer-relay_dev_req',
  //     },
  //     {
  //       label: Vue.prototype.$translate('relayTab.relay_session'),
  //       value: 'monitor.relay-RelayServer-relay_session',
  //     },

  //     {
  //       label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_sn_online'),
  //       value: 'monitor.nat-NatServer-nat_cleint_p2p_req_sn_online',
  //     },
  //     {
  //       label: Vue.prototype.$translate(
  //         'natTab.nat_cleint_p2p_req_token_online'
  //       ),
  //       value: 'monitor.nat-NatServer-nat_cleint_p2p_req_token_online',
  //     },
  //     {
  //       label: Vue.prototype.$translate('natTab.nat_client_offline'),
  //       value: 'monitor.nat-NatServer-nat_client_offline',
  //     },
  //     {
  //       label: Vue.prototype.$translate('natTab.nat_client_online'),
  //       value: 'monitor.nat-NatServer-nat_client_online',
  //     },
  //     {
  //       label: Vue.prototype.$translate('natTab.nat_client_reg'),
  //       value: 'monitor.nat-NatServer-nat_client_reg',
  //     },
  //     {
  //       label: Vue.prototype.$translate('natTab.nat_dev_big_heat'),
  //       value: 'monitor.nat-NatServer-nat_dev_big_heat',
  //     },
  //     {
  //       label: Vue.prototype.$translate('natTab.nat_dev_offline'),
  //       value: 'monitor.nat-NatServer-nat_dev_offline',
  //     },
  //     {
  //       label: Vue.prototype.$translate('natTab.nat_dev_small_heat'),
  //       value: 'monitor.nat-NatServer-nat_dev_small_heat',
  //     },
  //     {
  //       label: Vue.prototype.$translate('natTab.nat_dev_update_ip'),
  //       value: 'monitor.nat-NatServer-nat_dev_update_ip',
  //     },
  //     {
  //       label: Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
  //       value: 'monitor.nat-NatServer-nat_dev_big_heat_v6',
  //     },
  //     {
  //       label: Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
  //       value: 'monitor.nat-NatServer-nat_dev_small_heat_v6',
  //     },
  //     {
  //       label: Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
  //       value: 'monitor.nat-NatServer-nat_dev_update_ip_v6',
  //     },
  //     {
  //       label: Vue.prototype.$translate('natTab.nat_dev_online_dec'),
  //       value: 'monitor.nat-NatServer-nat_dev_online_dec',
  //     },
  //     {
  //       label: Vue.prototype.$translate('redirectTab.redirect_client'),
  //       value: 'monitor.udt-RedirectServer-redirect_client',
  //     },
  //     {
  //       label: Vue.prototype.$translate(
  //         'redirectTab.redirect_client_full_list'
  //       ),
  //       value: 'monitor.udt-RedirectServer-redirect_client_full_list',
  //     },
  //     {
  //       label: Vue.prototype.$translate('redirectTab.redirect_dev_inc'),
  //       value: 'monitor.udt-RedirectServer-redirect_dev_inc',
  //     },
  //     {
  //       label: Vue.prototype.$translate('relayTab.relay_client_req'),
  //       value: 'monitor.relay-RelayServer-relay_client_req',
  //     },
  //     {
  //       label: Vue.prototype.$translate('relayTab.relay_session_end'),
  //       value: 'monitor.relay-RelayServer-relay_session_end',
  //     },
  //     {
  //       label: Vue.prototype.$translate('relayTab.relay_speed_limit_count'),
  //       value: 'monitor.relay-RelayServer-relay_speed_limit_count',
  //     },
  //   ],
  // },
  {
    label: 'NatServer',
    value: 'monitor.nat',
    children: [
      {
        label: Vue.prototype.$translate('natTab.nat_dev_online'),
        value: 'monitor.nat-NatServer-nat_dev_online',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_reg'),
        value: 'monitor.nat-NatServer-nat_dev_reg',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_sn_online'),
        value: 'monitor.nat-NatServer-nat_cleint_p2p_req_sn_online',
      },
      {
        label: Vue.prototype.$translate(
          'natTab.nat_cleint_p2p_req_token_online'
        ),
        value: 'monitor.nat-NatServer-nat_cleint_p2p_req_token_online',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_client_offline'),
        value: 'monitor.nat-NatServer-nat_client_offline',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_client_online'),
        value: 'monitor.nat-NatServer-nat_client_online',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_client_reg'),
        value: 'monitor.nat-NatServer-nat_client_reg',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_big_heat'),
        value: 'monitor.nat-NatServer-nat_dev_big_heat',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_offline'),
        value: 'monitor.nat-NatServer-nat_dev_offline',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_small_heat'),
        value: 'monitor.nat-NatServer-nat_dev_small_heat',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_update_ip'),
        value: 'monitor.nat-NatServer-nat_dev_update_ip',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
        value: 'monitor.nat-NatServer-nat_dev_big_heat_v6',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
        value: 'monitor.nat-NatServer-nat_dev_small_heat_v6',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
        value: 'monitor.nat-NatServer-nat_dev_update_ip_v6',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_online_dec'),
        value: 'monitor.nat-NatServer-nat_dev_online_dec',
      },
    ],
  },
  {
    label: 'RedirectServer',
    value: 'monitor.udt',
    children: [
      {
        label: Vue.prototype.$translate('redirectTab.redirect_dev'),
        value: 'monitor.udt-RedirectServer-redirect_dev',
      },
      {
        label: Vue.prototype.$translate('redirectTab.redirect_client'),
        value: 'monitor.udt-RedirectServer-redirect_client',
      },
      {
        label: Vue.prototype.$translate(
          'redirectTab.redirect_client_full_list'
        ),
        value: 'monitor.udt-RedirectServer-redirect_client_full_list',
      },
      {
        label: Vue.prototype.$translate('redirectTab.redirect_dev_inc'),
        value: 'monitor.udt-RedirectServer-redirect_dev_inc',
      },
    ],
  },
  {
    label: 'RelayServer',
    value: 'monitor.relay',
    children: [
      {
        label: Vue.prototype.$translate('relayTab.relay_dev_req'),
        value: 'monitor.relay-RelayServer-relay_dev_req',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_session'),
        value: 'monitor.relay-RelayServer-relay_session',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_client_req'),
        value: 'monitor.relay-RelayServer-relay_client_req',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_session_end'),
        value: 'monitor.relay-RelayServer-relay_session_end',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_speed_limit_count'),
        value: 'monitor.relay-RelayServer-relay_speed_limit_count',
      },
    ],
  },
  {
    label: 'StunServer',
    value: 'monitor.stun',
    children: [
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_online'),
        value: 'monitor.stun-StunServer-nat_dev_online',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_reg'),
        value: 'monitor.stun-StunServer-nat_dev_reg',
      },
      {
        label:
          'Stun:' +
          Vue.prototype.$translate('natTab.nat_cleint_p2p_req_sn_online'),
        value: 'monitor.stun-StunServer-nat_cleint_p2p_req_sn_online',
      },
      {
        label:
          'Stun:' +
          Vue.prototype.$translate('natTab.nat_cleint_p2p_req_token_online'),
        value: 'monitor.stun-StunServer-nat_cleint_p2p_req_token_online',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_client_offline'),
        value: 'monitor.stun-StunServer-nat_client_offline',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_client_online'),
        value: 'monitor.stun-StunServer-nat_client_online',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_client_reg'),
        value: 'monitor.stun-StunServer-nat_client_reg',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_big_heat'),
        value: 'monitor.stun-StunServer-nat_dev_big_heat',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_offline'),
        value: 'monitor.stun-StunServer-nat_dev_offline',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_small_heat'),
        value: 'monitor.stun-StunServer-nat_dev_small_heat',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_update_ip'),
        value: 'monitor.stun-StunServer-nat_dev_update_ip',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
        value: 'monitor.stun-StunServer-nat_dev_big_heat_v6',
      },
      {
        label:
          'Stun:' + Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
        value: 'monitor.stun-StunServer-nat_dev_small_heat_v6',
      },
      {
        label:
          'Stun:' + Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
        value: 'monitor.stun-StunServer-nat_dev_update_ip_v6',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_online_dec'),
        value: 'monitor.stun-StunServer-nat_dev_online_dec',
      },
    ],
  },
  {
    label: 'AccessServer',
    value: 'monitor.access',
    children: [
      {
        label: 'Access:' + Vue.prototype.$translate('natTab.nat_dev_online'),
        value: 'monitor.access-AccessServer-nat_dev_online',
      },
      {
        label: 'Access:' + Vue.prototype.$translate('natTab.nat_dev_reg'),
        value: 'monitor.access-AccessServer-nat_dev_reg',
      },
      {
        label: 'Access:' + Vue.prototype.$translate('natTab.nat_dev_big_heat'),
        value: 'monitor.access-AccessServer-nat_dev_big_heat',
      },
      {
        label: 'Access:' + Vue.prototype.$translate('natTab.nat_dev_offline'),
        value: 'monitor.access-AccessServer-nat_dev_offline',
      },
      {
        label:
          'Access:' + Vue.prototype.$translate('natTab.nat_dev_small_heat'),
        value: 'monitor.access-AccessServer-nat_dev_small_heat',
      },
      {
        label: 'Access:' + Vue.prototype.$translate('natTab.nat_dev_update_ip'),
        value: 'monitor.access-AccessServer-nat_dev_update_ip',
      },
      {
        label:
          'Access:' + Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
        value: 'monitor.access-AccessServer-nat_dev_big_heat_v6',
      },
      {
        label:
          'Access:' + Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
        value: 'monitor.access-AccessServer-nat_dev_small_heat_v6',
      },
      {
        label:
          'Access:' + Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
        value: 'monitor.access-AccessServer-nat_dev_update_ip_v6',
      },
      {
        label:
          'Access:' + Vue.prototype.$translate('natTab.nat_dev_online_dec'),
        value: 'monitor.access-AccessServer-nat_dev_online_dec',
      },
    ],
  },
  {
    label: 'StunRelayServer',
    value: 'monitor.stunRelay',
    children: [
      {
        label: Vue.prototype.$translate('relayTab.relay_dev_req'),
        value: 'monitor.stunRelay-StunRelayServer-relay_dev_req',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_session'),
        value: 'monitor.stunRelay-StunRelayServer-relay_session',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_client_req'),
        value: 'monitor.stunRelay-StunRelayServer-relay_client_req',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_session_end'),
        value: 'monitor.stunRelay-StunRelayServer-relay_session_end',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_speed_limit_count'),
        value: 'monitor.stunRelay-StunRelayServer-relay_speed_limit_count',
      },
    ],
  },
  {
    label: 'NatCluster',
    value: 'monitor.nat.cluster',
    children: [
      {
        label: Vue.prototype.$translate('natTab.nat_dev_online'),
        value: 'monitor.nat.cluster-NatCluster-nat_dev_online',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_reg'),
        value: 'monitor.nat.cluster-NatCluster-nat_dev_reg',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_sn_online'),
        value: 'monitor.nat.cluster-NatCluster-nat_cleint_p2p_req_sn_online',
      },
      {
        label: Vue.prototype.$translate(
          'natTab.nat_cleint_p2p_req_token_online'
        ),
        value: 'monitor.nat.cluster-NatCluster-nat_cleint_p2p_req_token_online',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_client_offline'),
        value: 'monitor.nat.cluster-NatCluster-nat_client_offline',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_client_online'),
        value: 'monitor.nat.cluster-NatCluster-nat_client_online',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_client_reg'),
        value: 'monitor.nat.cluster-NatCluster-nat_client_reg',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_big_heat'),
        value: 'monitor.nat.cluster-NatCluster-nat_dev_big_heat',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_offline'),
        value: 'monitor.nat.cluster-NatCluster-nat_dev_offline',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_small_heat'),
        value: 'monitor.nat.cluster-NatCluster-nat_dev_small_heat',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_update_ip'),
        value: 'monitor.nat.cluster-NatCluster-nat_dev_update_ip',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
        value: 'monitor.nat.cluster-NatCluster-nat_dev_big_heat_v6',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
        value: 'monitor.nat.cluster-NatCluster-nat_dev_small_heat_v6',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
        value: 'monitor.nat.cluster-NatCluster-nat_dev_update_ip_v6',
      },
      {
        label: Vue.prototype.$translate('natTab.nat_dev_online_dec'),
        value: 'monitor.nat.cluster-NatServer-nat_dev_online_dec',
      },
    ],
  },
  {
    label: 'RelayCluster',
    value: 'monitor.relay.cluster',
    children: [
      {
        label: Vue.prototype.$translate('relayTab.relay_dev_req'),
        value: 'monitor.relay.cluster-RelayCluster-relay_dev_req',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_session'),
        value: 'monitor.relay.cluster-RelayCluster-relay_session',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_client_req'),
        value: 'monitor.relay.cluster-RelayCluster-relay_client_req',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_session_end'),
        value: 'monitor.relay.cluster-RelayCluster-relay_session_end',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_speed_limit_count'),
        value: 'monitor.relay.cluster-RelayCluster-relay_speed_limit_count',
      },
    ],
  },
  {
    label: 'StunCluster',
    value: 'monitor.stun.cluster',
    children: [
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_online'),
        value: 'monitor.stun.cluster-StunCluster-nat_dev_online',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_reg'),
        value: 'monitor.stun.cluster-StunCluster-nat_dev_reg',
      },
      {
        label:
          'Stun:' +
          Vue.prototype.$translate('natTab.nat_cleint_p2p_req_sn_online'),
        value: 'monitor.stun.cluster-StunCluster-nat_cleint_p2p_req_sn_online',
      },
      {
        label:
          'Stun:' +
          Vue.prototype.$translate('natTab.nat_cleint_p2p_req_token_online'),
        value:
          'monitor.stun.cluster-StunCluster-nat_cleint_p2p_req_token_online',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_client_offline'),
        value: 'monitor.stun.cluster-StunCluster-nat_client_offline',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_client_online'),
        value: 'monitor.stun.cluster-StunCluster-nat_client_online',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_client_reg'),
        value: 'monitor.stun.cluster-StunCluster-nat_client_reg',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_big_heat'),
        value: 'monitor.stun.cluster-StunCluster-nat_dev_big_heat',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_offline'),
        value: 'monitor.stun.cluster-StunCluster-nat_dev_offline',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_small_heat'),
        value: 'monitor.stun.cluster-StunCluster-nat_dev_small_heat',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_update_ip'),
        value: 'monitor.stun.cluster-StunCluster-nat_dev_update_ip',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
        value: 'monitor.stun.cluster-StunCluster-nat_dev_big_heat_v6',
      },
      {
        label:
          'Stun:' + Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
        value: 'monitor.stun.cluster-StunCluster-nat_dev_small_heat_v6',
      },
      {
        label:
          'Stun:' + Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
        value: 'monitor.stun.cluster-StunCluster-nat_dev_update_ip_v6',
      },
      {
        label: 'Stun:' + Vue.prototype.$translate('natTab.nat_dev_online_dec'),
        value: 'monitor.stun.cluster-StunCluster-nat_dev_online_dec',
      },
    ],
  },
  {
    label: 'StunRelayCluster',
    value: 'monitor.stun.relay.cluster',
    children: [
      {
        label: Vue.prototype.$translate('relayTab.relay_dev_req'),
        value: 'monitor.stun.relay.cluster-StunRelayCluster-relay_dev_req',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_session'),
        value: 'monitor.stun.relay.cluster-StunRelayCluster-relay_session',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_client_req'),
        value: 'monitor.stun.relay.cluster-StunRelayCluster-relay_client_req',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_session_end'),
        value: 'monitor.stun.relay.cluster-StunRelayCluster-relay_session_end',
      },
      {
        label: Vue.prototype.$translate('relayTab.relay_speed_limit_count'),
        value:
          'monitor.stun.relay.cluster-StunRelayCluster-relay_speed_limit_count',
      },
    ],
  },
  {
    label: Vue.prototype.$translate('hostMonitor'),
    value: 'monitor.host',
    children: [
      {
        label: Vue.prototype.$translate('resourceMonitor.hostCpuUseRate'),
        value: 'monitor.host-CPU-cpuRate',
      },
      {
        label: Vue.prototype.$translate('resourceMonitor.averageLoad'),
        value: 'monitor.host-CPU-loadOne,loadFive,loadFifteen',
      }, // loadOne loadFive loadFifteen合并
      {
        label: Vue.prototype.$translate('hostAvailMemory'),
        value: 'monitor.host-memory-availMemory',
      },
      {
        label: Vue.prototype.$translate('hostMemoryRate'),
        value: 'monitor.host-memory-memoryRate',
      },
      {
        label: Vue.prototype.$translate('hostAvailDisk'),
        value: 'monitor.host.disk-disk-availDisk',
      },
      {
        label: Vue.prototype.$translate('hostRecvmb'),
        value: 'monitor.host-hostTraffic-recvmb',
      },
      {
        label: Vue.prototype.$translate('hostSentmb'),
        value: 'monitor.host-hostTraffic-sentmb',
      },
      {
        label: Vue.prototype.$translate('hostIoRead'),
        value: 'monitor.host-CPU-ioRead',
      },
      {
        label: Vue.prototype.$translate('hostIoWrite'),
        value: 'monitor.host-CPU-ioWrite',
      },
      {
        label: Vue.prototype.$translate('hostBandwidthRate'),
        value: 'monitor.host-hostTraffic-bandwidthRate',
      },
      {
        label: 'hi(%)',
        value: 'monitor.host-CPU-hi',
      },
      {
        label: 'si(%)',
        value: 'monitor.host-CPU-si',
      },
      {
        label: 'st(%)',
        value: 'monitor.host-CPU-steal',
      },
      {
        label: Vue.prototype.$translate('processCpuRate'),
        value: 'monitor.host.process-process-processCpuRate',
      },
      {
        label: Vue.prototype.$translate('processMemory'),
        value: 'monitor.host.process-process-memory',
      },
      {
        label: Vue.prototype.$translate('processMemoryRate'),
        value: 'monitor.host.process-process-processMemoryRate',
      }, // 不展示
    ],
  },
  {
    label: Vue.prototype.$translate('httpsDomain'),
    value: 'monitor.domain',
    children: [
      {
        label: Vue.prototype.$translate('domainRemainDays'),
        value: 'monitor.domain-domain-remainDays',
      },
    ],
  },
  {
    label: Vue.prototype.$translate('certificate'),
    value: 'monitor.cert',
    children: [
      {
        label: Vue.prototype.$translate('certRemainDays'),
        value: 'monitor.cert-cert-remainDays',
      },
    ],
  },
  {
    label: Vue.prototype.$translate('javaMicroService'),
    value: 'monitor.microservice.java',
    children: [
      {
        label: 'Java:' + Vue.prototype.$translate('microcpuRate'),
        value: 'monitor.microservice.java-javaMicroService-cpuRate',
      },
      {
        label: 'Java:' + Vue.prototype.$translate('microMemoryRate'),
        value: 'monitor.microservice.java-javaMicroService-memoryRate',
      },
    ],
  },
  {
    label: Vue.prototype.$translate('cplusMicroService'),
    value: 'monitor.microservice.cplus',
    children: [
      {
        label: 'C++:' + Vue.prototype.$translate('microcpuRate'),
        value: 'monitor.microservice.cplus-cplusMicroService-cpuRate',
      },
      {
        label: 'C++:' + Vue.prototype.$translate('microMemoryRate'),
        value: 'monitor.microservice.cplus-cplusMicroService-memoryRate',
      },
    ],
  },
  {
    label: Vue.prototype.$translate('mysqlInstance'),
    value: 'monitor.mysql.instance',
    children: [
      {
        label: Vue.prototype.$translate('mySqlTab.maxConnections'),
        value: 'monitor.mysql.instance-mysqlInstance-maxConnections',
      },
      {
        label: Vue.prototype.$translate('mySqlTab.dbConnections'),
        value: 'monitor.mysql.instance-mysqlInstance-connections',
      },
      {
        label: Vue.prototype.$translate('mySqlTab.availSpace'),
        value: 'monitor.mysql.instance-mysqlInstance-availSpace',
      },
      {
        label: Vue.prototype.$translate('mySqlTab.spaceRate'),
        value: 'monitor.mysql.instance-mysqlInstance-spaceRate',
      },
      {
        label: Vue.prototype.$translate('mySqlTab.dbSpaces'),
        value: 'monitor.mysql.instance-mysqlInstance-spaces',
      },
    ],
  },
  {
    label: Vue.prototype.$translate('mysqlDb'),
    value: 'monitor.mysql.db',
    children: [
      {
        label: Vue.prototype.$translate('mySqlTab.dbConnections'),
        value: 'monitor.mysql.db-mysqlDb-dbConnections',
      },
      {
        label: Vue.prototype.$translate('mySqlTab.dbAvailConnections'),
        value: 'monitor.mysql.db-mysqlDb-dbAvailConnections',
      },
      {
        label: Vue.prototype.$translate('mySqlTab.dbDeadlocks'),
        value: 'monitor.mysql.db-mysqlDb-dbDeadlocks',
      },
      {
        label: Vue.prototype.$translate('mySqlTab.dbSpaces'),
        value: 'monitor.mysql.db-mysqlDb-dbSpaces',
      },
      {
        label: Vue.prototype.$translate('mysqlDbSlows'),
        value: 'monitor.mysql.db-mysqlDb-dbSlows',
      },
    ],
  },
  {
    label: 'Redis',
    value: 'monitor.redis',
    children: [
      {
        label: Vue.prototype.$translate('redisMemory'),
        value: 'monitor.redis-Redis-memory',
      },
      {
        label: Vue.prototype.$translate('redisMemoryRate'),
        value: 'monitor.redis-Redis-memoryRate',
      },
      {
        label: Vue.prototype.$translate('redisTab.connections'),
        value: 'monitor.redis-Redis-connections',
      },
      {
        label: Vue.prototype.$translate('redisTab.blockedClients'),
        value: 'monitor.redis-Redis-blockedClients',
      },
      {
        label: Vue.prototype.$translate('redisTab.rejectedConnections'),
        value: 'monitor.redis-Redis-rejectedConnections',
      },
      {
        label: Vue.prototype.$translate('redisTab.clusterDowns'),
        value: 'monitor.redis-Redis-clusterDowns',
      },
    ],
  },
  {
    label: 'Kafka',
    value: 'monitor.kafka',
    children: [
      {
        label: Vue.prototype.$translate('kafkaMemoryRate'),
        value: 'monitor.kafka.node-Kafka-memoryRate',
      },
      {
        label: Vue.prototype.$translate('kafkaCpuRate'),
        value: 'monitor.kafka.node-Kafka-cpuRate',
      },
      {
        label: Vue.prototype.$translate('kafkaTab.lags'),
        value: 'monitor.kafka.cluster-Kafka-lags',
      },
      {
        label: Vue.prototype.$translate('kafkaTab.clusterDowns'),
        value: 'monitor.kafka.cluster-Kafka-clusterDowns',
      },
    ],
  },
  {
    label: 'Mongo',
    value: 'monitor.mongo',
    children: [
      {
        label: Vue.prototype.$translate('mongoSpace'),
        value: 'monitor.mongo-Mongo-spaces',
      },
      {
        label: Vue.prototype.$translate('mySqlTab.spaceRate'),
        value: 'monitor.mongo-Mongo-spaceRaste',
      },
      {
        label: Vue.prototype.$translate('mongoConnections'),
        value: 'monitor.mongo-Mongo-connections',
      },
      {
        label: Vue.prototype.$translate('mongoAvailConnections'),
        value: 'monitor.mongo-Mongo-availConnections',
      },
      {
        label: Vue.prototype.$translate('mongoMemory'),
        value: 'monitor.mongo-Mongo-memory',
      },
      {
        label: Vue.prototype.$translate('mongoTab.requests'),
        value: 'monitor.mongo-Mongo-requests',
      },
      {
        label: Vue.prototype.$translate('mongoSlows'),
        value: 'monitor.mongo-Mongo-slows',
      },
    ],
  },
  {
    label: 'Nginx',
    value: 'monitor.nginx',
    children: [
      {
        label: Vue.prototype.$translate('nginxConnections'),
        value: 'monitor.nginx-Nginx-connections',
      },
      {
        label: Vue.prototype.$translate('nginxAvailConnections'),
        value: 'monitor.nginx-Nginx-availConnections',
      },
      {
        label: Vue.prototype.$translate('nginxIncrRequests'),
        value: 'monitor.nginx-Nginx-incrRequests',
      },
    ],
  },
  {
    label: 'OSS/S3',
    value: 'monitor.oss',
    children: [
      {
        label: Vue.prototype.$translate('storages'),
        value: 'monitor.oss-OSS/S3-storages',
      },
      {
        label: Vue.prototype.$translate('ossTab.objects'),
        value: 'monitor.oss-OSS/S3-objects',
      },
    ],
  },
  {
    label: 'Queue',
    value: 'monitor.queue',
    children: [
      {
        label: Vue.prototype.$translate('queueTab.usedRate'),
        value: 'monitor.queue-Queue-usedRate',
      },
    ],
  },
  {
    label: 'Apache',
    value: 'monitor.apache',
    children: [
      {
        label: Vue.prototype.$translate('apacheTab.busyWorkers'),
        value: 'monitor.apache-Apache-busyWorkers',
      },
      {
        label: Vue.prototype.$translate('apacheTab.idleWorkers'),
        value: 'monitor.apache-Apache-idleWorkers',
      },
      {
        label: Vue.prototype.$translate('apacheTab.incrRequests'),
        value: 'monitor.apache-Apache-incrRequests',
      },
    ],
  },
  {
    label: 'WsConnect',
    value: 'monitor.wsConnect',
    children: [
      {
        label: Vue.prototype.$translate('wsConnectTab.totalConnectNum'),
        value: 'monitor.wsConnect-WsConnect-totalConnectNum',
      },
      {
        label: Vue.prototype.$translate('wsConnectTab.connectNum'),
        value: 'monitor.wsConnect-WsConnect-connectNum',
      },
    ],
  },
  {
    label: Vue.prototype.$translate('email'),
    value: 'monitor.email',
    children: [
      {
        label: Vue.prototype.$translate('emailNums'),
        value: 'monitor.email-email-nums',
      },
      {
        label: Vue.prototype.$translate('emailErrors'),
        value: 'monitor.email-email-errors',
      },
      {
        label: Vue.prototype.$translate('emailLimits'),
        value: 'monitor.email-email-limits',
      },
    ],
  },
  {
    label: Vue.prototype.$translate('textMessage'),
    value: 'monitor.sms',
    children: [
      {
        label: Vue.prototype.$translate('smsNums'),
        value: 'monitor.sms-textMessage-nums',
      },
      {
        label: Vue.prototype.$translate('smsErrors'),
        value: 'monitor.sms-textMessage-errors',
      },
      {
        label: Vue.prototype.$translate('smsLimits'),
        value: 'monitor.sms-textMessage-limits',
      },
    ],
  },
  {
    label: Vue.prototype.$translate('account'),
    value: 'monitor.account',
    children: [
      {
        label: Vue.prototype.$translate('accountTab.logins'),
        value: 'monitor.account-account-logins',
      },
      {
        label: Vue.prototype.$translate('accountTab.registers'),
        value: 'monitor.account-account-registers',
      },
    ],
  },
  {
    label: 'Websocket',
    value: 'monitor.websocket',
    children: [
      {
        label: Vue.prototype.$translate('websocketTab.disconnects'),
        value: 'monitor.websocket-Websocket-disconnects',
      },
    ],
  },
  {
    label: Vue.prototype.$translate('push2'),
    value: 'monitor.push',
    children: [
      {
        label: Vue.prototype.$translate('pushTab.pushs'),
        value: 'monitor.push-push2-pushs',
      },
      {
        label: Vue.prototype.$translate('pushTab.onlinePushs'),
        value: 'monitor.push-push2-onlinePushs',
      },
      {
        label: Vue.prototype.$translate('pushTab.offlinePushs'),
        value: 'monitor.push-push2-offlinePushs',
      },
    ],
  },
]

// 柱状图中展示的name
export const barMetricsNameObj = {
  'monitor.host.disk-disk-availDisk': Vue.prototype.$translate(
    'resourceTab.availDisk'
  ), // '剩余空间(M)'
  'monitor.host.disk-disk-diskRate': Vue.prototype.$translate(
    'resourceTab.diskRate'
  ), // '空间使用率(%)'
  'monitor.cert-cert-remainDays': Vue.prototype.$translate(
    'domainCertTab.remainDays'
  ), // '证书有效剩余时间(天)'
  'monitor.domain-domain-remainDays': Vue.prototype.$translate(
    'domainCertTab.remainDays'
  ), // '域名有效剩余时间(天)'
  'monitor.oss-OSS/S3-storages': Vue.prototype.$translate('ossTab.storages'), // '桶已使用存储容量(M)'
  'monitor.oss-OSS/S3-objects': Vue.prototype.$translate('ossTab.objects'), // '桶已使用存储对象数'
}

// 指标下拉树中各个指标大类对应到的dcResCodeObj中的指标类型
export const metricsTypeObj = {
  'monitor.nat': 'nat',
  'monitor.udt': 'udt',
  'monitor.relay': 'relay',
  'monitor.stun': 'stun',
  'monitor.access': 'access',
  'monitor.stunRelay': 'stunRelay',
  'monitor.nat.cluster': 'nat_cluster',
  'monitor.relay.cluster': 'relay_cluster',
  'monitor.stun.cluster': 'stun_cluster',
  'monitor.stun.relay.cluster': 'stunrelay_cluster',
  'monitor.host': 'host_hostInfo',
  'monitor.host.disk': 'host_disk',
  'monitor.host.process': 'host_process',
  'monitor.domain': 'domain',
  'monitor.cert': 'cert',
  'monitor.microservice': 'microservice',
  'monitor.microservice.java': 'microservice',
  'monitor.microservice.cplus': 'microservice',
  'monitor.microservice.web': 'microservice',
  'monitor.mysql.instance': 'mysqlInstance',
  'monitor.mysql.db': 'mysqlDb',
  'monitor.redis': 'redis',
  'monitor.kafka.node': 'kafkaNode',
  'monitor.kafka.cluster': 'kafkaCluster',
  'monitor.mongo': 'mongo',
  'monitor.nginx': 'nginx',
  'monitor.oss': 'oss',
  'monitor.queue': 'queue',
  'monitor.apache': 'apache',
  'monitor.wsConnect': 'wsConnect',
  'monitor.email': 'email',
  'monitor.sms': 'sms',
  'monitor.account': 'account',
  'monitor.websocket': 'websocket',
  'monitor.dpikey': 'dpikey',
  'monitor.push': 'push',
}

// 指标下拉树中各个指标对应到的dcResCodeObj中的页面
export const metricsPageObj = {
  'monitor.nat': 'devops_monitor_p2p',
  'monitor.udt': 'devops_monitor_p2p',
  'monitor.relay': 'devops_monitor_p2p',
  'monitor.stun': 'devops_monitor_p2p',
  'monitor.access': 'devops_monitor_p2p',
  'monitor.stunRelay': 'devops_monitor_p2p',
  'monitor.nat.cluster': 'devops_monitor_p2p',
  'monitor.relay.cluster': 'devops_monitor_p2p',
  'monitor.stun.cluster': 'devops_monitor_p2p',
  'monitor.stun.relay.cluster': 'devops_monitor_p2p',
  'monitor.host': 'devops_monitor_resource',
  'monitor.host.disk': 'devops_monitor_resource',
  'monitor.host.process': 'devops_monitor_resource',
  'monitor.domain': 'devops_monitor_resource',
  'monitor.cert': 'devops_monitor_resource',
  'monitor.microservice': 'devops_monitor_application',
  'monitor.microservice.java': 'devops_monitor_application',
  'monitor.microservice.cplus': 'devops_monitor_application',
  'monitor.microservice.web': 'devops_monitor_application',
  'monitor.mysql.instance': 'devops_monitor_application',
  'monitor.mysql.db': 'devops_monitor_application',
  'monitor.redis': 'devops_monitor_application',
  'monitor.kafka.node': 'devops_monitor_application',
  'monitor.kafka.cluster': 'devops_monitor_application',
  'monitor.mongo': 'devops_monitor_application',
  'monitor.nginx': 'devops_monitor_application',
  'monitor.oss': 'devops_monitor_application',
  'monitor.queue': 'devops_monitor_application',
  'monitor.apache': 'devops_monitor_application',
  'monitor.wsConnect': 'devops_monitor_application',
  'monitor.email': 'devops_monitor_business',
  'monitor.sms': 'devops_monitor_business',
  'monitor.account': 'devops_monitor_business',
  'monitor.websocket': 'devops_monitor_business',
  'monitor.dpikey': 'devops_monitor_business',
  'monitor.push': 'devops_monitor_business',
}
