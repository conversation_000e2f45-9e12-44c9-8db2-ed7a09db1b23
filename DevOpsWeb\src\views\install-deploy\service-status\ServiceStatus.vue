<template>
  <!-- 部署环境资源规划 -->
  <div class="service-status">
    <div class="search-wrapper">
      <div class="search-left-wrapper">
        <search-input
          v-model="filterCond.projectName"
          :placeholder="$t('projectNameInput')"
          clearable
          maxlength="100"
          style="width: 250px; margin-right: 20px"
          @change="getServiceStatusTableDatas(1)"
        />
        <tvt-select
          v-model="filterCond.serviceStatus"
          :options="serviceStatusOptions"
          :placeholder="$t('serviceStatusTab.serviceStatus')"
          clearable
          @change="getServiceStatusTableDatas(1)"
        />
        <div class="btn-group">
          <el-button
            type="primary"
            size="small"
            round
            @click="queryServiceStatus"
          >
            {{ $t('search') }}
          </el-button>
          <el-button
            v-if="$isAuthorityExist()"
            type="primary"
            size="small"
            round
            @click="openLink('eureka')"
          >
            {{ $t('serviceStatusTab.openEureka') }}
          </el-button>
          <el-button
            v-if="$isAuthorityExist()"
            type="primary"
            size="small"
            round
            @click="openLink('springBootAdmin')"
          >
            {{ $t('serviceStatusTab.openSpringBootAdmin') }}
          </el-button>
        </div>
      </div>
    </div>
    <div class="table-wrapper">
      <tvt-table
        v-myLoading="loading"
        :data="serviceStatusTableDatas"
        :columns="serviceStatusColumns"
        :border="true"
        :border-bottom="true"
        :pagination="{
          total,
          current: filterCond.current,
          size: filterCond.size,
          'page-sizes': $tablePageSizes,
          background: true,
        }"
        @onFetchData="getServiceStatusTableDatas"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.key === 'operation'">
            <span class="btn-text" @click="serviceOperateOne(row, 'restart')">{{
              $t('serviceStatusTab.restartService')
            }}</span>
            <span class="btn-text" @click="serviceOperateOne(row, 'stop')">{{
              $t('serviceStatusTab.stopService')
            }}</span>
            <span
              class="btn-text"
              @click="serviceOperateOne(row, 'uninstall')"
              >{{ $t('serviceStatusTab.uninstallService') }}</span
            >
          </template>
        </template>
      </tvt-table>
    </div>
  </div>
</template>

<script>
import { serviceStatusList, serviceStatusOperate } from '@/api/installDeploy.js'
import { addObjKey, debounce } from '@/utils/common'
import { mapState } from 'vuex'
export default {
  name: 'ServiceStatus',
  data() {
    return {
      serviceStatusOptions: [
        { label: 'UP', value: 'UP' },
        { label: 'DOWN', value: 'DOWN' },
      ],
      serviceStatusTableDatas: [],
      loading: false,
      serviceStatusColumns: [
        { label: this.$t('index'), type: 'index', width: 60 },
        {
          label: this.$t('configTab.projectName'),
          prop: 'projectName',
          'min-width': 240,
        },
        {
          label: this.$t('configTab.resourceIp'),
          prop: 'resourceIp',
          width: 150,
        },
        {
          label: this.$t('configTab.projectPort'),
          prop: 'projectPort',
          width: 120,
        },
        {
          label: this.$t('serviceStatusTab.serviceInstanceId'),
          prop: 'serviceInstanceId',
          width: 120,
        },
        { label: this.$t('configTab.startXms'), prop: 'startXms', width: 180 },
        { label: this.$t('configTab.startXmx'), prop: 'startXmx', width: 180 },
        {
          label: this.$t('serviceStatusTab.serviceStatus'),
          prop: 'serviceStatus',
          width: 120,
        },
        {
          label: this.$t('configTab.operation'),
          key: 'operation',
          slotName: 'bodyCell',
          'min-width': 200,
          hide: !this.$isAuthorityExist(),
        },
      ],
      filterCond: {
        current: 1,
        size: 20,
        projectName: '',
        serviceStatus: '',
      },
      total: 0,
    }
  },
  computed: {
    ...mapState('params', ['environmentCode', 'dcName']),
  },
  mounted() {
    this.getServiceStatusTableDatas(1)
  },
  methods: {
    queryServiceStatus() {
      this.getServiceStatusTableDatas(1)
    },
    // 1.4分页获取资源规划列表
    getServiceStatusTableDatas: debounce(function (pageSize) {
      this.loading = true
      if (pageSize === 1) {
        this.filterCond.current = 1
      } else if (pageSize && pageSize.current) {
        this.filterCond.current = pageSize.current
        this.filterCond.size = pageSize.size
      }
      let data = addObjKey(this.filterCond)
      // 查询过滤参数
      let { environmentCode, dcName } = this
      data.environmentCode = environmentCode
      data.dcName = dcName ? dcName : undefined
      data.pageNum = this.filterCond.current
      data.pageSize = this.filterCond.size
      delete data['current']
      delete data['size']
      serviceStatusList(data)
        .then((res) => {
          this.loading = false
          if (res.basic && res.basic.code === 200) {
            if (res.data) {
              this.serviceStatusTableDatas = res.data.records
              this.total = parseInt(res.data.total)
            } else {
              this.serviceStatusTableDatas = []
            }
          } else {
            this.$message.error(res.basic.msg)
          }
        })
        .catch((err) => {
          this.loading = false
          console.log(err)
        })
    }, 500),
    serviceOperateOne(row, opType) {
      this.executeServiceOperate([row], opType)
    },
    executeServiceOperate(rows, opType) {
      if (!rows.length) {
        this.$message.error(this.$t('versionTab.needChooseList'))
        return false
      }
      let data = []
      for (var i = 0; i < rows.length; i++) {
        data.push({
          environmentName: rows[i].environmentName,
          projectName: rows[i].projectName,
          resourceIp: rows[i].resourceIp,
          projectPort: rows[i].projectPort,
          serviceInstanceId: rows[i].serviceInstanceId,
          dcName: rows[i].dcName,
          operate: opType,
        })
      }
      this.$TvtLoading.show({ text: this.$t('LoadingMsg') })
      serviceStatusOperate(data)
        .then((res) => {
          this.$TvtLoading.hide()

          if (res.basic && res.basic.code === 200) {
            this.$message({
              dangerouslyUseHTMLString: true,
              message: res.data.join(' <br/> '),
              type: 'success',
            })
            this.getServiceStatusTableDatas(this.filterCond.current)
          } else {
            this.$message.error(res.basic.msg)
          }
        })
        .catch((err) => {
          this.$TvtLoading.hide()
          console.log(err)
        })
    },
    openLink(type) {
      if (type == 'eureka') {
        //打开eureka
        var eurekaBasicService = this.serviceStatusTableDatas.find((item) => {
          return item.projectName == 'EurekaBasicService'
        })
        if (eurekaBasicService) {
          window.open(
            `http://${eurekaBasicService.resourceIp}:${eurekaBasicService.projectPort}`
          )
        } else {
          this.$message.error(this.$t('serviceStatusTab.openEurekaTip'))
        }
      }

      if (type == 'springBootAdmin') {
        //打开springBootAdmin
        var backgroundManageApplyService = this.serviceStatusTableDatas.find(
          (item) => {
            return item.projectName == 'BackgroundManageApplyService'
          }
        )
        if (backgroundManageApplyService) {
          window.open(
            `http://${backgroundManageApplyService.resourceIp}:${backgroundManageApplyService.projectPort}/admin`
          )
        } else {
          this.$message.error(
            this.$t('serviceStatusTab.openSpringBootAdminTip')
          )
        }
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.service-status {
  margin: 24px;
  width: calc(100% - 48px);
  position: relative;

  .search-wrapper {
    width: 100%;
    margin: 20px 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    .search-left-wrapper {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
    }
    .search-right-wrapper {
      width: max-content;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
    .btn-group button {
      margin-left: 20px;
    }
  }
  .table-wrapper {
  }

  .btn-text {
    color: #429efd !important;
    cursor: pointer;

    + .btn-text {
      margin-left: 20px;
    }
  }
}

.active-item {
  color: #429efd;
}
</style>

<style>
.service-status .el-input__inner {
  border: 1px solid #dcdfe6;
  border-radius: 16px;
}

.service-status .search-wrapper .tvt-input .tvt-field-set {
  border: none;
}
.service-status .search-wrapper .tvt-select .tvt-field-set {
  border: none;
}
</style>
