const webpackConfig = require('tvtclouddevdependency')
const { alias, cssLoader } = require('./utils.js')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')

const devWebpackConfig = {
  output: {
    publicPath: process.env.NODE_ENV === 'development' ? '/' : '/devops/',
    // 输出重构  打包编译后的 文件名称  【模块名称.版本号.js】
    filename: 'js/[name].[contenthash].js',
    chunkFilename: 'js/[name].[contenthash].chunk.js',
  },
  resolve: {
    alias,
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: `css/[name].[contenthash].css`,
      chunkFilename: 'css/[name].[contenthash].chunk.css',
    }),
  ],
  module: {
    rules: [cssLoader],
  },
  // these devServer options should be customized in /config/index.js
  devServer: {
    allowedHosts: ['zh7g22.natappfree.cc'],
    historyApiFallback: {
      rewrites: [{ from: /.*/, to: 'index.html' }],
    },
    host: '0.0.0.0',
    port: 8045,
    proxy: {
      '/dev-ops': {
        // target:'http://***********:8082',//袁欣
        // target:'http://************:8082',//杨炼
        // target: 'http://***********:7200', //王馨
        // target: 'http://************:7200', // 罗坤
        // target:'http://***********:1234',//正式开发
        // target: 'http://***********:7200', //临时开发
        // target: 'http://***********:7200', // 联调环境ddc
        target: 'http://***********:7200', // 联调环境rdc
        // target: 'http://************:7200/', // 武汉p2p1DC
        // target: 'http://*************:7200', // 测试环境
        // target: 'https://glbsit-ops.starvisioncloud.com/', // 集成测试环境
        changeOrigin: true,
        pathRewrite: {
          '^/dev-ops': '/dev-ops',
        },
        secure: false,
      },
    },
    static: {
      publicPath: '/static',
    },
  },
}

webpackConfig.mergeDev(devWebpackConfig)

module.exports = webpackConfig.dev
