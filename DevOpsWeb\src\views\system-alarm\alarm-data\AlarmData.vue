<template>
  <!-- 告警实时数据 -->
  <div class="alarm-data">
    <div class="table-wrapper">
      <tvt-table
        ref="myTable"
        v-myLoading="loading"
        :data="tableData"
        :columns="alarmDataColumns"
        :border="true"
        :border-bottom="true"
        :pagination="false"
        @onFetchData="() => {}"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.prop === 'monitorType'">
            <span>{{ dealMonitorType(row.monitorType) }}</span>
          </template>
          <template v-if="column.prop === 'startTime'">
            <span>{{ stampToStrLongMethod(row.startTime) }}</span>
          </template>
          <template v-if="column.prop === 'duration'">
            <span>{{ durationToStrLongMethod(row.duration) }}</span>
          </template>
        </template>
      </tvt-table>
    </div>
  </div>
</template>
<script>
import { getLocale } from '@/lang'
import { debounce, stampToStrLong, formatDuring } from '@/utils/common'
import {
  ctyTypeOptions,
  alarmDataColumns,
  ctyTypeClassify,
} from '@/views/system-alarm/config.js'
import { getAlarmDataList } from '@/api/systemAlarm'

const lang = getLocale() || 'zh-CN'

export default {
  name: 'AlarmHistory',
  components: {},
  data() {
    return {
      loading: false,
      alarmDataList: [],
      alarmDataColumns,
      monitorTypeName: ctyTypeOptions.reduce((pre, next) => {
        pre[next.value] = next.label
        return pre
      }, {}),
      monitorObj: {
        resource: this.$t('nav.resLayerMonitor'),
        application: this.$t('nav.appLayerMonitor'),
        business: this.$t('nav.businessLayerMonitor'),
        p2p: this.$t('nav.p2p'),
      },
      tableData: [],
      timer: null, // 定时器
      isShowTooltip: false, // 是否显示提示文字
    }
  },
  async created() {
    this.getAlarmData()
    // 30s轮询一次
    this.timer = setInterval(() => {
      this.getAlarmData()
    }, 30000)
  },
  mounted() {},
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    getAlarmData: debounce(async function () {
      this.loading = true
      return getAlarmDataList({ lang })
        .then((res) => {
          this.loading = false
          if (res.basic && res.basic.code == 200) {
            if (res.data) {
              this.tableData = res.data.map((item) => {
                const ctyClassify = ctyTypeClassify[item.monitorType]
                const systemMonitortType = this.monitorObj[ctyClassify]
                item.systemMonitortType = systemMonitortType
                return item
              })
            }
          }
        })
        .catch((err) => {
          this.loading = false
          console.log(err)
        })
    }, 500),
    stampToStrLongMethod(time) {
      let intTime = parseInt(time) //接口给的字符串时间
      return stampToStrLong(intTime)
    },
    dealMonitorType(monitorType) {
      return this.monitorTypeName[monitorType] || ''
    },
    durationToStrLongMethod(time) {
      let intTime = parseInt(time) //接口给的字符串时间
      return formatDuring(intTime)
    },
  },
}
</script>
<style lang="scss" scoped>
.alarm-data {
  margin: 24px;
  width: calc(100% - 48px);
  .table-list-wrapper {
    width: 100%;
    height: 100%;
  }
  .table-wrapper {
    width: 100%;
  }
  .btn-text {
    color: #429efd !important;
    cursor: pointer;

    + .btn-text {
      margin-left: 20px;
    }
  }

  .pre-detail {
    line-height: 24px;
    white-space: pre-line;
  }

  .no-detail {
    width: 600px;
    text-align: center;
  }
}
</style>
<style>
.alarm-data .table-wrapper .el-table__body .cell {
  white-space: pre-line;
}
.alarm-data .search-wrapper .el-date-editor .el-range-separator {
  display: inline-flex;
  align-items: center;
}
.alarm-data .search-wrapper .el-date-editor .el-range__icon {
  line-height: 28px;
}

.alarm-data .search-wrapper .el-date-editor .el-range__close-icon {
  line-height: 28px;
}

.alarm-data .tooltip-ellipsis-box {
  display: block;
  width: 100%;
}

.alarm-data .text-over-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}
</style>
