<template>
  <!-- Kafka日志 -->
  <div class="page-wrapper">
    <div class="search-wrapper">
      <tvt-select
        v-model="filterCond.topic"
        :options="topicOptions"
        :mockplaceholder="$t('kafkaSearchTab.topicSelect')"
        :clearable="false"
        style="width: 250px; margin-right: 20px"
        @change="handleTopic"
      />
      <tvt-select
        v-model="filterCond.hour"
        :options="hourOptions"
        :mockplaceholder="$t('kafkaSearchTab.hourSelect')"
        :clearable="false"
        style="width: 250px; margin-right: 20px"
      />
      <!-- <div class="auto-complete-box">
        <div class="search-icon">
          <svg-img viewBox="0 0 20 20" width="22" height="22"
            ><IconSearch
          /></svg-img>
        </div>
        <el-autocomplete
          v-model="filterCond.url"
          prefix-icon="el-icon-search"
          :class="['inline-input', isFocus && 'inline-input-focus']"
          :fetch-suggestions="querySearch"
          :placeholder="$t('kafkaSearchTab.urlInput')"
          :maxlength="32"
          style="width: 250px; margin-right: 20px"
          @select="handleSelect"
          @focus="isFocus = true"
          @blur="isFocus = false"
        >
        </el-autocomplete>
      </div> -->
      <!-- <search-input
        v-model="filterCond.key"
        :placeholder="$t('kafkaSearchTab.keywordInput')"
        clearable
        size="small"
        maxlength="32"
        style="width: 250px; margin-right: 20px"
      /> -->
      <multi-input
        v-model="filterCond.urlList"
        :mockplaceholder="$t('kafkaSearchTab.urlInput')"
        :input-type="2"
        class="device-mutli-input"
        clearable
        :has-limit="true"
        style="width: 320px; margin-right: 20px"
        tag-max-length="32"
        @change="handleUrlChange"
      />
      <multi-input
        v-model="filterCond.snList"
        :mockplaceholder="$t('kafkaSearchTab.keyInput')"
        :input-type="2"
        class="device-mutli-input"
        clearable
        :has-limit="true"
        style="width: 320px; margin-right: 20px"
        tag-max-length="32"
        @change="handleSnChange"
      />
      <multi-input
        v-model="filterCond.keyList"
        :mockplaceholder="$t('kafkaSearchTab.keywordInput')"
        :input-type="2"
        class="device-mutli-input"
        clearable
        :has-limit="true"
        style="width: 320px; margin-right: 20px"
        tag-max-length="32"
        @change="handleKeyChange"
      />
      <div class="search-btn-box">
        <el-button type="primary" size="small" round @click="handleAdvanced">{{
          $t('kafkaSearchTab.customSearch')
        }}</el-button>
        <el-button type="primary" size="small" round @click="handleReset">{{
          $t('reset')
        }}</el-button>
        <el-button type="primary" size="small" round @click="handleRefresh">{{
          $t('search')
        }}</el-button>
      </div>
      <div class="search-left-wrapper"></div>
    </div>
    <!-- Kafka日志表格 -->
    <div class="table-wrapper">
      <tvt-table
        ref="myTable"
        v-myLoading="loading"
        :data="kafkaLogList"
        :columns="kafkaLogColumn"
        :border="true"
        :border-bottom="true"
        :pagination="{
          total: kafkaTotal,
          current: filterCond.current,
          size: filterCond.size,
          'page-sizes': $tablePageSizes,
          background: true,
        }"
        @onFetchData="getkafkaLogList"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.prop === 'createTime'">
            <span>{{ stampToStrLongMethod(row[column.prop]) }}</span>
          </template>
          <template v-if="column.prop === 'content'">
            <el-tooltip
              class="item"
              effect="dark"
              :content="row[column.prop]"
              placement="left-start"
            >
              <span
                class="tooltip-ellipsis-box"
                @click="changeSnCopy(row[column.prop])"
                >{{ row[column.prop] }}</span
              >
            </el-tooltip>
          </template>
        </template>
      </tvt-table>
    </div>
    <!-- 高级搜索弹框 -->
    <tvt-dialog
      :title="$t('kafkaSearchTab.customSearch')"
      :show.sync="showAdvance"
      width="500px"
      :modal-append-to-body="false"
      :cancel-text="$t('cancel')"
      :submit-text="$t('confirm')"
      :close-on-click-modal="false"
      @close="closeAdvanceDialog"
      @Submit="confirmAdvanceSearch"
    >
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item prop="advanceInput">
          <tvt-input
            v-model="form.advanceInput"
            :placeholder="$t('kafkaSearchTab.customInput')"
            type="textarea"
            rows="6"
          />
        </el-form-item>
        <div>
          <div>{{ $t('kafkaSearchTab.pasteExample') }}</div>
          <div class="text-desc-box">
            {{
              `{ 
                "topic":"topic_upgrade",
                "hour":"16",
                "urlList":[],
                "snList":[],
                "keyList":[]
              }
              `
            }}
          </div>
          <div>{{ $t('kafkaSearchTab.paramsDesc') }}</div>
          <div class="text-desc-box">
            <div>{{ `topic:  topic` }}</div>
            <div>{{ `houre: ${$t('kafkaSearchTab.hour')} 0-23` }}</div>
            <div>
              {{
                `urlList: ${$t('kafkaSearchTab.protocolName')} ${$t(
                  'kafkaSearchTab.forExample'
                )} '/device/ready'`
              }}
            </div>
            <div>
              {{
                `snList: ${$t('kafkaSearchTab.ciphertextSN')} ${$t(
                  'kafkaSearchTab.forExample'
                )}'B0F0DF4701DCE814FF6F16084E31F59B'`
              }}
            </div>
            <div>{{ `keyList: ${$t('kafkaSearchTab.searchKeywords')}` }}</div>
          </div>
        </div>
      </el-form>
    </tvt-dialog>
  </div>
</template>
<script>
import { debounce, stampToStrLong } from '@/utils/common'
import { getTopicList, getKafkaLogList } from '@/api/basicTool'
// import SvgImg from './SvgImg'
// import IconSearch from './IconSearch'
import MultiInput from '@/components/common/MultiInput.vue'
export default {
  name: 'KafkaSearch',
  components: {
    // SvgImg,
    // IconSearch,
    MultiInput,
  },
  data() {
    return {
      defaultTimeRange: [],
      filterCond: {
        current: 1,
        size: 20,
        topic: '',
        hour: String(new Date().getHours()),
        key: '',
        url: '',
        urlList: [],
        snList: [],
        keyList: [],
      },
      loading: false,
      topicList: [],
      topicOptions: [],
      urlList: [], // url备选下拉
      kafkaTotal: 0,
      kafkaLogList: [],
      hourOptions: new Array(24).fill(0).map((_, index) => ({
        label: `${String(index).padStart(2, '0')}:00-${String(
          index + 1
        ).padStart(2, '0')}:00`,
        value: String(index),
      })),
      kafkaLogColumn: [
        {
          label: 'topic',
          prop: 'topic',
          width: 180,
        },
        {
          label: this.$t('kafkaSearchTab.partition'),
          prop: 'partition',
          width: 100,
        },
        {
          label: this.$t('kafkaSearchTab.offset'),
          prop: 'offset',
          width: 100,
        },
        {
          label: 'key',
          prop: 'keyString',
          width: 300,
        },
        {
          label: this.$t('kafkaSearchTab.content'),
          prop: 'content',
          slotName: 'bodyCell',
          minWidth: 300,
        },
        {
          label: this.$t('kafkaSearchTab.createTime'),
          prop: 'createTime',
          slotName: 'bodyCell',
          width: 160,
        },
      ],
      isFocus: false,
      showAdvance: false, // 高级搜索弹窗
      form: {
        advanceInput: '',
      },
      rules: {
        advanceInput: [
          {
            required: true,
            message: this.$t('kafkaSearchTab.advanceInput'),
            trigger: 'blur',
          },
        ],
      },
    }
  },
  async mounted() {
    await this.getTopicList()
    // this.getkafkaLogList()
  },
  methods: {
    stampToStrLongMethod(time) {
      let intTime = parseInt(time) //接口给的字符串时间
      return stampToStrLong(intTime)
    },
    handleTopic(val) {
      this.filterCond.topic = val
      const topicItem = this.topicList.find((item) => item.topic === val)
      console.log('topicItem', topicItem)
      if (topicItem) {
        this.urlList = topicItem.urlList
      } else {
        this.urlList = null
      }
    },
    // 获取topic列表
    async getTopicList() {
      try {
        const { data = [] } = await getTopicList({})
        this.topicList = data.slice()
        this.topicOptions = data.map((item) => ({
          label: item.topicLabel,
          value: item.topic,
        }))
        // 微服务列表默认选中第一个
        this.filterCond.topic = data[0].topic
        this.urlList = data[0].urlList
      } catch (error) {
        console.error(error)
      }
    },
    querySearch(queryString, cb) {
      const urlList = this.urlList
        ? this.urlList.map((item) => ({ label: item, value: item }))
        : []
      const results = queryString
        ? urlList.filter(this.createFilter(queryString))
        : urlList
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return (res) => {
        return res.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    handleSelect(item) {
      this.filterCond.url = item.value
    },
    handleUrlChange(list) {
      this.filterCond.urlList = list
    },
    handleSnChange(list) {
      this.filterCond.snList = list
    },
    handleKeyChange(list) {
      this.filterCond.keyList = list
    },
    getkafkaLogList: debounce(async function (pageSize) {
      try {
        if (pageSize == 1) {
          this.filterCond.current = 1
        } else if (pageSize && pageSize.current) {
          this.filterCond.current = pageSize.current
          this.filterCond.size = pageSize.size
        }
        const { current, size, topic, hour, urlList, snList, keyList } =
          this.filterCond
        if (
          topic === 'dev-edc-nat-forward' &&
          !urlList.length &&
          !snList.length
        ) {
          // 在选择的dev-edc-nat-forward配置时，url必传
          this.$message.warning(this.$t('kafkaSearchTab.urlInput'))
          return
        }
        this.loading = true
        const params = { pageNum: current, pageSize: size }
        if (topic) {
          params.topic = topic
        }
        if (hour !== null) {
          params.hour = hour
        }
        params.urlList = urlList
        params.snList = snList
        params.keyList = keyList
        const { data } = await getKafkaLogList(params)
        if (data) {
          this.kafkaLogList = data.records
          this.kafkaTotal = parseInt(data.total)
        } else {
          this.kafkaLogList = []
          this.kafkaTotal = 0
        }
        this.loading = false
      } catch (error) {
        console.error(error)
        this.loading = false
      }
    }, 500),
    handleReset() {
      // 条件重置
      this.filterCond = {
        ...this.filterCond,
        topic: this.topicOptions[0].value,
        hour: String(new Date().getHours()),
        key: '',
        url: '',
        urlList: [],
        snList: [],
        keyList: [],
        current: 1,
        size: 20,
      }
      this.kafkaLogList = []
      this.kafkaTotal = 0
      // this.$nextTick(() => {
      //   this.getkafkaLogList()
      // })
    },
    handleRefresh() {
      this.getkafkaLogList(1)
    },
    changeSnCopy(text) {
      const url = text
      const oInput = document.createElement('input')
      oInput.value = url
      document.body.appendChild(oInput)
      oInput.select() // 选择对象;

      document.execCommand('Copy') // 执行浏览器复制命令
      this.$message({
        message: this.$t('kafkaSearchTab.copySuccess'),
        type: 'success',
      })
      oInput.remove()
    },
    handleAdvanced() {
      this.showAdvance = true
    },
    closeAdvanceDialog() {
      this.showAdvance = false
      this.form = {
        advanceInput: '',
      }
      this.$refs['form'].resetFields()
    },
    confirmAdvanceSearch() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const { advanceInput } = this.form
          try {
            const json = advanceInput ? JSON.parse(advanceInput) : {}
            console.log('json', json)
            const {
              topic,
              hour,
              urlList = [],
              snList = [],
              keyList = [],
            } = json
            this.filterCond = {
              ...this.filterCond,
              topic,
              hour: String(hour),
              urlList,
              snList,
              keyList,
            }
            this.$nextTick(() => {
              // 触发查询
              this.handleRefresh()
            })
            this.showAdvance = false
            this.form = {
              advanceInput: '',
            }
            this.$refs['form'].resetFields()
          } catch (err) {
            this.$message.warning(this.$t('kafkaSearchTab.abnormalData'))
          }
        } else {
          return false
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.auto-complete-box {
  position: relative;
}
.search-icon {
  position: absolute;
  top: 8px;
  left: 10px;
}
.text-desc-box {
  padding: 0px 20px;
  margin-bottom: 10px;
}
::v-deep .tooltip-ellipsis-box {
  display: block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

::v-deep .search-wrapper .device-mutli-input .el-input__inner {
  width: 320px;
}

::v-deep .search-wrapper .device-mutli-input.isFocus .label {
  border: 1px solid #dcdfe6;
  border-radius: 18px;
  width: 320px;
  box-sizing: border-box;
}

::v-deep .search-wrapper .device-mutli-input.isFocus .el-input__inner {
  border: 1px solid #429efd;
  box-sizing: border-box;
}
</style>
