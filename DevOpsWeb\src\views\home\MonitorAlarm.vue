<template>
  <div class="treMon-list-wrapper">
    <div
      v-for="item2 of searchCond.areas"
      :key="`${searchCond.quota}-${
        item2 && item2.value ? item2.value : 'collapse'
      }`"
      :class="[
        searchCond.colonyNum > 1 && searchCond.quotaType === 'metric'
          ? 'treMon-item-wrapper'
          : 'treMon-line-wrapper',
      ]"
    >
      <alarm-component
        :collapse-key="`${searchCond.dcId}-${
          item2 && item2.value ? item2.value : 'area'
        }-${searchCond.quota}`"
        :search-cond="{ ...searchCond, area: item2 }"
        :collapse-open="collapseOpen"
      />
    </div>
  </div>
</template>
<script>
import AlarmComponent from './AlarmComponent.vue'
export default {
  name: 'MonitorAlarm',
  components: {
    AlarmComponent,
  },
  props: {
    searchCond: {
      type: Object,
      default: () => null,
    },
    collapseOpen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
}
</script>
<style lang="scss" scoped>
.treMon-list-wrapper {
  width: 100%;
  height: 100%;
  min-height: 300px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 10px;
  .treMon-item-wrapper {
    width: calc(50% - 10px);
    min-height: 300px;
    // margin: 10px 0px;
    // border: 1px solid #c6c6c6;
  }
  .treMon-line-wrapper {
    width: calc(100%);
    // height: 300px;
    // margin: 10px 0px;
    // border: 1px solid #c6c6c6;
  }
}
</style>
