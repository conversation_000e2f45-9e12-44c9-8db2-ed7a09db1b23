@import './vars.scss';

.el-form-item__content {
  .tvt-input .label-text-wrapper, .tvt-select .label-text-wrapper {
    height: 40px;
  }
}
.el-dialog__body .el-form{
  padding: 20px 20px 0px;
  flex: 1;
  .el-form-item{
    margin-bottom: 36px;
  }
  .tvt-input, .tvt-select {
    width: 100%;
  }
  .selectTree {
    .el-select-dropdown__item.selected {
      color: #409eff!important;
      font-weight: 400 !important;
    }
     .el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
        background-color: #fff;
    }
  }
  
}

.el-table {
  .el-table__header thead .el-table__cell {
    background-color: #FAFAFA;
  }
}

.footer-border button {
  border-radius: 16px;
}

.tvt-select .el-select-dropdown__item.selected {
  color: #409eff!important;
}

.el-select .el-tag:not(:has(.el-icon-close)) {
  padding: 0px 8px 0px 8px;
}

.el-select .el-tag:has(.el-icon-close) {
  padding: 0px 20px 0px 8px;
}

.tvt-select .tvt-select-inner .tvt-select-prefix {
  padding-left: 0px!important;
}



/* 改变 icon 字体路径变量，必需 */
$--font-path: '~element-ui/lib/theme-chalk/fonts';
@import "~element-ui/packages/theme-chalk/src/index";
// @import "~tvtcloudbaiscwidget/src/widgets/basic/vue-widget/styles/index";
