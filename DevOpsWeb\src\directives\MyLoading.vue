<template>
  <div class="htmleaf-container">
    <div class="loader">
      <div class="el-icon-loading" />
      <div class="my-loading-text">
        {{ msg }}
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'MyLoading',
  components: {},
  props: {},
  data() {
    return {
      msg: this.$t('LoadingMsg'),
    }
  },
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {},
}
</script>

<style lang="scss" scoped>
.htmleaf-container {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  font-size: 12px;
  min-height: 60px;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 999 !important;
}

.loader {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  z-index: 999 !important;
}

.loader .el-icon-loading {
  font-size: 30px;
  color: #429efd;
}

.loader .my-loading-text {
  width: 100%;
  position: absolute;
  font-size: 14px;
  left: 18px;
  top: 6px;
  color: #429efd;
  text-align: center;
}
</style>
