<template>
  <!-- 首页 -->
  <div class="home-wrapper">
    <div class="home-search-wrapper">
      <tvt-select-tree
        v-model="filterCond.treeQuota"
        clearable
        multiple
        collapse-tags
        class="select-tree"
        :options="treeData"
        :style="{ width: '250px' }"
        :default-expanded-keys="defaultExpandedKeys"
        @change="handleChange"
        @focus="handleFocus"
      />
      <tu-search-time
        ref="tuSearchTimeRef"
        v-model="filterCond.timeRange"
        class="search-time"
        @change="handleTimeChange"
      />
      <el-button type="primary" size="small" round @click="handleReset">
        {{ $t('reset') }}
      </el-button>
    </div>
    <div class="home-content-wrapper">
      <trend-monitor :search-cond="searchCond" />
    </div>
  </div>
</template>
<script>
import TuSearchTime from '@/components/common/TuSearchTime.vue'
// import TvtSelectTree from '@/components/common/TvtSelectTree.vue'
import TrendMonitor from './TrendMonitor.vue'
import { getDcInfoListApi } from '@/api/home.js'
import { getLocale } from '@/lang'
import {
  ctyTypeOptions,
  homeMetricsOptions,
  metricsPageObj,
  metricsTypeObj,
} from './config.js'
import { debounce } from '@/utils/common.js'
import { mapState } from 'vuex'
import selfLocaLStorage from '@/utils/selfLocalStorage'

export default {
  name: 'HomePage',
  components: {
    TrendMonitor,
    TuSearchTime,
    // TvtSelectTree
  },
  props: {},
  data() {
    return {
      // condConfig: {}, // 过滤条件配置项
      ctyTypeOptions,
      defaultExpandedKeys: ['monitor.nat'], // 默认展开p2p2.0
      filterCond: {
        // 默认设备端上线数量、设备端注册数量、设备端重定向数量、设备端请求转发数量、转发连接数
        // 有localStorage就用本地存储的，否则用默认的
        treeQuota: selfLocaLStorage.getItem('envHomeQuota') || [
          'monitor.nat-NatServer-nat_dev_online',
          'monitor.nat-NatServer-nat_dev_reg',
          'monitor.udt-RedirectServer-redirect_dev',
          'monitor.relay-RelayServer-relay_dev_req',
          'monitor.relay-RelayServer-relay_session',
        ],
        // timeRange: dateTimeRangeHour()
        timeRange: [],
        // quota: null
      },
      // 点击搜索时保存的查询条件
      preFilterCond: {
        treeQuota: [
          'monitor.nat-NatServer-nat_dev_online',
          'monitor.nat-NatServer-nat_dev_reg',
          'monitor.udt-RedirectServer-redirect_dev',
          'monitor.relay-RelayServer-relay_dev_req',
          'monitor.relay-RelayServer-relay_session',
        ],
        // timeRange: dateTimeRangeHour()
        timeRange: [],
      },
      allTreeData: homeMetricsOptions,
      treeData: homeMetricsOptions,
      quotaNameObj: {},
      defaultTreeQuota: [
        'monitor.nat-NatServer-nat_dev_online',
        'monitor.nat-NatServer-nat_dev_reg',
        'monitor.udt-RedirectServer-redirect_dev',
        'monitor.relay-RelayServer-relay_dev_req',
        'monitor.relay-RelayServer-relay_session',
      ],
      dcInfo: [
        // dc相关信息 { dcId: '1062947703740170242', dcName: 'ddc1', areas: ['ddc-01'] }, { dcId: '1', dcName: 'rdc', areas: [] }
      ],
      searchCond: null, // 传给子组件的查询条件
    }
  },
  computed: {
    ...mapState('params', ['envDcInfo']),
    ...mapState('monitor', ['dcInfoList']),
  },
  watch: {},
  created() {
    // RDC下才展示Https域名和证书的选项, DDC不展示
    if (!(this.envDcInfo && this.envDcInfo.dcType === 'rdc')) {
      const temp = this.allTreeData.slice()
      const httpIndex = temp.findIndex(
        (item) => item.value === 'monitor.domain'
      )
      if (httpIndex > -1) {
        temp.splice(httpIndex, 1)
      }
      const certIndex = temp.findIndex((item) => item.value === 'monitor.cert')
      if (certIndex > -1) {
        temp.splice(certIndex, 1)
      }
      this.allTreeData = temp
      this.treeData = temp
    }

    // 根据权限过滤treeData
    this.filterTreeDataByPermission()
  },
  async mounted() {
    // this.$EventBus.$emit('searchCond', this.preFilterCond)
    // 将树打平，用value为key,label为值
    this.quotaNameObj = this.flatTreeData(homeMetricsOptions).reduce(
      (pre, next) => {
        pre[next.value] = next.label
        return pre
      },
      {}
    )
    try {
      let data = []
      if (this.dcInfoList) {
        // 有dcInfo信息，则不用请求
        data = this.dcInfoList.slice()
      } else {
        // 否则走请求逻辑
        const res = await getDcInfoListApi(getLocale())
        data = res.data.slice()
        // 更新Vuex中存储的dcInfo
        this.$store.commit('monitor/DC_INFO_LIST', data)
      }
      const { treeQuota, timeRange } = this.filterCond
      this.dcInfo = data
      const treeQuotaName = treeQuota.map((item) => this.quotaNameObj[item])
      this.searchCond = {
        treeQuota,
        timeRange,
        // treeQuotaName: [
        //   this.$t('natTab.nat_dev_online'),
        //   this.$t('natTab.nat_dev_reg'),
        //   this.$t('redirectTab.redirect_dev'),
        //   this.$t('relayTab.relay_dev_req'),
        //   this.$t('relayTab.relay_session'),
        // ],
        treeQuotaName,
        dcInfo: data,
        timeStamp: Date.now(),
      }
    } catch (err) {
      console.log(err)
    }
  },
  methods: {
    /**
     * 根据权限过滤treeData
     */
    filterTreeDataByPermission() {
      // 获取dcResCodeObj
      const dcResCodeObj = selfLocaLStorage.getItem('dcResCodeObj') || {}

      // 过滤treeData
      const filteredTreeData = this.allTreeData.filter((item) => {
        const monitorType = item.value
        // 告警数据不过滤
        if (monitorType === 'alarm') {
          return true
        }

        // 通过metricsPageObj获取对应的权限页面
        const metricsPage = metricsPageObj[monitorType]
        if (!metricsPage || !dcResCodeObj[metricsPage]) {
          return false
        }

        // 检查是否有任何DC包含此监控类型
        const monitor = metricsTypeObj[monitorType]
        for (const dcId in dcResCodeObj[metricsPage]) {
          if (dcResCodeObj[metricsPage][dcId].includes(monitor)) {
            return true
          }
        }

        return false
      })

      this.treeData = filteredTreeData
    },

    /**
     * 树形转平面的迭代方法
     * option 1的el-option需要此方法绑定数据
     */
    flatTreeData(array, result = []) {
      array.forEach((item) => {
        result.push({ label: item.label, value: item.value })
        if (item.children && item.children.length !== 0) {
          this.flatTreeData(item.children, result)
        }
      })
      return JSON.parse(JSON.stringify(result))
    },
    // select-tree控件变化
    handleChange: debounce(function () {
      const { treeQuota } = this.filterCond
      if (treeQuota && treeQuota.length) {
        // 将treeQuotq暂存下来
        selfLocaLStorage.setItem('envHomeQuota', treeQuota)
        this.handleSearch()
      }
    }, 500),
    // 时间控件变化
    handleTimeChange() {
      const { treeQuota } = this.filterCond
      if (treeQuota && treeQuota.length) {
        this.handleSearch()
      } else {
        this.$message.warning(this.$t('quotaSelect'))
      }
    },
    // 点击搜索按钮
    handleSearch: debounce(function () {
      // console.log('查询参数', this.filterCond)
      // this.getMonitorDataList(this.filterCond)
      // 遍历
      const { treeQuota, timeRange } = this.filterCond
      if (treeQuota && treeQuota.length) {
        const treeQuotaName = treeQuota.map((item) => this.quotaNameObj[item])
        this.preFilterCond = {
          treeQuota: treeQuota.slice(),
          timeRange: timeRange.slice(),
        }
        // 如果需要搜索每次点击都重新请求，则加个时间戳
        this.searchCond = {
          treeQuota,
          timeRange,
          treeQuotaName: treeQuotaName || [],
          dcInfo: this.dcInfo.slice() || [],
          timeStamp: Date.now(),
        }
      } else {
        // this.$message.warning(this.$t('quotaSelect'))
      }
    }, 500),
    // 点击重置按钮
    handleReset: debounce(function () {
      this.defaultTreeQuota = [
        'monitor.nat-NatServer-nat_dev_online',
        'monitor.nat-NatServer-nat_dev_reg',
        'monitor.udt-RedirectServer-redirect_dev',
        'monitor.relay-RelayServer-relay_dev_req',
        'monitor.relay-RelayServer-relay_session',
      ]
      this.treeQuotaName = [
        this.$t('natTab.nat_dev_online'),
        this.$t('natTab.nat_dev_reg'),
        this.$t('redirectTab.redirect_dev'),
        this.$t('relayTab.relay_dev_req'),
        this.$t('relayTab.relay_session'),
      ]
      // 将treeQuotq暂存下来
      selfLocaLStorage.setItem(
        'envHomeQuota',
        JSON.stringify(this.defaultTreeQuota)
      )
      this.filterCond = {
        treeQuota: [
          'monitor.nat-NatServer-nat_dev_online',
          'monitor.nat-NatServer-nat_dev_reg',
          'monitor.udt-RedirectServer-redirect_dev',
          'monitor.relay-RelayServer-relay_dev_req',
          'monitor.relay-RelayServer-relay_session',
        ],
        timeRange: [],
      }
      this.searchCond = {
        treeQuota: [
          'monitor.nat-NatServer-nat_dev_online',
          'monitor.nat-NatServer-nat_dev_reg',
          'monitor.udt-RedirectServer-redirect_dev',
          'monitor.relay-RelayServer-relay_dev_req',
          'monitor.relay-RelayServer-relay_session',
        ],
        timeRange: [],
        treeQuotaName: [
          this.$t('natTab.nat_dev_online'),
          this.$t('natTab.nat_dev_reg'),
          this.$t('redirectTab.redirect_dev'),
          this.$t('relayTab.relay_dev_req'),
          this.$t('relayTab.relay_session'),
        ],
        dcInfo: this.dcInfo.slice() || [],
      }
    }, 500),
    // 选择框获取焦点时
    handleFocus() {
      // 手动关闭时间弹窗
      if (this.$refs.tuSearchTimeRef) {
        this.$refs.tuSearchTimeRef.$refs.dateTimePicker.handleClose()
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.home-wrapper {
  margin: 24px;
  width: calc(100% - 48px);
  height: calc(100% - 44px);
  position: relative;
  display: flex;
  flex-direction: column;
}
.home-search-wrapper {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  & > div {
    margin-right: 20px;
  }
}

.home-content-wrapper {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  padding-top: 20px;
}
</style>
<style>
.home-search-wrapper .el-input__inner {
  border: 1px solid #dcdfe6;
  border-radius: 16px;
}

.home-search-wrapper .select-tree .el-input__inner {
  height: 34px;
  line-height: 34px;
}

.home-search-wrapper .select-tree .el-input__icon {
  line-height: 34px;
}

.home-search-wrapper .select-tree .el-tag__close.el-icon-close {
  top: 2px;
}

.home-search-wrapper .select-tree .el-tag {
  border-radius: 12px;
}

.home-search-wrapper .search-time .tu-date-custom .data-picker-modal {
  border-radius: 16px;
}

.home-search-wrapper .tvt-input .tvt-field-set {
  border: none;
}

.home-search-wrapper .tvt-select .tvt-field-set {
  border: none;
}

.home-search-wrapper .btn-search {
  border-radius: 16px;
}

.home-search-wrapper .el-form--inline .el-form-item {
  margin-right: 20px;
}

.home-search-wrapper .btn-box button {
  height: 32px !important;
}

.home-search-wrapper .el-date-editor .el-range__close-icon {
  line-height: 28px;
}
.home-search-wrapper .el-select-dropdown__wrap {
  margin-top: 10px;
}
.home-search-wrapper .el-select-dropdown__list {
  padding: 0px;
}
</style>
