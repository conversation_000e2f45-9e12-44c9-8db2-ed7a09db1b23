import Vue from 'vue'
import { getLocale } from '@/lang'

const lang = getLocale() || 'zh-CN'

// 指标选择树的选项--用作下拉

export const TIMESPLITSECONDS = 6 * 60 * 60 * 1000 // 时间切片的间隔 六小时

// 采集项对应的指标项下拉选项
export const resourceFormOptions = [
  {
    label: Vue.prototype.$translate('hostMonitor'),
    value: 'monitor.host',
    children: [
      {
        label: Vue.prototype.$translate('cpuMemoryHost'),
        value: 'monitor.host-CPU/内存/主机流量',
        quotaAuthList: ['host_hostInfo'],
      },
      {
        label: Vue.prototype.$translate('configTab.disk'),
        value: 'monitor.host.disk-disk',
        quotaAuthList: ['host_disk'],
      },
      {
        label: Vue.prototype.$translate('applicationMonitor.process'),
        value: 'monitor.host.process-process',
        quotaAuthList: ['host_process'],
      },
    ],
  },
  {
    label: Vue.prototype.$translate('httpsDomain'),
    value: 'monitor.domain',
    quotaAuthList: ['domain'],
  },
  {
    label: Vue.prototype.$translate('certificate'),
    value: 'monitor.cert',
    quotaAuthList: ['cert'],
  },
]

export const appFormOptions = [
  {
    label: Vue.prototype.$translate('javaMicroService'),
    value: 'monitor.microservice.java',
    quotaAuthList: ['microservice_java'],
  },
  {
    label: Vue.prototype.$translate('cplusMicroService'),
    value: 'monitor.microservice.cplus',
    quotaAuthList: ['microservice_cplus'],
  },
  {
    label: Vue.prototype.$translate('webMicroService'),
    value: 'monitor.microservice.web',
    quotaAuthList: ['microservice_web'],
  },
  {
    label: Vue.prototype.$translate('mysqlInstance'),
    value: 'monitor.mysql.instance',
    quotaAuthList: ['mysqlInstance'],
  },
  {
    label: Vue.prototype.$translate('mysqlDb'),
    value: 'monitor.mysql.db',
    quotaAuthList: ['mysqlDb'],
  },
  {
    label: 'Redis',
    value: 'monitor.redis',
    quotaAuthList: ['redis'],
  },
  {
    label: Vue.prototype.$translate('kafkaNode'),
    value: 'monitor.kafka.node-kafkaNode',
    quotaAuthList: ['kafkaNode'],
  },
  {
    label: Vue.prototype.$translate('kafkaCluster'),
    value: 'monitor.kafka.cluster-kafkaCluster',
    quotaAuthList: ['kafkaCluster'],
  },
  {
    label: 'Mongo',
    value: 'monitor.mongo',
    quotaAuthList: ['mongo'],
  },
  {
    label: 'Nginx',
    value: 'monitor.nginx',
    quotaAuthList: ['nginx'],
  },
  {
    label: 'OSS/S3',
    value: 'monitor.oss',
    quotaAuthList: ['oss'],
  },
  {
    label: 'Queue',
    value: 'monitor.queue',
    quotaAuthList: ['queue'],
  },
  {
    label: 'Apache',
    value: 'monitor.apache',
    quotaAuthList: ['apache'],
  },
  {
    label: 'WsConnect',
    value: 'monitor.wsConnect',
    quotaAuthList: ['wsConnect'],
  },
]

export const businessFormOptions = [
  {
    label: Vue.prototype.$translate('email'),
    value: 'monitor.email',
    quotaAuthList: ['email'],
  },
  {
    label: Vue.prototype.$translate('textMessage'),
    value: 'monitor.sms',
    quotaAuthList: ['sms'],
  },
  {
    label: Vue.prototype.$translate('account'),
    value: 'monitor.account',
    quotaAuthList: ['account'],
  },
  {
    label: Vue.prototype.$translate('push2'),
    value: 'monitor.push',
    quotaAuthList: ['push'],
  },
  {
    label: 'Websocket',
    value: 'monitor.websocket',
    quotaAuthList: ['websocket'],
  },
  // 不展示DevopsWebsocket
  // {
  //   label: 'DevopsWebsocket',
  //   value: 'monitor.devopswebsocket',
  // },
  {
    label: 'Dpikey',
    value: 'monitor.dpikey',
    quotaAuthList: ['dpikey'],
  },
]

export const p2pFormOptions = [
  {
    label: 'NatServer',
    value: 'monitor.nat',
    quotaAuthList: ['nat'],
  },
  {
    label: 'RedirectServer',
    value: 'monitor.udt',
    quotaAuthList: ['udt'],
  },
  {
    label: 'RelayServer',
    value: 'monitor.relay',
    quotaAuthList: ['relay'],
  },
  {
    label: 'StunServer',
    value: 'monitor.stun',
    quotaAuthList: ['stun'],
  },
  {
    label: 'AccessServer',
    value: 'monitor.access',
    quotaAuthList: ['access'],
  },
  {
    label: 'StunRelayServer',
    value: 'monitor.stunRelay',
    quotaAuthList: ['stunrelay'],
  },
  {
    label: 'NatCluster',
    value: 'monitor.nat.cluster',
    quotaAuthList: ['nat_cluster'],
  },
  {
    label: 'RelayCluster',
    value: 'monitor.relay.cluster',
    quotaAuthList: ['relay_cluster'],
  },
  {
    label: 'StunCluster',
    value: 'monitor.stun.cluster',
    quotaAuthList: ['stun_cluster'],
  },
  {
    label: 'StunRelayCluster',
    value: 'monitor.stun.relay.cluster',
    quotaAuthList: ['stunrelay_cluster'],
  },
  {
    label: 'Push1Server',
    value: 'monitor.push1',
    // quotaAuthList: ['push1'],
    quotaAuthList: ['stun'],
  },
  {
    label: 'Push1SdnsServer',
    value: 'monitor.push1.sdns',
    // quotaAuthList: ['push1_sdns'],
    quotaAuthList: ['stun'],
  },
]

export const p2p1FormOptions = [
  {
    label: 'NatServer',
    value: 'monitor.nat1',
    quotaAuthList: ['nat1'],
  },
  {
    label: 'RelayServer',
    value: 'monitor.relay1',
    quotaAuthList: ['relay1'],
  },
]

// 图表需要显示的字段--用作下拉切换
export const metricsOptionsObj = {
  // 'CPU': ['cpuRate', 'loadOne', 'loadFive', 'loadFifteen'],
  'monitor.host-CPU/内存/主机流量': [
    {
      // label: Vue.prototype.$translate('resourceMonitor.hostCpuUseRate'),
      label: Vue.prototype.$translate('resourceTab.cpuRate'),
      value: 'monitor.host-CPU-cpuRate',
    },
    {
      label: Vue.prototype.$translate('resourceMonitor.averageLoad'),
      value: 'monitor.host-CPU-loadOne,loadFive,loadFifteen',
    }, // loadOne loadFive loadFifteen合并
    // { label: '1分钟平均负载', value: 'monitor.host-CPU-loadOne' },
    // { label: '5分钟平均负载', value: 'monitor.host-CPU-loadFive' },
    // { label: '15分钟平均负载', value: 'monitor.host-CPU-loadFifteen' },
    {
      label: Vue.prototype.$translate('resourceTab.availMemory'),
      value: 'monitor.host-memory-availMemory',
    },
    {
      label: Vue.prototype.$translate('resourceTab.memoryRate'),
      value: 'monitor.host-memory-memoryRate',
    },
    {
      label: Vue.prototype.$translate('resourceTab.recvmb'),
      value: 'monitor.host-hostTraffic-recvmb',
    },
    {
      label: Vue.prototype.$translate('resourceTab.sentmb'),
      value: 'monitor.host-hostTraffic-sentmb',
    },
    {
      label: Vue.prototype.$translate('resourceTab.ioRead'),
      value: 'monitor.host-CPU-ioRead',
    },
    {
      label: Vue.prototype.$translate('resourceTab.ioWrite'),
      value: 'monitor.host-CPU-ioWrite',
    },
    {
      label: Vue.prototype.$translate('resourceTab.bandwidthRate'),
      value: 'monitor.host-hostTraffic-bandwidthRate',
    },
    {
      label: 'hi(%)',
      value: 'monitor.host-CPU-hi',
    },
    {
      label: 'si(%)',
      value: 'monitor.host-CPU-si',
    },
    {
      label: 'st(%)',
      value: 'monitor.host-CPU-steal',
    },
  ],
  'monitor.host.disk-disk': [
    {
      // label: Vue.prototype.$translate('hostAvailDisk'),
      label: Vue.prototype.$translate('resourceTab.availDisk'),
      value: 'monitor.host.disk-disk-availDisk',
    },
    // { label: '空间使用率', value: 'monitor.host.disk-disk-diskRate' },
  ],
  'monitor.host.process-process': [
    {
      label: Vue.prototype.$translate('resourceTab.processCpuRate'),
      value: 'monitor.host.process-process-processCpuRate',
    },
    {
      label: Vue.prototype.$translate('resourceTab.memory'),
      value: 'monitor.host.process-process-processMemory',
    },
    {
      label: Vue.prototype.$translate('resourceTab.processMemoryRate'),
      value: 'monitor.host.process-process-processMemoryRate',
    },
  ],
  'monitor.microservice': [
    {
      label: Vue.prototype.$translate('microServeTab.memoryRate'),
      value: 'monitor.microservice-microService-memoryRate',
    },
    {
      label: Vue.prototype.$translate('microServeTab.cpuRate'),
      value: 'monitor.microservice-microService-cpuRate',
    },
  ],
  'monitor.microservice.java': [
    {
      label: Vue.prototype.$translate('microServeTab.memoryRate'),
      value: 'monitor.microservice.java-javaMicroService-memoryRate',
    },
    {
      label: Vue.prototype.$translate('microServeTab.cpuRate'),
      value: 'monitor.microservice.java-javaMicroService-cpuRate',
    },
  ],
  'monitor.microservice.cplus': [
    {
      label: Vue.prototype.$translate('microServeTab.memoryRate'),
      value: 'monitor.microservice.cplus-cplusMicroService-memoryRate',
    },
    {
      label: Vue.prototype.$translate('microServeTab.cpuRate'),
      value: 'monitor.microservice.cplus-cplusMicroService-cpuRate',
    },
  ],
  'monitor.microservice.web': [
    {
      label: Vue.prototype.$translate('microServeTab.memoryRate'),
      value: 'monitor.microservice.web-microService-memoryRate',
    },
    {
      label: Vue.prototype.$translate('microServeTab.cpuRate'),
      value: 'monitor.microservice.web-microService-cpuRate',
    },
  ],
  'monitor.mysql.instance': [
    {
      label: Vue.prototype.$translate('mySqlTab.availSpace'),
      value: 'monitor.mysql.instance-mysqlInstance-availSpace',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.spaceRate'),
      value: 'monitor.mysql.instance-mysqlInstance-spaceRate',
    },
  ],
  'monitor.mysql.db': [
    {
      label: Vue.prototype.$translate('mySqlTab.dbConnections'),
      value: 'monitor.mysql.db-mysqlDb-dbConnections',
    },
    // {
    //   label: Vue.prototype.$translate('mySqlTab.dbSpaces'),
    //   value: 'monitor.mysql.db-Mysql-dbSpaces',
    // },
  ],
  'monitor.redis': [
    // { label: 'Redis已使用内存', value: 'monitor.redis-Redis-memory' },
    {
      // label: Vue.prototype.$translate('redisMemoryRate'),
      label: Vue.prototype.$translate('redisTab.memoryRate'),
      value: 'monitor.redis-Redis-memoryRate',
    },
    {
      label: Vue.prototype.$translate('redisTab.connections'),
      value: 'monitor.redis-Redis-connections',
    },
    {
      label: Vue.prototype.$translate('redisTab.blockedClients'),
      value: 'monitor.redis-Redis-blockedClients',
    },
    {
      label: Vue.prototype.$translate('redisTab.rejectedConnections'),
      value: 'monitor.redis-Redis-rejectedConnections',
    },
  ],
  'monitor.kafka.node-kafkaNode': [
    {
      label: Vue.prototype.$translate('kafkaTab.cpuRate'),
      value: 'monitor.kafka.node-kafkaNode-cpuRate',
    },
    {
      label: Vue.prototype.$translate('kafkaTab.memoryRate'),
      value: 'monitor.kafka.node-kafkaNode-memoryRate',
    },
  ],
  'monitor.kafka.cluster-kafkaCluster': [
    {
      label: Vue.prototype.$translate('kafkaTab.lags'),
      value: 'monitor.kafka.cluster-kafkaCluster-lags',
    },
  ],
  'monitor.mongo': [
    {
      label: Vue.prototype.$translate('mongoTab.connections'),
      value: 'monitor.mongo-Mongo-connections',
    },
    {
      label: Vue.prototype.$translate('mongoTab.memory'),
      value: 'monitor.mongo-Mongo-memory',
    },
    {
      label: Vue.prototype.$translate('mongoTab.requests'),
      value: 'monitor.mongo-Mongo-requests',
    },
    {
      label: Vue.prototype.$translate('mongoTab.spaces'),
      value: 'monitor.mongo-Mongo-spaces',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.spaceRate'),
      value: 'monitor.mongo-Mongo-spaceRate',
    },
  ],
  'monitor.nginx': [
    {
      label: Vue.prototype.$translate('nginxTab.connections'),
      value: 'monitor.nginx-Nginx-connections',
    },
    {
      label: Vue.prototype.$translate('nginxTab.availConnections'),
      value: 'monitor.nginx-Nginx-availConnections',
    },
    {
      label: Vue.prototype.$translate('nginxTab.incrRequests'),
      value: 'monitor.nginx-Nginx-incrRequests',
    },
  ],
  'monitor.oss': [
    {
      label: Vue.prototype.$translate('ossTab.storages'),
      value: 'monitor.oss-OSS/S3-storages',
    },
    {
      label: Vue.prototype.$translate('ossTab.objects'),
      value: 'monitor.oss-OSS/S3-objects',
    },
  ],
  'monitor.queue': [
    {
      label: Vue.prototype.$translate('queueTab.usedRate'),
      value: 'monitor.queue-Queue-usedRate',
    },
  ],
  'monitor.apache': [
    {
      label: Vue.prototype.$translate('apacheTab.busyWorkers'),
      value: 'monitor.apache-Apache-busyWorkers',
    },
    {
      label: Vue.prototype.$translate('apacheTab.idleWorkers'),
      value: 'monitor.apache-Apache-idleWorkers',
    },
    {
      label: Vue.prototype.$translate('apacheTab.incrRequests'),
      value: 'monitor.apache-Apache-incrRequests',
    },
  ],
  'monitor.wsConnect': [
    {
      label: Vue.prototype.$translate('wsConnectTab.totalConnectNum'),
      value: 'monitor.wsConnect-WsConnect-totalConnectNum',
    },
    {
      label: Vue.prototype.$translate('wsConnectTab.connectNum'),
      value: 'monitor.wsConnect-WsConnect-connectNum',
    },
  ],
  'monitor.domain': [
    {
      label: Vue.prototype.$translate('domainCertTab.remainDays'),
      value: 'monitor.domain-httpsDomain-remainDays',
    },
  ],
  'monitor.cert': [
    {
      label: Vue.prototype.$translate('domainCertTab.remainDays'),
      value: 'monitor.cert-cert-remainDays',
    },
  ],
  'monitor.nat': [
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online'),
      value: 'monitor.nat-NatServer-nat_dev_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_reg'),
      value: 'monitor.nat-NatServer-nat_dev_reg',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_online'),
      value: 'monitor.nat-NatServer-nat_client_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_reg'),
      value: 'monitor.nat-NatServer-nat_client_reg',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_sn_online'),
      value: 'monitor.nat-NatServer-nat_cleint_p2p_req_sn_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_token_online'),
      value: 'monitor.nat-NatServer-nat_cleint_p2p_req_token_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_offline'),
      value: 'monitor.nat-NatServer-nat_client_offline',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat'),
      value: 'monitor.nat-NatServer-nat_dev_big_heat',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_offline'),
      value: 'monitor.nat-NatServer-nat_dev_offline',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat'),
      value: 'monitor.nat-NatServer-nat_dev_small_heat',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip'),
      value: 'monitor.nat-NatServer-nat_dev_update_ip',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
      value: 'monitor.nat-NatServer-nat_dev_big_heat_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
      value: 'monitor.nat-NatServer-nat_dev_small_heat_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
      value: 'monitor.nat-NatServer-nat_dev_update_ip_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online_dec'),
      value: 'monitor.nat-NatServer-nat_dev_online_dec',
    },
  ],
  'monitor.nat1': [
    {
      label: Vue.prototype.$translate('natTab.devNum'),
      value: 'monitor.nat1-NatServer-dev_num',
    },
    {
      label: Vue.prototype.$translate('natTab.connectionNum'),
      value: 'monitor.nat1-NatServer-connection_num',
    },
    {
      label: Vue.prototype.$translate('natTab.clientNum'),
      value: 'monitor.nat1-NatServer-client_num',
    },
    {
      label: Vue.prototype.$translate('natTab.p2pReqNum'),
      value: 'monitor.nat1-NatServer-p2pReqNum',
    },
    {
      label: Vue.prototype.$translate('natTab.relayReqNum'),
      value: 'monitor.nat1-NatServer-relayReqNum',
    },
  ],
  'monitor.udt': [
    {
      label: Vue.prototype.$translate('redirectTab.redirect_dev'),
      value: 'monitor.udt-RedirectServer-redirect_dev',
    },
    {
      label: Vue.prototype.$translate('redirectTab.redirect_dev_inc'),
      value: 'monitor.udt-RedirectServer-redirect_dev_inc',
    },
    {
      label: Vue.prototype.$translate('redirectTab.redirect_client'),
      value: 'monitor.udt-RedirectServer-redirect_client',
    },
    {
      label: Vue.prototype.$translate('redirectTab.redirect_client_full_list'),
      value: 'monitor.udt-RedirectServer-redirect_client_full_list',
    },
  ],
  'monitor.relay': [
    {
      label: Vue.prototype.$translate('relayTab.relay_dev_req'),
      value: 'monitor.relay-RelayServer-relay_dev_req',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session'),
      value: 'monitor.relay-RelayServer-relay_session',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_client_req'),
      value: 'monitor.relay-RelayServer-relay_client_req',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session_end'),
      value: 'monitor.relay-RelayServer-relay_session_end',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_speed_limit_count'),
      value: 'monitor.relay-RelayServer-relay_speed_limit_count',
    },
  ],
  'monitor.relay1': [
    {
      label: Vue.prototype.$translate('relayTab.currentSession'),
      value: 'monitor.relay1-RelayServer-current_session',
    },
  ],
  'monitor.stun': [
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online'),
      value: 'monitor.stun-StunServer-nat_dev_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_reg'),
      value: 'monitor.stun-StunServer-nat_dev_reg',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_online'),
      value: 'monitor.stun-StunServer-nat_client_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_reg'),
      value: 'monitor.stun-StunServer-nat_client_reg',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_sn_online'),
      value: 'monitor.stun-StunServer-nat_cleint_p2p_req_sn_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_token_online'),
      value: 'monitor.stun-StunServer-nat_cleint_p2p_req_token_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_offline'),
      value: 'monitor.stun-StunServer-nat_client_offline',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat'),
      value: 'monitor.stun-StunServer-nat_dev_big_heat',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_offline'),
      value: 'monitor.stun-StunServer-nat_dev_offline',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat'),
      value: 'monitor.stun-StunServer-nat_dev_small_heat',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip'),
      value: 'monitor.stun-StunServer-nat_dev_update_ip',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
      value: 'monitor.stun-StunServer-nat_dev_big_heat_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
      value: 'monitor.stun-StunServer-nat_dev_small_heat_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
      value: 'monitor.stun-StunServer-nat_dev_update_ip_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online_dec'),
      value: 'monitor.stun-StunServer-nat_dev_online_dec',
    },
  ],
  'monitor.access': [
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online'),
      value: 'monitor.access-AccessServer-nat_dev_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_reg'),
      value: 'monitor.access-AccessServer-nat_dev_reg',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat'),
      value: 'monitor.access-AccessServer-nat_dev_big_heat',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_offline'),
      value: 'monitor.access-AccessServer-nat_dev_offline',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat'),
      value: 'monitor.access-AccessServer-nat_dev_small_heat',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip'),
      value: 'monitor.access-AccessServer-nat_dev_update_ip',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
      value: 'monitor.access-AccessServer-nat_dev_big_heat_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
      value: 'monitor.access-AccessServer-nat_dev_small_heat_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
      value: 'monitor.access-AccessServer-nat_dev_update_ip_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online_dec'),
      value: 'monitor.access-AccessServer-nat_dev_online_dec',
    },
  ],
  'monitor.stunRelay': [
    {
      label: Vue.prototype.$translate('relayTab.relay_dev_req'),
      value: 'monitor.stunRelay-RelayServer-relay_dev_req',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session'),
      value: 'monitor.stunRelay-RelayServer-relay_session',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_client_req'),
      value: 'monitor.stunRelay-RelayServer-relay_client_req',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session_end'),
      value: 'monitor.stunRelay-RelayServer-relay_session_end',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_speed_limit_count'),
      value: 'monitor.stunRelay-RelayServer-relay_speed_limit_count',
    },
  ],
  'monitor.nat.cluster': [
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online'),
      value: 'monitor.nat.cluster-NatCluster-nat_dev_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_reg'),
      value: 'monitor.nat.cluster-NatCluster-nat_dev_reg',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_online'),
      value: 'monitor.nat.cluster-NatCluster-nat_client_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_reg'),
      value: 'monitor.nat.cluster-NatCluster-nat_client_reg',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_sn_online'),
      value: 'monitor.nat.cluster-NatCluster-nat_cleint_p2p_req_sn_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_token_online'),
      value: 'monitor.nat.cluster-NatCluster-nat_cleint_p2p_req_token_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_offline'),
      value: 'monitor.nat.cluster-NatCluster-nat_client_offline',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat'),
      value: 'monitor.nat.cluster-NatCluster-nat_dev_big_heat',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_offline'),
      value: 'monitor.nat.cluster-NatCluster-nat_dev_offline',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat'),
      value: 'monitor.nat.cluster-NatCluster-nat_dev_small_heat',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip'),
      value: 'monitor.nat.cluster-NatCluster-nat_dev_update_ip',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
      value: 'monitor.nat.cluster-NatCluster-nat_dev_big_heat_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
      value: 'monitor.nat.cluster-NatCluster-nat_dev_small_heat_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
      value: 'monitor.nat.cluster-NatCluster-nat_dev_update_ip_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online_dec'),
      value: 'monitor.nat.cluster-NatCluster-nat_dev_online_dec',
    },
  ],
  'monitor.relay.cluster': [
    {
      label: Vue.prototype.$translate('relayTab.relay_dev_req'),
      value: 'monitor.relay.cluster-RelayCluster-relay_dev_req',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session'),
      value: 'monitor.relay.cluster-RelayCluster-relay_session',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_client_req'),
      value: 'monitor.relay.cluster-RelayCluster-relay_client_req',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session_end'),
      value: 'monitor.relay.cluster-RelayCluster-relay_session_end',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_speed_limit_count'),
      value: 'monitor.relay.cluster-RelayCluster-relay_speed_limit_count',
    },
  ],
  'monitor.stun.cluster': [
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online'),
      value: 'monitor.stun.cluster-StunCluster-nat_dev_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_reg'),
      value: 'monitor.stun.cluster-StunCluster-nat_dev_reg',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_online'),
      value: 'monitor.stun.cluster-StunCluster-nat_client_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_reg'),
      value: 'monitor.stun.cluster-StunCluster-nat_client_reg',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_sn_online'),
      value: 'monitor.stun.cluster-StunCluster-nat_cleint_p2p_req_sn_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_token_online'),
      value: 'monitor.stun.cluster-StunCluster-nat_cleint_p2p_req_token_online',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_offline'),
      value: 'monitor.stun.cluster-StunCluster-nat_client_offline',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat'),
      value: 'monitor.stun.cluster-StunCluster-nat_dev_big_heat',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_offline'),
      value: 'monitor.stun.cluster-StunCluster-nat_dev_offline',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat'),
      value: 'monitor.stun.cluster-StunCluster-nat_dev_small_heat',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip'),
      value: 'monitor.stun.cluster-StunCluster-nat_dev_update_ip',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
      value: 'monitor.stun.cluster-StunCluster-nat_dev_big_heat_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
      value: 'monitor.stun.cluster-StunCluster-nat_dev_small_heat_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
      value: 'monitor.stun.cluster-StunCluster-nat_dev_update_ip_v6',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online_dec'),
      value: 'monitor.stun.cluster-StunCluster-nat_dev_online_dec',
    },
  ],
  'monitor.stun.relay.cluster': [
    {
      label: Vue.prototype.$translate('relayTab.relay_dev_req'),
      value: 'monitor.stun.relay.cluster-StunRelayCluster-relay_dev_req',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session'),
      value: 'monitor.stun.relay.cluster-StunRelayCluster-relay_session',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_client_req'),
      value: 'monitor.stun.relay.cluster-StunRelayCluster-relay_client_req',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session_end'),
      value: 'monitor.stun.relay.cluster-StunRelayCluster-relay_session_end',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_speed_limit_count'),
      value:
        'monitor.stun.relay.cluster-StunRelayCluster-relay_speed_limit_count',
    },
  ],
  'monitor.push1': [
    {
      label: Vue.prototype.$translate('push1Tab.tpnsMsgRecv'),
      value: 'monitor.push1-Push1Server-tpnsMsgRecv',
    },
    {
      label: Vue.prototype.$translate('push1Tab.tpnsDispMsg'),
      value: 'monitor.push1-Push1Server-tpnsDispMsg',
    },
    {
      label: Vue.prototype.$translate('push1Tab.tpnsDevTotal'),
      value: 'monitor.push1-Push1Server-tpnsDevTotal',
    },
    {
      label: Vue.prototype.$translate('push1Tab.tpnsEnableDev'),
      value: 'monitor.push1-Push1Server-tpnsEnableDev',
    },
    {
      label: Vue.prototype.$translate('push1Tab.tpnsPendingJobCount'),
      value: 'monitor.push1-Push1Server-tpnsPendingJobCount',
    },
    {
      label: Vue.prototype.$translate('push1Tab.amdsPushReq'),
      value: 'monitor.push1-Push1Server-amdsPushReq',
    },
    {
      label: Vue.prototype.$translate('push1Tab.amdsPushMsg'),
      value: 'monitor.push1-Push1Server-amdsPushMsg',
    },
    {
      label: Vue.prototype.$translate('push1Tab.amdsExpCertCount'),
      value: 'monitor.push1-Push1Server-amdsExpCertCount',
    },
    {
      label: Vue.prototype.$translate('push1Tab.gmdsPushReq'),
      value: 'monitor.push1-Push1Server-gmdsPushReq',
    },
    {
      label: Vue.prototype.$translate('push1Tab.gmdsDispMsg'),
      value: 'monitor.push1-Push1Server-gmdsDispMsg',
    },
    {
      label: Vue.prototype.$translate('push1Tab.gpnsPushReq'),
      value: 'monitor.push1-Push1Server-gpnsPushReq',
    },
    {
      label: Vue.prototype.$translate('push1Tab.gpnsPushMsg'),
      value: 'monitor.push1-Push1Server-gpnsPushMsg',
    },
    {
      label: Vue.prototype.$translate('push1Tab.gpnsClientNum'),
      value: 'monitor.push1-Push1Server-gpnsClientNum',
    },
    {
      label: Vue.prototype.$translate('push1Tab.gpnsClientRegNum'),
      value: 'monitor.push1-Push1Server-gpnsClientRegNum',
    },
  ],
  'monitor.push1.sdns': [
    {
      label: Vue.prototype.$translate('push1Tab.sdnsDevSrvReq'),
      value: 'monitor.push1.sdns-Push1SdnsServer-sdnsDevSrvReq',
    },
    {
      label: Vue.prototype.$translate('push1Tab.sdnsAppSrvReq'),
      value: 'monitor.push1.sdns-Push1SdnsServer-sdnsAppSrvReq',
    },
  ],
  'monitor.websocket': [
    {
      label: Vue.prototype.$translate('websocketTab.disconnects'),
      value: 'monitor.websocket-Websocket-disconnects',
    },
  ],
  'monitor.devopswebsocket': [],
  'monitor.dpikey': [
    // {
    //   label: Vue.prototype.$translate('dpikeyErrors'),
    //   value: 'monitor.dpikey-dpikey-errors',
    // },
  ],
  'monitor.email': [
    {
      label: Vue.prototype.$translate('emailTab.nums'),
      value: 'monitor.email-email-nums',
    },
    {
      label: Vue.prototype.$translate('emailTab.errors'),
      value: 'monitor.email-email-errors',
    },
    {
      label: Vue.prototype.$translate('emailTab.limits'),
      value: 'monitor.email-email-limits',
    },
    // {
    //   label: Vue.prototype.$translate('emailTab.tps'),
    //   value: 'monitor.email-邮件-tps',
    // },
  ],
  'monitor.sms': [
    {
      label: Vue.prototype.$translate('textMessageTab.nums'),
      value: 'monitor.sms-textMessage-nums',
    },
    {
      label: Vue.prototype.$translate('textMessageTab.errors'),
      value: 'monitor.sms-textMessage-errors',
    },
    {
      label: Vue.prototype.$translate('textMessageTab.limits'),
      value: 'monitor.sms-textMessage-limits',
    },
  ],
  'monitor.account': [
    {
      label: Vue.prototype.$translate('accountTab.logins'),
      value: 'monitor.account-account-logins',
    },
    {
      label: Vue.prototype.$translate('accountTab.registers'),
      value: 'monitor.account-account-registers',
    },
  ],
  'monitor.push': [
    {
      label: Vue.prototype.$translate('pushTab.pushs'),
      value: 'monitor.push-push2-pushs',
    },
    {
      label: Vue.prototype.$translate('pushTab.onlinePushs'),
      value: 'monitor.push-push2-onlinePushs',
    },
    {
      label: Vue.prototype.$translate('pushTab.offlinePushs'),
      value: 'monitor.push-push2-offlinePushs',
    },
  ],
}

export const resourceColumns = {
  'monitor.host-CPU/内存/主机流量': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': 140,
    }, // unCheckAble: true 表示为固定展示，不可勾选隐藏的
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': 160,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.cpuRate'),
      prop: 'cpuRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 200,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.loadOne'),
      prop: 'loadOne',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 210,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.loadFive'),
      prop: 'loadFive',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 210,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.loadFifteen'),
      prop: 'loadFifteen',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 150 : 210,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.availMemory'),
      prop: 'availMemory',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 130 : 190,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.memoryRate'),
      prop: 'memoryRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 200,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.recvmb'),
      prop: 'recvmb',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 150 : 220,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.sentmb'),
      prop: 'sentmb',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 150 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.bandwidthRate'),
      prop: 'bandwidthRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 250,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.ioRead'),
      prop: 'ioRead',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 140,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.ioWrite'),
      prop: 'ioWrite',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 140,
      sortable: 'custom',
    },
    {
      label: 'hi(%)',
      prop: 'hi',
      slotName: 'bodyCell',
      'min-width': 100,
      sortable: 'custom',
    },
    {
      label: 'si(%)',
      prop: 'si',
      slotName: 'bodyCell',
      'min-width': 100,
      sortable: 'custom',
    },
    {
      label: 'st(%)',
      prop: 'steal',
      slotName: 'bodyCell',
      'min-width': 100,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.nfsAvailable'),
      prop: 'nfsAvailable',
      slotName: 'bodyCell',
      'min-width': 120,
    },
  ],
  'monitor.host.disk-disk': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': 160,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': 200,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.fs'),
      prop: 'fs',
      'min-width': 240,
    },
    {
      label: Vue.prototype.$translate('resourceTab.disk'),
      prop: 'disk',
      'min-width': 200,
    },
    {
      label: Vue.prototype.$translate('resourceTab.availDisk'),
      prop: 'availDisk',
      slotName: 'bodyCell',
      'min-width': 200,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.diskRate'),
      prop: 'diskRate',
      slotName: 'bodyCell',
      'min-width': 200,
      sortable: 'custom',
    },
  ],
  'monitor.host.process-process': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': lang === 'zh-CN' ? 140 : 110,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 160 : 120,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.pid'),
      prop: 'pid',
      'min-width': lang === 'zh-CN' ? 120 : 110,
    },
    {
      label: Vue.prototype.$translate('resourceTab.parentPid'),
      prop: 'parentPid',
      'min-width': lang === 'zh-CN' ? 120 : 90,
    },
    {
      label: Vue.prototype.$translate('resourceTab.name'),
      prop: 'name',
      'min-width': lang === 'zh-CN' ? 200 : 170,
    },
    {
      label: Vue.prototype.$translate('resourceTab.user'),
      prop: 'user',
      'min-width': lang === 'zh-CN' ? 120 : 70,
    },
    {
      label: Vue.prototype.$translate('resourceTab.processCpuRate'),
      prop: 'processCpuRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 190 : 130,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.memory'),
      prop: 'processMemory',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 130,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.processMemoryRate'),
      prop: 'processMemoryRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.state'),
      prop: 'state',
      'min-width': 130,
    },
    {
      label: Vue.prototype.$translate('resourceTab.threads'),
      prop: 'threads',
      'min-width': lang === 'zh-CN' ? 120 : 110,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.openFiles'),
      prop: 'openFiles',
      'min-width': 120,
      sortable: 'custom',
    },
  ],
}

export const resourceData = {
  CPU: [
    {
      ip: '***********',
      host: 'nat-us-02',
      ct: '2024-01-30 15:26:10',
      cpuRate: '50%',
      loadOne: '0.5',
      loadFive: '1',
      loadFifteen: '1.2',
    },
    {
      ip: '************',
      host: 'bus-us-02',
      ct: '2024-01-30 15:22:15',
      cpuRate: '50%',
      loadOne: '0.5',
      loadFive: '1',
      loadFifteen: '1.2',
    },
    {
      ip: '***********',
      host: 'nat-us-02',
      ct: '2024-01-30 15:26:10',
      cpuRate: '50%',
      loadOne: '0.5',
      loadFive: '1',
      loadFifteen: '1.2',
    },
    {
      ip: '************',
      host: 'bus-us-02',
      ct: '2024-01-30 15:22:15',
      cpuRate: '50%',
      loadOne: '0.5',
      loadFive: '1',
      loadFifteen: '1.2',
    },
    {
      ip: '***********',
      host: 'nat-us-02',
      ct: '2024-01-30 15:26:10',
      cpuRate: '50%',
      loadOne: '0.5',
      loadFive: '1',
      loadFifteen: '1.2',
    },
    {
      ip: '************',
      host: 'bus-us-02',
      ct: '2024-01-30 15:22:15',
      cpuRate: '50%',
      loadOne: '0.5',
      loadFive: '1',
      loadFifteen: '1.2',
    },
    {
      ip: '***********',
      host: 'nat-us-02',
      ct: '2024-01-30 15:26:10',
      cpuRate: '50%',
      loadOne: '0.5',
      loadFive: '1',
      loadFifteen: '1.2',
    },
    {
      ip: '************',
      host: 'bus-us-02',
      ct: '2024-01-30 15:22:15',
      cpuRate: '50%',
      loadOne: '0.5',
      loadFive: '1',
      loadFifteen: '1.2',
    },
    {
      ip: '***********',
      host: 'nat-us-02',
      ct: '2024-01-30 15:26:10',
      cpuRate: '50%',
      loadOne: '0.5',
      loadFive: '1',
      loadFifteen: '1.2',
    },
    {
      ip: '************',
      host: 'bus-us-02',
      ct: '2024-01-30 15:22:15',
      cpuRate: '50%',
      loadOne: '0.5',
      loadFive: '1',
      loadFifteen: '1.2',
    },
  ],
  内存: [
    {
      ip: '***********',
      host: 'nat-us-02',
      ct: '2024-01-30 15:26:10',
      availMemory: '2048',
      memoryRate: '80',
    },
    {
      ip: '***********',
      host: 'nat-us-02',
      ct: '2024-01-30 15:27:40',
      availMemory: '1024',
      memoryRate: '90',
    },
  ],
  磁盘: [
    {
      ip: '***********',
      host: 'nat-us-02',
      ct: '2024-01-30 15:26:10',
      availDisk: '20480',
      diskRate: '80',
    },
    {
      ip: '***********',
      host: 'nat-us-02',
      ct: '2024-01-30 15:26:40',
      availDisk: '10240',
      diskRate: '90',
    },
    {
      ip: '***********',
      host: 'nat-us-02',
      ct: '2024-01-30 15:27:10',
      availDisk: '10240',
      diskRate: '90',
    },
    {
      ip: '***********',
      host: 'nat-us-02',
      ct: '2024-01-30 15:28:10',
      availDisk: '10240',
      diskRate: '90',
    },
    {
      ip: '***********',
      host: 'nat-us-02',
      ct: '2024-01-30 15:29:10',
      availDisk: '10240',
      diskRate: '90',
    },
  ],
  主机流量: [
    {
      ip: '***********',
      host: 'nat-us-02',
      ct: '2024-01-30 15:26:10',
      recvmb: '2048',
      sentmb: '1024',
      bandwidthRate: '10',
    },
    {
      ip: '***********',
      host: 'nat-us-04',
      ct: '2024-01-30 15:27:10',
      recvmb: '2048',
      sentmb: '1024',
      bandwidthRate: '10',
    },
    {
      ip: '***********',
      host: 'nat-us-07',
      ct: '2024-01-30 15:28:10',
      recvmb: '2048',
      sentmb: '1024',
      bandwidthRate: '10',
    },
  ],
  进程: [
    {
      ip: '***********',
      host: 'nat-us-01',
      ct: '2024-01-30 15:26:10',
      memory: '100',
      cpuRate: '20',
      memoryRate: '10',
    },
    {
      ip: '***********',
      host: 'nat-us-02',
      ct: '2024-01-30 15:26:10',
      memory: '100',
      cpuRate: '20',
      memoryRate: '10',
    },
    {
      ip: '***********',
      host: 'nat-us-03',
      ct: '2024-01-30 15:26:10',
      memory: '100',
      cpuRate: '20',
      memoryRate: '10',
    },
    {
      ip: '***********',
      host: 'nat-us-04',
      ct: '2024-01-30 15:26:10',
      memory: '100',
      cpuRate: '20',
      memoryRate: '10',
    },
  ],
}

export const domainFormOptions = (areaOptions) => [
  {
    label: '',
    prop: 'ctSubtype',
    element: 'el-radio-group',
    initValue: 'Https域名',
    options: [
      { label: 'Https域名', value: 'Https域名' },
      { label: '证书', value: '证书' },
    ],
  },
  {
    label: '',
    prop: 'dcId',
    element: 'tvt-select',
    options: areaOptions,
    size: 'small',
  },
  {
    label: '',
    prop: 'dateTime',
    type: 'datetimerange',
    element: 'el-date-picker',
    size: 'small',
  },
]

export const domainColumns = {
  'monitor.domain': [
    {
      label: Vue.prototype.$translate('domainCertTab.domain'),
      prop: 'domain',
      'min-width': 300,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': 180,
    },
    {
      label: Vue.prototype.$translate('domainCertTab.customerName'),
      prop: 'customerName',
      'min-width': 200,
    },
    {
      label: Vue.prototype.$translate('domainCertTab.envType'),
      prop: 'envType',
      'min-width': 200,
    },
    {
      label: Vue.prototype.$translate('domainCertTab.available'),
      prop: 'available',
      slotName: 'bodyCell',
      'min-width': 200,
    },
    {
      label: Vue.prototype.$translate('domainCertTab.remainDays'),
      prop: 'remainDays',
      slotName: 'bodyCell',
      'min-width': 210,
      sortable: 'custom',
      order: 'asc', // 默认升序排序  升序 asc 降序 desc
    },
  ],
  'monitor.cert': [
    {
      label: Vue.prototype.$translate('domainCertTab.cert'),
      prop: 'cert',
      'min-width': 380,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': 180,
    },
    {
      label: Vue.prototype.$translate('domainCertTab.appId'),
      prop: 'appId',
      'min-width': 250,
    },
    {
      label: Vue.prototype.$translate('domainCertTab.customerName'),
      prop: 'customerName',
      'min-width': 200,
    },
    // {
    //   label: Vue.prototype.$translate('domainCertTab.ptp'),
    //   prop: 'ptp',
    //   'min-width': 200,
    // },
    {
      label: Vue.prototype.$translate('domainCertTab.remainDays'),
      prop: 'remainDays',
      slotName: 'bodyCell',
      'min-width': 200,
      sortable: 'custom',
      order: 'asc', // 默认升序排序  升序 asc 降序 desc
    },
  ],
}

export const domainData = {
  Https域名: [],
  证书: [],
}

export const appColumns = {
  'monitor.microservice': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': 160,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      'min-width': 240,
    },
    {
      label: Vue.prototype.$translate('microServeTab.instanceId'),
      prop: 'instanceId',
      'min-width': 120,
    },
    {
      label: Vue.prototype.$translate('microServeTab.available'),
      prop: 'available',
      key: 'microAvailable',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 150,
    },
    {
      label: Vue.prototype.$translate('microServeTab.cpuRate'),
      prop: 'cpuRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.memory'),
      prop: 'memory',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.memoryRate'),
      prop: 'memoryRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 210,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.restarts'),
      prop: 'restarts',
      slotName: 'bodyCell',
      'min-width': 110,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.dmpfiles'),
      prop: 'dmpfiles',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 150 : 210,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectPort'),
      prop: 'projectPort',
      'min-width': lang === 'zh-CN' ? 120 : 150,
    },
    {
      label: Vue.prototype.$translate('microServeTab.startXmx'),
      prop: 'startXmx',
      'min-width': lang === 'zh-CN' ? 160 : 120,
    },
    {
      label: Vue.prototype.$translate('microServeTab.pid'),
      prop: 'pid',
      'min-width': 120,
    },
  ],
  'monitor.microservice.java': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      key: 'javaIp',
      'min-width': 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      key: 'javaHost',
      'min-width': 160,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      key: 'javaSt',
      slotName: 'bodyCell',
      'min-width': 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      key: 'javaProjectName',
      'min-width': 200,
    },
    {
      label: Vue.prototype.$translate('microServeTab.instanceId'),
      prop: 'instanceId',
      key: 'javaInstanceId',
      'min-width': 100,
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectPort'),
      prop: 'projectPort',
      key: 'javaProjectPort',
      'min-width': lang === 'zh-CN' ? 120 : 150,
    },
    {
      label: Vue.prototype.$translate('microServeTab.available'),
      prop: 'available',
      key: 'javaAvailable',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 150,
    },
    {
      label: Vue.prototype.$translate('microServeTab.cpuRate'),
      prop: 'cpuRate',
      key: 'javaCpuRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.memory'),
      prop: 'memory',
      key: 'javaMemory',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.memoryRate'),
      prop: 'memoryRate',
      key: 'javaMemoryRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 210,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.restarts'),
      prop: 'restarts',
      key: 'javaRestarts',
      slotName: 'bodyCell',
      'min-width': 110,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.dmpfiles'),
      prop: 'dmpfiles',
      key: 'javaDmpfiles',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 150 : 210,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.startXmx'),
      prop: 'startXmx',
      key: 'javaStartXmx',
      'min-width': lang === 'zh-CN' ? 160 : 120,
    },
    {
      label: Vue.prototype.$translate('microServeTab.pid'),
      prop: 'pid',
      key: 'javaPid',
      'min-width': 120,
    },
  ],
  'monitor.microservice.cplus': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      key: 'cplusIp',
      'min-width': 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      key: 'cplusHost',
      'min-width': 160,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      key: 'cplusSt',
      slotName: 'bodyCell',
      'min-width': 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      key: 'cplusClusterName',
      slotName: 'bodyCell',
      'min-width': 120,
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      key: 'cplusProjectName',
      'min-width': 200,
    },
    {
      label: Vue.prototype.$translate('microServeTab.instanceId'),
      prop: 'instanceId',
      key: 'cplusInstanceId',
      'min-width': 100,
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectPort'),
      prop: 'projectPort',
      key: 'cplusProjectPort',
      'min-width': lang === 'zh-CN' ? 120 : 150,
    },
    {
      label: Vue.prototype.$translate('microServeTab.available'),
      prop: 'available',
      key: 'cplusAvailable',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 150,
    },
    {
      label: Vue.prototype.$translate('microServeTab.cpuRate'),
      prop: 'cpuRate',
      key: 'cplusCpuRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.memory'),
      prop: 'memory',
      key: 'cplusMemory',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.memoryRate'),
      prop: 'memoryRate',
      key: 'cplusMemoryRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 210,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.restarts'),
      prop: 'restarts',
      key: 'cplusRestarts',
      slotName: 'bodyCell',
      'min-width': 110,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.dmpfiles'),
      prop: 'dmpfiles',
      key: 'cplusDmpfiles',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 150 : 210,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.pid'),
      prop: 'pid',
      key: 'cplusPid',
      'min-width': 120,
    },
  ],
  'monitor.microservice.web': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      key: 'webIp',
      'min-width': 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      key: 'webHost',
      'min-width': 160,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      key: 'webSt',
      slotName: 'bodyCell',
      'min-width': 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      key: 'webProjectName',
      'min-width': 240,
    },
    {
      label: Vue.prototype.$translate('microServeTab.instanceId'),
      prop: 'instanceId',
      key: 'webInstanceId',
      'min-width': 120,
    },
    {
      label: Vue.prototype.$translate('microServeTab.available'),
      prop: 'available',
      key: 'webAvailable',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 150,
    },
  ],
  'monitor.mysql.instance': [
    // {
    //   label: Vue.prototype.$translate('resourceTab.ip'),
    //   prop: 'ip',
    //   'min-width': lang === 'zh-CN' ? 140 : 110,
    // },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 140 : 110,
    },
    {
      label: Vue.prototype.$translate('mySqlTab.mysqlType'),
      prop: 'mysqlType',
      'min-width': lang === 'zh-CN' ? 100 : 100,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 150 : 120,
      sortable: 'custom',
    },
    // {
    //   label: Vue.prototype.$translate('mySqlTab.dbName'),
    //   prop: 'dbName',
    //   'min-width': lang === 'zh-CN' ? 140 : 140,
    // },
    // {
    //   label: Vue.prototype.$translate('mySqlTab.dbSpaces'),
    //   prop: 'dbSpaces',
    //   slotName: 'bodyCell',
    //   'min-width': lang === 'zh-CN' ? 120 : 175,
    //   sortable: 'custom',
    // },
    // {
    //   label: Vue.prototype.$translate('mySqlTab.dbTables'),
    //   prop: 'dbTables',
    //   'min-width': lang === 'zh-CN' ? 90 : 65,
    // },
    {
      label: Vue.prototype.$translate('mySqlTab.available'),
      prop: 'available',
      key: 'mysqlAvailable',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 100 : 70,
    },
    {
      label: Vue.prototype.$translate('mySqlTab.maxConnections'),
      prop: 'maxConnections',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 170 : 130,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.dbConnections'),
      prop: 'connections',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 110 : 130,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.availSpace'),
      prop: 'availSpace',
      key: 'availSpace',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 130,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.spaceRate'),
      prop: 'spaceRate',
      key: 'spaceRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 110,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.dbSpaces'),
      prop: 'spaces',
      key: 'mysqlSpace',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 140,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.totalSpace'),
      prop: 'totalSpace',
      key: 'mysqlTotalSpace',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 110 : 110,
      sortable: 'custom',
    },
  ],
  'monitor.mysql.db': [
    // {
    //   label: Vue.prototype.$translate('resourceTab.ip'),
    //   prop: 'ip',
    //   'min-width': lang === 'zh-CN' ? 140 : 120,
    // },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 140 : 100,
    },
    {
      label: Vue.prototype.$translate('mySqlTab.mysqlType'),
      prop: 'mysqlType',
      'min-width': 120,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 150 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.dbName'),
      prop: 'dbName',
      'min-width': 140,
    },
    {
      label: Vue.prototype.$translate('mySqlTab.dbSpaces'),
      prop: 'dbSpaces',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 175,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.dbTables'),
      prop: 'dbTables',
      'min-width': lang === 'zh-CN' ? 70 : 65,
    },
    {
      label: Vue.prototype.$translate('mySqlTab.available'),
      prop: 'available',
      key: 'mysqlAvailable',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 110 : 90,
    },
    {
      label: Vue.prototype.$translate('mySqlTab.maxConnections'),
      prop: 'maxConnections',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 165,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.dbConnections'),
      prop: 'dbConnections',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.dbAvailConnections'),
      prop: 'dbAvailConnections',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 195,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.dbDeadlocks'),
      prop: 'dbDeadlocks',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 100 : 120,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.dbSlows'),
      prop: 'dbSlows',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 130,
      sortable: 'custom',
    },
    // {
    //   label: Vue.prototype.$translate('microServeTab.restarts'),
    //   prop: 'restarts',
    //   slotName: 'bodyCell',
    //   'min-width': lang === 'zh-CN' ? 110 : 105,
    //   sortable: 'custom',
    // },
  ],
  'monitor.redis': [
    // {
    //   label: Vue.prototype.$translate('resourceTab.ip'),
    //   prop: 'ip',
    //   'min-width': lang === 'zh-CN' ? 130 : 120,
    // },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 130 : 120,
    },
    {
      label: Vue.prototype.$translate('redisTab.redisType'),
      prop: 'redisType',
      'min-width': lang === 'zh-CN' ? 130 : 110,
    },
    {
      label: Vue.prototype.$translate('natTab.instanceId'),
      prop: 'instanceId',
      'min-width': lang === 'zh-CN' ? 70 : 100,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 140,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('redisTab.available'),
      prop: 'available',
      key: 'redisAvailable',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 90,
    },
    {
      label: Vue.prototype.$translate('redisTab.memory'),
      prop: 'memory',
      slotName: 'bodyCell',
      key: 'redisMemory',
      'min-width': lang === 'zh-CN' ? 140 : 150,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('redisTab.memoryRate'),
      prop: 'memoryRate',
      key: 'redisMemoryRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('redisTab.connections'),
      prop: 'connections',
      slotName: 'bodyCell',
      key: 'redisConnections',
      'min-width': lang === 'zh-CN' ? 140 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('redisTab.blockedClients'),
      prop: 'blockedClients',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('redisTab.rejectedConnections'),
      prop: 'rejectedConnections',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.restarts'),
      prop: 'restarts',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 110 : 100,
      sortable: 'custom',
    },
    // {
    //   label: Vue.prototype.$translate('redisTab.clusterDowns'),
    //   prop: 'clusterDowns',
    //   slotName: 'bodyCell',
    //   'min-width': lang === 'zh-CN' ? 220 : 170,
    //   sortable: 'custom',
    // },
  ],
  'monitor.kafka.node-kafkaNode': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': 160,
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      width: lang === 'zh-CN' ? 100 : 120,
    },
    {
      label: Vue.prototype.$translate('microServeTab.instanceId'),
      prop: 'instanceId',
      width: lang === 'zh-CN' ? 100 : 100,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('resourceTab.pid'),
      prop: 'pid',
      'min-width': 140,
    },
    {
      label: Vue.prototype.$translate('microServeTab.startXmx'),
      prop: 'startXmx',
      'min-width': lang === 'zh-CN' ? 160 : 120,
    },
    {
      label: Vue.prototype.$translate('kafkaTab.memory'),
      prop: 'memory',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('kafkaTab.cpuRate'),
      prop: 'cpuRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 200,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('kafkaTab.memoryRate'),
      prop: 'memoryRate',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 200,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.restarts'),
      prop: 'restarts',
      slotName: 'bodyCell',
      'min-width': 110,
      sortable: 'custom',
    },
  ],
  'monitor.kafka.cluster-kafkaCluster': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': 160,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': 200,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('kafkaTab.available'),
      prop: 'available',
      key: 'kafkaAvailable',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 170,
    },
    {
      label: Vue.prototype.$translate('kafkaTab.lags'),
      prop: 'lags',
      slotName: 'bodyCell',
      'min-width': 200,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('kafkaTab.clusterDowns'),
      prop: 'clusterDowns',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 230,
      sortable: 'custom',
    },
    { label: 'Topic', prop: 'topic', 'min-width': 200, sortable: 'custom' },
  ],
  'monitor.mongo': [
    // {
    //   label: Vue.prototype.$translate('resourceTab.ip'),
    //   prop: 'ip',
    //   'min-width': lang === 'zh-CN' ? 140 : 80,
    // },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 140 : 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mongoTab.available'),
      prop: 'available',
      key: 'mongoAvailable',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 90,
    },
    {
      label: Vue.prototype.$translate('mongoTab.spaces'),
      prop: 'spaces',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.spaceRate'),
      prop: 'spaceRate',
      slotName: 'bodyCell',
      key: 'mongoSpaceRate',
      'min-width': lang === 'zh-CN' ? 150 : 140,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mySqlTab.totalSpace'),
      prop: 'totalSpace',
      slotName: 'bodyCell',
      key: 'mongoTotalSpace',
      'min-width': lang === 'zh-CN' ? 110 : 150,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mongoTab.connections'),
      prop: 'connections',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 130 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mongoTab.availConnections'),
      prop: 'availConnections',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 150 : 190,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mongoTab.memory'),
      prop: 'memory',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mongoTab.requests'),
      prop: 'requests',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 150 : 200,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('mongoTab.slows'),
      prop: 'slows',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 130,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.restarts'),
      prop: 'restarts',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 110 : 105,
      sortable: 'custom',
    },
  ],
  'monitor.nginx': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': lang === 'zh-CN' ? 140 : 120,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 160 : 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      'min-width': lang === 'zh-CN' ? 200 : 140,
    },
    {
      label: Vue.prototype.$translate('nginxTab.available'),
      prop: 'available',
      key: 'nginxAvailable',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 100,
    },
    {
      label: Vue.prototype.$translate('nginxTab.connections'),
      prop: 'connections',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('nginxTab.availConnections'),
      prop: 'availConnections',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 150 : 270,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('nginxTab.totalRequests'),
      prop: 'totalRequests',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('nginxTab.incrRequests'),
      prop: 'incrRequests',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 200,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.restarts'),
      prop: 'restarts',
      slotName: 'bodyCell',
      'min-width': 110,
      sortable: 'custom',
    },
  ],
  'monitor.oss': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': lang === 'zh-CN' ? 140 : 120,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 160 : 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('ossTab.bucketName'),
      prop: 'bucketName',
      'min-width': lang === 'zh-CN' ? 200 : 210,
    },
    {
      label: Vue.prototype.$translate('ossTab.bucketType'),
      prop: 'bucketType',
      'min-width': lang === 'zh-CN' ? 160 : 110,
    },
    {
      label: Vue.prototype.$translate('ossTab.businessType'),
      prop: 'businessType',
      'min-width': lang === 'zh-CN' ? 160 : 120,
    },
    {
      label: Vue.prototype.$translate('ossTab.available'),
      prop: 'available',
      key: 'ossAvailable',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 140,
    },
    {
      label: Vue.prototype.$translate('ossTab.storages'),
      prop: 'storages',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 280,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('ossTab.objects'),
      prop: 'objects',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 330,
      sortable: 'custom',
    },
  ],
  'monitor.queue': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': 120,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': 130,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': 140,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      'min-width': 200,
    },
    {
      label: Vue.prototype.$translate('natTab.instanceId'),
      prop: 'instanceId',
      'min-width': 100,
    },
    {
      label: Vue.prototype.$translate('queueTab.queueName'),
      prop: 'queueName',
      'min-width': 280,
    },
    {
      label: Vue.prototype.$translate('queueTab.queueType'),
      prop: 'queueType',
      'min-width': 140,
    },
    {
      label: Vue.prototype.$translate('queueTab.maxSize'),
      prop: 'maxSize',
      'min-width': 120,
    },
    {
      label: Vue.prototype.$translate('queueTab.usedSize'),
      prop: 'usedSize',
      'min-width': 120,
      slotName: 'bodyCell',
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('queueTab.usedRate'),
      prop: 'usedRate',
      'min-width': 120,
      slotName: 'bodyCell',
      sortable: 'custom',
    },
  ],
  'monitor.apache': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': lang === 'zh-CN' ? 140 : 120,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 160 : 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      'min-width': lang === 'zh-CN' ? 200 : 140,
    },
    {
      label: Vue.prototype.$translate('natTab.instanceId'),
      prop: 'instanceId',
      'min-width': 100,
    },
    {
      label: Vue.prototype.$translate('apacheTab.available'),
      prop: 'available',
      key: 'apacheAvailable',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 120 : 140,
    },
    {
      label: Vue.prototype.$translate('apacheTab.busyWorkers'),
      prop: 'busyWorkers',
      slotName: 'bodyCell',
      'min-width': 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('apacheTab.idleWorkers'),
      prop: 'idleWorkers',
      slotName: 'bodyCell',
      'min-width': 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('apacheTab.incrRequests'),
      prop: 'incrRequests',
      key: 'apacheIncrRequests',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.restarts'),
      prop: 'restarts',
      slotName: 'bodyCell',
      'min-width': 120,
      sortable: 'custom',
    },
  ],
  'monitor.wsConnect': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': lang === 'zh-CN' ? 140 : 120,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 160 : 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      'min-width': lang === 'zh-CN' ? 160 : 140,
    },
    {
      label: Vue.prototype.$translate('natTab.instanceId'),
      prop: 'instanceId',
      'min-width': 100,
    },
    {
      label: Vue.prototype.$translate('devopsStatisticsTab.clientType'),
      prop: 'clientType',
      'min-width': 120,
    },
    {
      label: 'AppId',
      prop: 'appId',
      'min-width': 200,
    },
    {
      label: Vue.prototype.$translate('wsConnectTab.connectNum'),
      prop: 'connectNum',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 200,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('wsConnectTab.totalConnectNum'),
      prop: 'totalConnectNum',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 200,
      sortable: 'custom',
    },
  ],
}

export const appData = {
  微服务: [],
  Mysql: [],
  Redis: [],
  Kafka: [],
  Mongo: [],
  Nginx: [],
  'OSS/S3': [],
}

export const websocketColumns = [
  {
    label: Vue.prototype.$translate('resourceTab.ip'),
    prop: 'ip',
    'min-width': lang === 'zh-CN' ? 140 : 120,
  },
  {
    label: Vue.prototype.$translate('resourceTab.host'),
    prop: 'host',
    'min-width': lang === 'zh-CN' ? 160 : 140,
  },
  {
    label: Vue.prototype.$translate('resourceTab.ct'),
    prop: 'st',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 200 : 160,
    sortable: 'custom',
  },
  // { label: Vue.prototype.$translate('resourceTab.pid'), prop: 'pid', 'min-width': 140 },
  {
    label: Vue.prototype.$translate('websocketTab.websocketType'),
    prop: 'websocketType',
    slotName: 'bodyCell',
    'min-width': 190,
  },
  {
    label: Vue.prototype.$translate('websocketTab.serverInstance'),
    prop: 'serverInstance',
    'min-width': 170,
  },
  {
    label: Vue.prototype.$translate('websocketTab.clientInstance'),
    prop: 'clientInstance',
    'min-width': 170,
  },
  // {
  //   label: Vue.prototype.$translate('websocketTab.p2pInstanceId'),
  //   prop: 'p2pInstanceId',
  //   'min-width': lang === 'zh-CN' ? 160 : 140,
  // },
  // { label: Vue.prototype.$translate('websocketTab.heartbeat'), prop: 'heartbeat', slotName: 'bodyCell', 'min-width': 200 },
  {
    label: Vue.prototype.$translate('websocketTab.disconnects'),
    prop: 'disconnects',
    slotName: 'bodyCell',
    'min-width': 200,
    sortable: 'custom',
  },
]

export const devopsWebsocketColumns = [
  {
    label: Vue.prototype.$translate('resourceTab.ip'),
    prop: 'ip',
    'min-width': lang === 'zh-CN' ? 140 : 120,
  },
  {
    label: Vue.prototype.$translate('resourceTab.host'),
    prop: 'host',
    'min-width': lang === 'zh-CN' ? 160 : 140,
  },
  {
    label: Vue.prototype.$translate('resourceTab.ct'),
    prop: 'st',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 200 : 160,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('websocketTab.websocketType'),
    prop: 'websocketType',
    slotName: 'bodyCell',
    'min-width': 140,
  },
  {
    label: Vue.prototype.$translate('websocketTab.agentHost'),
    prop: 'agentHost',
    'min-width': 180,
  },
  {
    label: Vue.prototype.$translate('websocketTab.disconnects'),
    prop: 'disconnects',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 200 : 300,
    sortable: 'custom',
  },
]

export const dpikeyColumns = [
  {
    label: Vue.prototype.$translate('resourceTab.ip'),
    prop: 'ip',
    'min-width': 140,
  },
  {
    label: Vue.prototype.$translate('resourceTab.host'),
    prop: 'host',
    'min-width': lang === 'zh-CN' ? 160 : 150,
  },
  {
    label: Vue.prototype.$translate('resourceTab.ct'),
    prop: 'st',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 200 : 180,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('microServeTab.projectName'),
    prop: 'projectName',
    'min-width': lang === 'zh-CN' ? 200 : 160,
  },
  {
    label: Vue.prototype.$translate('natTab.serviceInstanceId'),
    prop: 'instanceId',
    key: 'dpiInstanceId',
    'min-width': lang === 'zh-CN' ? 120 : 160,
  },
  {
    label: Vue.prototype.$translate('natTab.syncErrors'),
    prop: 'syncErrors',
    key: 'syncErrors',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 150 : 160,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('natTab.cplusGetErrors'),
    prop: 'cplusGetErrors',
    key: 'cplusGetErrors',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 150 : 130,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('natTab.javaGetErrors'),
    prop: 'javaGetErrors',
    key: 'javaGetErrors',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 150 : 150,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('dpikeyTab.errors'),
    prop: 'errors',
    key: 'dpiErrors',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 220 : 260,
    sortable: 'custom',
  },
]

export const dpikeyData = []

export const commonFormOptions = (areaOptions) => [
  {
    label: '',
    prop: 'dcId',
    element: 'tvt-select',
    options: areaOptions,
    size: 'small',
  },
  {
    label: '',
    prop: 'dateTime',
    type: 'datetimerange',
    element: 'el-date-picker',
    size: 'small',
  },
]

export const emailColumns = [
  {
    label: Vue.prototype.$translate('resourceTab.ip'),
    prop: 'ip',
    'min-width': lang === 'zh-CN' ? 140 : 90,
  },
  {
    label: Vue.prototype.$translate('resourceTab.host'),
    prop: 'host',
    'min-width': lang === 'zh-CN' ? 160 : 130,
  },
  {
    label: Vue.prototype.$translate('resourceTab.ct'),
    prop: 'st',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 200 : 150,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('accountTab.accountType'),
    prop: 'accountType',
    'min-width': lang === 'zh-CN' ? 110 : 120,
  },
  {
    label: Vue.prototype.$translate('emailTab.nums'),
    prop: 'nums',
    key: 'emailNums',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 200 : 270,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('emailTab.errors'),
    prop: 'errors',
    key: 'emailErrors',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 200 : 240,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('emailTab.limits'),
    prop: 'limits',
    key: 'emailLimits',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 230 : 280,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('emailTab.summary'),
    prop: 'summary',
    key: 'summary',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 230 : 180,
    sortable: 'custom',
  },
]

export const emailData = []

export const textMessColumns = [
  {
    label: Vue.prototype.$translate('resourceTab.ip'),
    prop: 'ip',
    'min-width': 140,
  },
  {
    label: Vue.prototype.$translate('resourceTab.host'),
    prop: 'host',
    'min-width': 160,
  },
  {
    label: Vue.prototype.$translate('resourceTab.ct'),
    prop: 'st',
    slotName: 'bodyCell',
    'min-width': 200,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('accountTab.accountType'),
    prop: 'accountType',
    'min-width': lang === 'zh-CN' ? 110 : 120,
  },
  {
    label: Vue.prototype.$translate('textMessageTab.nums'),
    prop: 'nums',
    key: 'smsNums',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 260 : 280,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('textMessageTab.errors'),
    prop: 'errors',
    key: 'smsErrors',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 300 : 280,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('textMessageTab.limits'),
    prop: 'limits',
    key: 'smsLimits',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 270 : 280,
    sortable: 'custom',
  },
]

export const textMessData = []

export const accountColumns = [
  {
    label: Vue.prototype.$translate('resourceTab.ip'),
    prop: 'ip',
    'min-width': lang === 'zh-CN' ? 140 : 100,
  },
  {
    label: Vue.prototype.$translate('resourceTab.host'),
    prop: 'host',
    'min-width': lang === 'zh-CN' ? 160 : 120,
  },
  {
    label: Vue.prototype.$translate('resourceTab.ct'),
    prop: 'st',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 180 : 160,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('accountTab.accountType'),
    prop: 'accountType',
    'min-width': lang === 'zh-CN' ? 110 : 120,
  },
  {
    label: Vue.prototype.$translate('accountTab.registerErrors'),
    prop: 'registerErrors',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 190 : 280,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('accountTab.loginErrors'),
    prop: 'loginErrors',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 190 : 260,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('accountTab.logins'),
    prop: 'logins',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 140 : 130,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('accountTab.registers'),
    prop: 'registers',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 140 : 200,
    sortable: 'custom',
  },
]

export const accountData = []

export const pushColumns = [
  {
    label: Vue.prototype.$translate('resourceTab.ip'),
    prop: 'ip',
    'min-width': 100,
  },
  {
    label: Vue.prototype.$translate('resourceTab.host'),
    prop: 'host',
    'min-width': lang === 'zh-CN' ? 160 : 130,
  },
  {
    label: Vue.prototype.$translate('resourceTab.ct'),
    prop: 'st',
    slotName: 'bodyCell',
    'min-width': 160,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('accountTab.accountType'),
    prop: 'accountType',
    'min-width': lang === 'zh-CN' ? 110 : 120,
  },
  {
    label: Vue.prototype.$translate('pushTab.pushs'),
    prop: 'pushs',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 130 : 140,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('pushTab.onlinePushs'),
    prop: 'onlinePushs',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 145 : 160,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('pushTab.onlineErrors'),
    prop: 'onlineErrors',
    slotName: 'bodyCell',
    'min-width': 150,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('pushTab.offlinePushs'),
    prop: 'offlinePushs',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 150 : 160,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('pushTab.offlineErrors'),
    prop: 'offlineErrors',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 150 : 150,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('pushTab.offlineLimits'),
    prop: 'offlineLimits',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 180 : 240,
    sortable: 'custom',
  },
  {
    label: Vue.prototype.$translate('pushTab.apnsPushs'),
    prop: 'apnsPushs',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 150 : 160,
    sortable: 'custom',
    initHide: true, // 在表格中默认隐藏列，对应的列设置也不勾选
  },
  {
    label: Vue.prototype.$translate('pushTab.apnsErrors'),
    prop: 'apnsErrors',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 150 : 150,
    sortable: 'custom',
    initHide: true,
  },
  {
    label: Vue.prototype.$translate('pushTab.fcmPushs'),
    prop: 'fcmPushs',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 150 : 160,
    sortable: 'custom',
    initHide: true,
  },
  {
    label: Vue.prototype.$translate('pushTab.fcmErrors'),
    prop: 'fcmErrors',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 150 : 150,
    sortable: 'custom',
    initHide: true,
  },
  {
    label: Vue.prototype.$translate('pushTab.hmsPushs'),
    prop: 'hmsPushs',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 150 : 160,
    sortable: 'custom',
    initHide: true,
  },
  {
    label: Vue.prototype.$translate('pushTab.hmsErrors'),
    prop: 'hmsErrors',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 150 : 150,
    sortable: 'custom',
    initHide: true,
  },
  {
    label: Vue.prototype.$translate('pushTab.linePushs'),
    prop: 'linePushs',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 150 : 160,
    sortable: 'custom',
    initHide: true,
  },
  {
    label: Vue.prototype.$translate('pushTab.lineErrors'),
    prop: 'lineErrors',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 150 : 150,
    sortable: 'custom',
    initHide: true,
  },
  {
    label: Vue.prototype.$translate('pushTab.errors'),
    prop: 'errors',
    slotName: 'bodyCell',
    'min-width': lang === 'zh-CN' ? 140 : 140,
    sortable: 'custom',
  },
]

export const pushData = []

export const p2pColumns = {
  'monitor.nat': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': lang === 'zh-CN' ? 100 : 90,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 140 : 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.instanceId'),
      prop: 'instanceId',
      'min-width': lang === 'zh-CN' ? 70 : 100,
    },
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 100 : 120,
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      key: 'natProjectName',
      'min-width': 150,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online'),
      prop: 'natDevOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 150,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_reg'),
      prop: 'natDevReg',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 190,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_online'),
      prop: 'natClientOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 160,
      sortable: 'custom',
      initHide: true, // 在表格中默认隐藏列，对应的列设置也不勾选
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_reg'),
      prop: 'natClientReg',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 180,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_sn_online'),
      prop: 'natCleintP2pReqSnOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 230 : 400,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_token_online'),
      prop: 'natCleintP2pReqTokenOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 270 : 450,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_offline'),
      prop: 'natClientOffline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 200,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat'),
      prop: 'natDevBigHeat',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_offline'),
      prop: 'natDevOffline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat'),
      prop: 'natDevSmallHeat',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip'),
      prop: 'natDevUpdateIp',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 220 : 300,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
      prop: 'natDevBigHeatV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 370,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
      prop: 'natDevSmallHeatV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 320,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
      prop: 'natDevUpdateIpV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 215 : 360,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online_dec'),
      prop: 'natDevOnlineDec',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 175 : 160,
      sortable: 'custom',
    },
  ],
  'monitor.nat1': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': lang === 'zh-CN' ? 100 : 110,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 140 : 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.instanceId'),
      prop: 'instanceId',
      'min-width': lang === 'zh-CN' ? 70 : 100,
    },
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      slotName: 'bodyCell',
      'min-width': 120,
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      key: 'nat1ProjectName',
      'min-width': 150,
    },
    {
      label: Vue.prototype.$translate('natTab.maxDevNum'),
      prop: 'maxDevNum',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 130 : 150,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.devNum'),
      prop: 'devNum',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 130 : 150,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.connectionNum'),
      prop: 'connectionNum',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 190,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.clientNum'),
      prop: 'clientNum',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 140,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.p2pReqNum'),
      prop: 'p2pReqNum',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 140,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.relayReqNum'),
      prop: 'relayReqNum',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 210,
      sortable: 'custom',
    },
  ],
  'monitor.udt': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': lang === 'zh-CN' ? 140 : 100,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 160 : 120,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 150,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.instanceId'),
      prop: 'instanceId',
      'min-width': lang === 'zh-CN' ? 120 : 100,
    },
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 100 : 120,
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      key: 'udtProjectName',
      'min-width': 150,
    },
    {
      label: Vue.prototype.$translate('redirectTab.redirect_client'),
      prop: 'redirectClient',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 150,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('redirectTab.redirect_client_full_list'),
      prop: 'redirectClientFullList',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 270 : 390,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('redirectTab.redirect_dev'),
      prop: 'redirectDev',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 190,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('redirectTab.redirect_dev_inc'),
      prop: 'redirectDevInc',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 240,
      sortable: 'custom',
    },
  ],
  'monitor.relay': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': 160,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.instanceId'),
      prop: 'instanceId',
      'min-width': 120,
    },
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      slotName: 'bodyCell',
      'min-width': 130,
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      key: 'relayProjectName',
      'min-width': 150,
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_client_req'),
      prop: 'relayClientReq',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_dev_req'),
      prop: 'relayDevReq',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session'),
      prop: 'relaySession',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 140,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session_end'),
      prop: 'relaySessionEnd',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_speed_limit_count'),
      prop: 'relaySpeedLimitCount',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 220 : 240,
      sortable: 'custom',
    },
  ],
  'monitor.relay1': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': lang === 'zh-CN' ? 100 : 100,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 140 : 120,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 140,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.instanceId'),
      prop: 'instanceId',
      'min-width': 120,
    },
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      slotName: 'bodyCell',
      'min-width': 130,
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      key: 'relay1ProjectName',
      'min-width': 150,
    },
    {
      label: Vue.prototype.$translate('relayTab.maxSession'),
      prop: 'maxSession',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 200,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.currentSession'),
      prop: 'currentSession',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 220,
      sortable: 'custom',
    },
  ],
  'monitor.stun': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': lang === 'zh-CN' ? 100 : 90,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 140 : 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.instanceId'),
      prop: 'instanceId',
      'min-width': lang === 'zh-CN' ? 70 : 100,
    },
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 100 : 120,
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      key: 'stunProjectName',
      'min-width': 150,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online'),
      prop: 'natDevOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 150,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_reg'),
      prop: 'natDevReg',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 190,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_online'),
      prop: 'natClientOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 160,
      sortable: 'custom',
      initHide: true, // 在表格中默认隐藏列，对应的列设置也不勾选
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_reg'),
      prop: 'natClientReg',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 180,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_sn_online'),
      prop: 'natCleintP2pReqSnOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 230 : 400,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_token_online'),
      prop: 'natCleintP2pReqTokenOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 270 : 450,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_offline'),
      prop: 'natClientOffline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 200,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat'),
      prop: 'natDevBigHeat',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_offline'),
      prop: 'natDevOffline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat'),
      prop: 'natDevSmallHeat',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip'),
      prop: 'natDevUpdateIp',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 300,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
      prop: 'natDevBigHeatV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 370,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
      prop: 'natDevSmallHeatV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 320,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
      prop: 'natDevUpdateIpV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 215 : 360,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online_dec'),
      prop: 'natDevOnlineDec',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 175 : 160,
      sortable: 'custom',
    },
  ],
  'monitor.access': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': lang === 'zh-CN' ? 100 : 90,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': lang === 'zh-CN' ? 140 : 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.instanceId'),
      prop: 'instanceId',
      'min-width': lang === 'zh-CN' ? 70 : 100,
    },
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 100 : 120,
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      key: 'accessProjectName',
      'min-width': 150,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online'),
      prop: 'natDevOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 150,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_reg'),
      prop: 'natDevReg',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 190,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat'),
      prop: 'natDevBigHeat',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_offline'),
      prop: 'natDevOffline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat'),
      prop: 'natDevSmallHeat',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip'),
      prop: 'natDevUpdateIp',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 300,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
      prop: 'natDevBigHeatV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 370,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
      prop: 'natDevSmallHeatV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 320,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
      prop: 'natDevUpdateIpV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 215 : 360,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online_dec'),
      prop: 'natDevOnlineDec',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 175 : 160,
      sortable: 'custom',
    },
  ],
  'monitor.stunRelay': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': 160,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.instanceId'),
      prop: 'instanceId',
      'min-width': 120,
    },
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      slotName: 'bodyCell',
      'min-width': 130,
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      key: 'stunRelayProjectName',
      'min-width': 150,
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_client_req'),
      prop: 'relayClientReq',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_dev_req'),
      prop: 'relayDevReq',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session'),
      prop: 'relaySession',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 140,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session_end'),
      prop: 'relaySessionEnd',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_speed_limit_count'),
      prop: 'relaySpeedLimitCount',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 220 : 240,
      sortable: 'custom',
    },
  ],
  'monitor.nat.cluster': [
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      key: 'natClusterName',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 100 : 120,
    },
    {
      label: Vue.prototype.$translate('natTab.clusterCode'),
      prop: 'clusterCode',
      key: 'natClusterCode',
      'min-width': 120,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      key: 'natClusterSt',
      slotName: 'bodyCell',
      'min-width': 160,
      sortable: 'custom',
    },

    // { label: Vue.prototype.$translate('natTab.nodeName'), prop: 'nodeName', 'min-width': 140 },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online'),
      prop: 'natDevOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 150,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_reg'),
      prop: 'natDevReg',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 190,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_online'),
      prop: 'natClientOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 160,
      sortable: 'custom',
      initHide: true, // 在表格中默认隐藏列，对应的列设置也不勾选
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_reg'),
      prop: 'natClientReg',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 180,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_sn_online'),
      prop: 'natCleintP2pReqSnOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 230 : 400,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_token_online'),
      prop: 'natCleintP2pReqTokenOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 270 : 450,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_offline'),
      prop: 'natClientOffline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 200,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat'),
      prop: 'natDevBigHeat',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_offline'),
      prop: 'natDevOffline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat'),
      prop: 'natDevSmallHeat',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip'),
      prop: 'natDevUpdateIp',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 220 : 300,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
      prop: 'natDevBigHeatV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 370,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
      prop: 'natDevSmallHeatV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 320,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
      prop: 'natDevUpdateIpV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 215 : 360,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online_dec'),
      prop: 'natDevOnlineDec',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 175 : 160,
      sortable: 'custom',
    },
  ],
  'monitor.relay.cluster': [
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      key: 'relayClusterName',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 100 : 120,
    },
    {
      label: Vue.prototype.$translate('natTab.clusterCode'),
      prop: 'clusterCode',
      key: 'relayClusterCode',
      'min-width': 120,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      key: 'relayClusterSt',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 180,
      sortable: 'custom',
    },
    // { label: Vue.prototype.$translate('natTab.nodeName'), prop: 'nodeName', 'min-width': 140 },
    {
      label: Vue.prototype.$translate('relayTab.relay_client_req'),
      prop: 'relayClientReq',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_dev_req'),
      prop: 'relayDevReq',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session'),
      prop: 'relaySession',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 140,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session_end'),
      prop: 'relaySessionEnd',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_speed_limit_count'),
      prop: 'relaySpeedLimitCount',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 220 : 240,
      sortable: 'custom',
    },
  ],
  'monitor.stun.cluster': [
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      key: 'stunClusterName',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 100 : 120,
    },
    {
      label: Vue.prototype.$translate('natTab.clusterCode'),
      prop: 'clusterCode',
      key: 'stunClusterCode',
      'min-width': 120,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      key: 'stunClusterSt',
      slotName: 'bodyCell',
      'min-width': 160,
      sortable: 'custom',
    },
    // { label: Vue.prototype.$translate('natTab.nodeName'), prop: 'nodeName', 'min-width': 140 },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online'),
      prop: 'natDevOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 150,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_reg'),
      prop: 'natDevReg',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 190,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_online'),
      prop: 'natClientOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 160,
      sortable: 'custom',
      initHide: true, // 在表格中默认隐藏列，对应的列设置也不勾选
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_reg'),
      prop: 'natClientReg',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 180,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_sn_online'),
      prop: 'natCleintP2pReqSnOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 230 : 400,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_cleint_p2p_req_token_online'),
      prop: 'natCleintP2pReqTokenOnline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 270 : 450,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_client_offline'),
      prop: 'natClientOffline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 200,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat'),
      prop: 'natDevBigHeat',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_offline'),
      prop: 'natDevOffline',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 145 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat'),
      prop: 'natDevSmallHeat',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 170,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip'),
      prop: 'natDevUpdateIp',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 300,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_big_heat_v6'),
      prop: 'natDevBigHeatV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 370,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_small_heat_v6'),
      prop: 'natDevSmallHeatV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 200 : 320,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_update_ip_v6'),
      prop: 'natDevUpdateIpV6',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 215 : 360,
      sortable: 'custom',
      initHide: true,
    },
    {
      label: Vue.prototype.$translate('natTab.nat_dev_online_dec'),
      prop: 'natDevOnlineDec',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 175 : 160,
      sortable: 'custom',
    },
  ],
  'monitor.stun.relay.cluster': [
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      key: 'stunRelayClusterName',
      slotName: 'bodyCell',
      'min-width': 130,
    },
    {
      label: Vue.prototype.$translate('natTab.clusterCode'),
      prop: 'clusterCode',
      key: 'stunRelayClusterCode',
      'min-width': 120,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      key: 'stunRelayClusterSt',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 180,
      sortable: 'custom',
    },
    // { label: Vue.prototype.$translate('natTab.nodeName'), prop: 'nodeName', 'min-width': 140 },
    {
      label: Vue.prototype.$translate('relayTab.relay_client_req'),
      prop: 'relayClientReq',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_dev_req'),
      prop: 'relayDevReq',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 160,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session'),
      prop: 'relaySession',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 140 : 140,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_session_end'),
      prop: 'relaySessionEnd',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 180 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('relayTab.relay_speed_limit_count'),
      prop: 'relaySpeedLimitCount',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 220 : 240,
      sortable: 'custom',
    },
  ],
  'monitor.push1': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': 160,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.instanceId'),
      prop: 'instanceId',
      key: 'push1InstanceId',
      'min-width': lang === 'zh-CN' ? 70 : 100,
    },
    {
      label: Vue.prototype.$translate('push1Tab.customer'),
      prop: 'customer',
      key: 'push1customer',
      'min-width': 100,
    },
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 130 : 160,
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      key: 'push1ProjectName',
      'min-width': 150,
    },
    {
      label: Vue.prototype.$translate('push1Tab.tpnsMsgRecv'),
      prop: 'tpnsMsgRecv',
      'min-width': lang === 'zh-CN' ? 160 : 300,
    },
    {
      label: Vue.prototype.$translate('push1Tab.tpnsDispMsg'),
      prop: 'tpnsDispMsg',
      'min-width': lang === 'zh-CN' ? 160 : 310,
    },
    {
      label: Vue.prototype.$translate('push1Tab.tpnsDevTotal'),
      prop: 'tpnsDevTotal',
      'min-width': lang === 'zh-CN' ? 150 : 190,
    },
    {
      label: Vue.prototype.$translate('push1Tab.tpnsEnableDev'),
      prop: 'tpnsEnableDev',
      'min-width': lang === 'zh-CN' ? 160 : 180,
    },
    {
      label: Vue.prototype.$translate('push1Tab.tpnsPendingJobCount'),
      prop: 'tpnsPendingJobCount',
      'min-width': lang === 'zh-CN' ? 160 : 220,
    },
    {
      label: Vue.prototype.$translate('push1Tab.amdsPushReq'),
      prop: 'amdsPushReq',
      'min-width': lang === 'zh-CN' ? 200 : 300,
    },
    {
      label: Vue.prototype.$translate('push1Tab.amdsPushMsg'),
      prop: 'amdsPushMsg',
      'min-width': lang === 'zh-CN' ? 150 : 180,
    },
    {
      label: Vue.prototype.$translate('push1Tab.amdsExpCertCount'),
      prop: 'amdsExpCertCount',
      'min-width': lang === 'zh-CN' ? 180 : 220,
    },
    {
      label: Vue.prototype.$translate('push1Tab.gmdsPushReq'),
      prop: 'gmdsPushReq',
      'min-width': lang === 'zh-CN' ? 200 : 300,
    },
    {
      label: Vue.prototype.$translate('push1Tab.gmdsDispMsg'),
      prop: 'gmdsDispMsg',
      'min-width': lang === 'zh-CN' ? 150 : 220,
    },
    {
      label: Vue.prototype.$translate('push1Tab.gpnsPushReq'),
      prop: 'gpnsPushReq',
      'min-width': lang === 'zh-CN' ? 200 : 280,
    },
    {
      label: Vue.prototype.$translate('push1Tab.gpnsPushMsg'),
      prop: 'gpnsPushMsg',
      'min-width': lang === 'zh-CN' ? 150 : 180,
    },
    {
      label: Vue.prototype.$translate('push1Tab.gpnsClientNum'),
      prop: 'gpnsClientNum',
      'min-width': lang === 'zh-CN' ? 150 : 180,
    },
    {
      label: Vue.prototype.$translate('push1Tab.gpnsClientRegNum'),
      prop: 'gpnsClientRegNum',
      'min-width': lang === 'zh-CN' ? 150 : 220,
    },
  ],
  'monitor.push1.sdns': [
    {
      label: Vue.prototype.$translate('resourceTab.ip'),
      prop: 'ip',
      'min-width': 140,
    },
    {
      label: Vue.prototype.$translate('resourceTab.host'),
      prop: 'host',
      'min-width': 160,
    },
    {
      label: Vue.prototype.$translate('resourceTab.ct'),
      prop: 'st',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 160 : 180,
      sortable: 'custom',
    },
    {
      label: Vue.prototype.$translate('natTab.instanceId'),
      prop: 'instanceId',
      key: 'push1SdnsInstanceId',
      'min-width': lang === 'zh-CN' ? 70 : 100,
    },
    {
      label: Vue.prototype.$translate('push1Tab.customer'),
      prop: 'customer',
      key: 'push1SdnsCustomer',
      'min-width': 100,
    },
    {
      label: Vue.prototype.$translate('natTab.clusterName'),
      prop: 'clusterName',
      slotName: 'bodyCell',
      'min-width': lang === 'zh-CN' ? 130 : 160,
    },
    {
      label: Vue.prototype.$translate('microServeTab.projectName'),
      prop: 'projectName',
      key: 'push1SdnsProjectName',
      'min-width': 150,
    },
    {
      label: Vue.prototype.$translate('push1Tab.sdnsDevSrvReq'),
      prop: 'sdnsDevSrvReq',
      'min-width': lang === 'zh-CN' ? 150 : 260,
    },
    {
      label: Vue.prototype.$translate('push1Tab.sdnsAppSrvReq'),
      prop: 'sdnsAppSrvReq',
      'min-width': 150,
    },
  ],
}

export const p2pData = {
  NatServer: [],
  RelayServer: [],
}

export const businessColumns = {
  'monitor.websocket': websocketColumns,
  'monitor.devopswebsocket': devopsWebsocketColumns,
  'monitor.dpikey': dpikeyColumns,
  'monitor.email': emailColumns,
  'monitor.sms': textMessColumns,
  'monitor.account': accountColumns,
  'monitor.push': pushColumns,
}

// 采集项下拉树
export const formOptionsObj = {
  resLayerMonitor: resourceFormOptions,
  appLayerMonitor: appFormOptions,
  businessLayerMonitor: businessFormOptions,
  p2p: p2pFormOptions,
  p2p1: p2p1FormOptions,
  // domainCert: domainFormOptions,
  // dpikey: commonFormOptions,
  // email: commonFormOptions,
  // textMessage: commonFormOptions,
  // push: commonFormOptions,
}

// 所有采集项的表格配置
export const columnsObj = {
  resLayerMonitor: { ...resourceColumns, ...domainColumns },
  appLayerMonitor: appColumns,
  businessLayerMonitor: businessColumns,
  p2p: p2pColumns,
  p2p1: p2pColumns,
}

// 每种monitorType和ctSubType指标对应的metrics--表格请求时使用
export const monitorMetricsObj = {
  'monitor.host-CPU/内存/主机流量': [
    'cpuRate',
    'loadOne',
    'loadFive',
    'loadFifteen',
    'availMemory',
    'memoryRate',
    'recvmb',
    'sentmb',
    'bandwidthRate',
    'nfsAvailable',
  ],
  'monitor.host.disk-disk': ['availDisk', 'diskRate'],
  'monitor.host.process-process': [
    'processCpuRate',
    'memory',
    'processMemoryRate',
  ],
  'monitor.microservice': [
    'available',
    'cpuRate',
    'memory',
    'memoryRate',
    'restarts',
    'dmpfiles',
  ],
  'monitor.microservice.java': [
    'available',
    'cpuRate',
    'memory',
    'memoryRate',
    'restarts',
    'dmpfiles',
  ],
  'monitor.microservice.cplus': [
    'available',
    'cpuRate',
    'memory',
    'memoryRate',
    'restarts',
    'dmpfiles',
  ],
  'monitor.microservice.web': [
    'available',
    'cpuRate',
    'memory',
    'memoryRate',
    'restarts',
    'dmpfiles',
  ],
  'monitor.mysql': [
    'available',
    'maxConnections',
    'dbConnections',
    'dbAvailConnections',
    'dbDeadlocks',
    'dbSpaces',
    'dbSlows',
  ],
  'monitor.redis': [
    'available',
    'memory',
    'memoryRate',
    'connections',
    'blockedClients',
    'rejectedConnections',
    'clusterDowns',
  ],
  'monitor.kafka': [
    'available',
    'memoryRate',
    'cpuRate',
    'lags',
    'clusterDowns',
  ],
  'monitor.mongo': [
    'available',
    'spaces',
    'connections',
    'availConnections',
    'memory',
    'requests',
    'space',
    'spaceRate',
    'slows',
  ],
  'monitor.nginx': [
    'available',
    'connections',
    'availConnections',
    'incrRequests',
  ],
  'monitor.oss': ['available', 'storages', 'objects'],
  'monitor.queue': ['usedRate'],
  'monitor.domain': ['available', 'remainDays'],
  'monitor.cert': ['remainDays'],
  'monitor.nat': [
    'nat_cleint_p2p_req_sn_online',
    'nat_cleint_p2p_req_token_online',
    'nat_client_offline',
    'nat_client_online',
    'nat_client_reg',
    'nat_dev_big_heat',
    'nat_dev_offline',
    'nat_dev_online',
    'nat_dev_reg',
    'nat_dev_small_heat',
    'nat_dev_update_ip',
    'nat_dev_big_heat_v6',
    'nat_dev_small_heat_v6',
    'nat_dev_update_ip_v6',
    'nat_dev_online_dec',
  ],
  'monitor.nat1': [
    'maxDevNum',
    'dev_num',
    'connection_num',
    'client_num',
    'p2pReqNum',
    'relayReqNum',
  ],
  'monitor.udt': [
    'redirect_client',
    'redirect_client_full_list',
    'redirect_dev',
    'redirect_dev_inc',
  ],
  'monitor.relay': [
    'relay_client_req',
    'relay_dev_req',
    'relay_session',
    'relay_session_end',
    'relay_speed_limit_count',
  ],
  'monitor.relay1': ['current_session'],
  'monitor.stun': [
    'nat_cleint_p2p_req_sn_online',
    'nat_cleint_p2p_req_token_online',
    'nat_client_offline',
    'nat_client_online',
    'nat_client_reg',
    'nat_dev_big_heat',
    'nat_dev_offline',
    'nat_dev_online',
    'nat_dev_reg',
    'nat_dev_small_heat',
    'nat_dev_update_ip',
    'nat_dev_big_heat_v6',
    'nat_dev_small_heat_v6',
    'nat_dev_update_ip_v6',
    'nat_dev_online_dec',
  ],
  'monitor.access': [
    'nat_dev_big_heat',
    'nat_dev_offline',
    'nat_dev_online',
    'nat_dev_reg',
    'nat_dev_small_heat',
    'nat_dev_update_ip',
    'nat_dev_big_heat_v6',
    'nat_dev_small_heat_v6',
    'nat_dev_update_ip_v6',
    'nat_dev_online_dec',
  ],
  'monitor.stunRelay': [
    'relay_client_req',
    'relay_dev_req',
    'relay_session',
    'relay_session_end',
    'relay_speed_limit_count',
  ],
  'monitor.nat.cluster': [
    'nat_cleint_p2p_req_sn_online',
    'nat_cleint_p2p_req_token_online',
    'nat_client_offline',
    'nat_client_online',
    'nat_client_reg',
    'nat_dev_big_heat',
    'nat_dev_offline',
    'nat_dev_online',
    'nat_dev_reg',
    'nat_dev_small_heat',
    'nat_dev_update_ip',
    'nat_dev_big_heat_v6',
    'nat_dev_small_heat_v6',
    'nat_dev_update_ip_v6',
    'nat_dev_online_dec',
  ],
  'monitor.relay.cluster': [
    'relay_client_req',
    'relay_dev_req',
    'relay_session',
    'relay_session_end',
    'relay_speed_limit_count',
  ],
  'monitor.stun.cluster': [
    'nat_cleint_p2p_req_sn_online',
    'nat_cleint_p2p_req_token_online',
    'nat_client_offline',
    'nat_client_online',
    'nat_client_reg',
    'nat_dev_big_heat',
    'nat_dev_offline',
    'nat_dev_online',
    'nat_dev_reg',
    'nat_dev_small_heat',
    'nat_dev_update_ip',
    'nat_dev_big_heat_v6',
    'nat_dev_small_heat_v6',
    'nat_dev_update_ip_v6',
    'nat_dev_online_dec',
  ],
  'monitor.stun.relay.cluster': [
    'relay_client_req',
    'relay_dev_req',
    'relay_session',
    'relay_session_end',
    'relay_speed_limit_count',
  ],
  'monitor.push1': [
    'tpnsMsgRecv',
    'tpnsDispMsg',
    'tpnsDevTotal',
    'tpnsEnableDev',
    'tpnsPendingJobCount',
    'amdsPushReq',
    'amdsPushMsg',
    'amdsExpCertCount',
    'gmdsPushReq',
    'gmdsDispMsg',
    'gpnsPushReq',
    'gpnsPushMsg',
    'gpnsClientNum',
    'gpnsClientRegNum',
  ],
  'monitor.push1.sdns': ['sdnsDevSrvReq', 'sdnsAppSrvReq'],
  'monitor.websocket': ['heartbeat', 'disconnects'],
  'monitor.devopswebsocket': ['heartbeat', 'disconnects'],
  'monitor.dpikey': ['dpikeySend', 'dpikeyErr', 'errors'],
  'monitor.email': [
    'emailErr',
    'emailLimit',
    'errors',
    'limits',
    'nums',
    'tps',
  ],
  'monitor.sms': ['smsErr', 'smsLimit', 'errors', 'limits', 'nums', 'tps'],
  'monitor.account': [
    'registerErr',
    'loginErr',
    'registerErrors',
    'loginErrors',
    'registers',
    'logins',
  ],
  'monitor.push': [
    'pushErr',
    'pushLimit',
    'offlineErrors',
    'offlineLimits',
    'offlineTps',
    'onlineErrors',
    'onlinePushs',
    'onlineTps',
    'pushs',
    'tps',
  ],
}

export const fliterCondObj = {
  resLayerMonitor: {
    monitorType: 'monitor.host',
    ctSubtype: 'CPU',
    metrics: ['cpuRate', 'loadOne', 'loadFive', 'loadFifteen'],
  },
  appLayerMonitor: {
    monitorType: 'monitor.microservice',
    ctSubtype: 'microService',
    metrics: [
      'available',
      'cpuRate',
      'memory',
      'memoryRate',
      'restarts',
      'dmpfiles',
    ],
  },
  domainCert: {
    monitorType: 'monitor.domain',
    ctSubtype: 'httpsDomain',
    metrics: ['available', 'remainDays'],
  },
  dpikey: {
    monitorType: 'monitor.dpikey',
    ctSubtype: 'dpikey',
    metrics: ['dpikeySend', 'dpikeyErr', 'errors'],
  },
  email: {
    monitorType: 'monitor.email',
    ctSubtype: 'email',
    metrics: ['emailErr', 'emailLimit', 'errors', 'limits', 'nums', 'tps'],
  },
  textMessage: {
    monitorType: 'monitor.sms',
    ctSubtype: 'textMessage',
    metrics: ['smsErr', 'smsLimit', 'errors', 'limits', 'nums', 'tps'],
  },
  push: {
    monitorType: 'monitor.push',
    ctSubtype: 'push',
    metrics: [
      'pushErr',
      'pushLimit',
      'offlineErrors',
      'offlineLimits',
      'offlineTps',
      'onlineErrors',
      'onlinePushs',
      'onlineTps',
      'pushs',
      'tps',
    ],
  },
  account: {
    monitorType: 'monitor.account',
    ctSubtype: 'account',
    metrics: [
      'registerErr',
      'loginErr',
      'registerErr',
      'loginErr',
      'registerErrors',
      'logins',
    ],
  },
  p2p: {
    monitorType: 'monitor.nat',
    ctSubtype: 'NatServer',
    metrics: [
      'nat_cleint_p2p_req_sn_online',
      'nat_cleint_p2p_req_token_online',
      'nat_client_offline',
      'nat_client_online',
      'nat_client_reg',
      'nat_dev_big_heat',
      'nat_dev_offline',
      'nat_dev_online',
      'nat_dev_reg',
      'nat_dev_small_heat',
      'nat_dev_update_ip',
      'nat_dev_online_dec30',
      'nat_dev_online_dec60',
      'nat_dev_online_dec120',
    ],
  },
  p2p1: {
    monitorType: 'monitor.nat1',
    ctSubtype: 'NatServer',
    metrics: [
      'dev_num',
      'connection_num',
      'client_num',
      'p2pReqNum',
      'relayReqNum',
    ],
  },
}

// 系统监控页面对应的module权限
export const pageMonitorObj = {
  resLayerMonitor: 'devops_monitor_resource', // 资源层监控页面
  appLayerMonitor: 'devops_monitor_application', // 应用层监控页面
  businessLayerMonitor: 'devops_monitor_business', // 业务层监控页面
  p2p: 'devops_monitor_p2p', // P2P2.0监控页面
  p2p1: 'devops_monitor_p2p1', // P2P1.0监控页面
}
