import Vue from 'vue'
import { getLocale } from '@/lang'

const lang = getLocale() || 'zh-CN'

export const ctyTypeOptions = [
  { label: Vue.prototype.$translate('all'), value: 'All' },
  { label: Vue.prototype.$translate('hostMonitor'), value: 'monitor_host' },
  { label: Vue.prototype.$translate('disk'), value: 'monitor_disk' },
  { label: Vue.prototype.$translate('process'), value: 'monitor_process' },
  {
    label: Vue.prototype.$translate('javaMicroService'),
    value: 'monitor_microservice_java',
  },
  {
    label: Vue.prototype.$translate('cplusMicroService'),
    value: 'monitor_microservice_cplus',
  },
  {
    label: Vue.prototype.$translate('webMicroService'),
    value: 'monitor_microservice_web',
  },
  {
    label: Vue.prototype.$translate('mysqlInstance'),
    value: 'monitor_mysql_instance',
  },
  { label: Vue.prototype.$translate('mysqlDb'), value: 'monitor_mysql_db' },
  { label: 'Redis', value: 'monitor_redis' },
  { label: 'Kafka', value: 'monitor_kafka' },
  {
    label: Vue.prototype.$translate('kafkaNode'),
    value: 'monitor_kafka_node',
  },
  {
    label: Vue.prototype.$translate('kafkaCluster'),
    value: 'monitor_kafka_cluster',
  },
  { label: 'Mongo', value: 'monitor_mongo' },
  { label: 'Nginx', value: 'monitor_nginx' },
  { label: 'OSS/S3', value: 'monitor_oss' },
  { label: 'Queue', value: 'monitor_queue' },
  { label: 'Apache', value: 'monitor_apache' },
  { label: 'Websocket', value: 'monitor_wsConnect' },
  { label: Vue.prototype.$translate('httpsDomain'), value: 'monitor_domain' },
  { label: Vue.prototype.$translate('certificate'), value: 'monitor_cert' },
  { label: 'NatServer', value: 'monitor_nat' },
  { label: 'RedirectServer', value: 'monitor_udt' },
  { label: 'RelayServer', value: 'monitor_relay' },
  { label: 'StunServer', value: 'monitor_stun' },
  { label: 'AccessServer', value: 'monitor_access' },
  { label: 'StunRelayServer', value: 'monitor_stunRelay' },
  { label: 'NatCluster', value: 'monitor_nat_cluster' },
  { label: 'RelayCluster', value: 'monitor_relay_cluster' },
  { label: 'StunCluster', value: 'monitor_stun_cluster' },
  { label: 'StunRelayCluster', value: 'monitor_stun_relay_cluster' },
  { label: 'Websocket', value: 'monitor_websocket' },
  { label: 'Dpikey', value: 'monitor_dpikey' },
  { label: Vue.prototype.$translate('email'), value: 'monitor_email' },
  { label: Vue.prototype.$translate('textMessage'), value: 'monitor_sms' },
  { label: Vue.prototype.$translate('account'), value: 'monitor_account' },
  { label: Vue.prototype.$translate('push2'), value: 'monitor_push' },
  { label: Vue.prototype.$translate('videoFlow'), value: 'monitor_video_flow' },
  { label: 'DevopsWebsocket', value: 'monitor_devopswebsocket' },
]

export const collectItemList = [
  '主机',
  '微服务',
  'Mysql',
  'Redis',
  'Kafka',
  'Mongo',
  'Nginx',
  'OSS/S3',
  'Https域名',
  '证书',
  'NatServer',
  'RedirectServer',
  'RelayServer',
  'StunServer',
  'AccessServer',
  'WebSocket',
  'dpikey',
  '邮件',
  '短信',
  '账号',
  '推送2.0',
]

// 指标名称
export const alarmIndexDescObj = {
  monitor_host_cpuRate: Vue.prototype.$translate('cpuRate'),
  monitor_host_loadOne: Vue.prototype.$translate('loadOne'),
  monitor_host_loadFive: Vue.prototype.$translate('loadFive'),
  monitor_host_loadFifteen: Vue.prototype.$translate('loadFifteen'),
  monitor_host_availMemory: Vue.prototype.$translate('hostAvailMemory'),
  monitor_host_memoryRate: Vue.prototype.$translate('hostMemoryRate'),
  monitor_host_availDisk: Vue.prototype.$translate('hostAvailDisk'),
  monitor_host_diskRate: Vue.prototype.$translate('hostDiskRate'),
  monitor_host_recvmb: Vue.prototype.$translate('hostRecvmb'),
  monitor_host_sentmb: Vue.prototype.$translate('hostSentmb'),
  monitor_host_bandwidthRate: Vue.prototype.$translate('hostBandwidthRate'),
  monitor_host_memory: Vue.prototype.$translate('processMemory'),
  monitor_host_processCpuRate: Vue.prototype.$translate('processCpuRate'),
  monitor_host_processMemoryRate: Vue.prototype.$translate('processMemoryRate'),
  monitor_host_nfsAvailable: Vue.prototype.$translate(
    'resourceTab.nfsAvailable'
  ),
  monitor_microservice_available: Vue.prototype.$translate(
    'microServeTab.available'
  ),
  monitor_microservice_cpuRate: Vue.prototype.$translate('microcpuRate'),
  monitor_microservice_memory: Vue.prototype.$translate('usedMemory'),
  monitor_microservice_memoryRate: Vue.prototype.$translate('hostMemoryRate'),
  monitor_microservice_restarts: Vue.prototype.$translate(
    'microServeTab.restarts'
  ),
  monitor_microservice_dmpfiles: Vue.prototype.$translate(
    'microServeTab.dmpfiles'
  ),
  monitor_mysql_available: Vue.prototype.$translate('mySqlTab.available'),
  monitor_mysql_maxConnections: Vue.prototype.$translate(
    'mySqlTab.maxConnections'
  ),
  monitor_mysql_dbConnections: Vue.prototype.$translate(
    'mySqlTab.dbConnections'
  ),
  monitor_mysql_dbAvailConnections: Vue.prototype.$translate(
    'mySqlTab.dbAvailConnections'
  ),
  monitor_mysql_dbDeadlocks: Vue.prototype.$translate('mySqlTab.dbDeadlocks'),
  monitor_mysql_dbSpaces: Vue.prototype.$translate('mySqlTab.dbSpaces'),
  monitor_mysql_dbSlows: Vue.prototype.$translate('mySqlTab.dbSlows'),
  monitor_redis_available: Vue.prototype.$translate('redisTab.available'),
  monitor_redis_memory: Vue.prototype.$translate('usedMemory'),
  monitor_redis_memoryRate: Vue.prototype.$translate(
    'resourceMonitor.memoryUseRate'
  ),
  monitor_redis_connections: Vue.prototype.$translate('redisTab.connections'),
  monitor_redis_blockedClients: Vue.prototype.$translate(
    'redisTab.blockedClients'
  ),
  monitor_redis_rejectedConnections: Vue.prototype.$translate(
    'redisTab.rejectedConnections'
  ),
  monitor_redis_clusterDowns: Vue.prototype.$translate('redisTab.clusterDowns'),
  monitor_kafka_available: Vue.prototype.$translate('kafkaTab.available'),
  monitor_kafka_memoryRate: Vue.prototype.$translate(
    'resourceMonitor.memoryUseRate'
  ),
  monitor_kafka_cpuRate: Vue.prototype.$translate('resourceMonitor.cpuUseRate'),
  monitor_kafka_lags: Vue.prototype.$translate('kafkaTab.lags'),
  monitor_kafka_clusterDowns: Vue.prototype.$translate('kafkaTab.clusterDowns'),
  monitor_mongo_available: Vue.prototype.$translate('mongoTab.available'),
  monitor_mongo_spaces: Vue.prototype.$translate('mySqlTab.dbSpaces'),
  monitor_mongo_connections: Vue.prototype.$translate('mongoTab.connections'),
  monitor_mongo_availConnections: Vue.prototype.$translate(
    'mongoTab.availConnections'
  ),
  monitor_mongo_memory: Vue.prototype.$translate('usedMemory'),
  monitor_mongo_requests: Vue.prototype.$translate('mongoTab.requests'),
  monitor_mongo_slows: Vue.prototype.$translate('mongoTab.slows'),
  monitor_nginx_available: Vue.prototype.$translate('nginxTab.available'),
  monitor_nginx_connections: Vue.prototype.$translate('nginxTab.connections'),
  monitor_nginx_availConnections: Vue.prototype.$translate(
    'nginxTab.availConnections'
  ),
  monitor_nginx_incrRequests: Vue.prototype.$translate('nginxIncrRequests'),
  monitor_oss_available: Vue.prototype.$translate('ossTab.available'),
  monitor_oss_storages: Vue.prototype.$translate('storages'),
  monitor_oss_objects: Vue.prototype.$translate('ossTab.objects'),
  monitor_domain_available: Vue.prototype.$translate('domainCertTab.available'),
  monitor_domain_remainDays: Vue.prototype.$translate('remainDays'),
  monitor_cert_remainDays: Vue.prototype.$translate('remainDays'),
  monitor_nat_natCleintP2pReqSnOnline: Vue.prototype.$translate(
    'natTab.nat_cleint_p2p_req_sn_online'
  ),
  monitor_nat_natCleintP2pReqTokenOnline: Vue.prototype.$translate(
    'natTab.nat_cleint_p2p_req_token_online'
  ),
  monitor_nat_natClientOffline: Vue.prototype.$translate(
    'natTab.nat_client_offline'
  ),
  monitor_nat_natClientOnline: Vue.prototype.$translate(
    'natTab.nat_client_online'
  ),
  monitor_nat_natClientReg: Vue.prototype.$translate('natTab.nat_client_reg'),
  monitor_nat_natDevBigHeat: Vue.prototype.$translate(
    'natTab.nat_dev_big_heat'
  ),
  monitor_nat_natDevOffline: Vue.prototype.$translate('natTab.nat_dev_offline'),
  monitor_nat_natDevOnline: Vue.prototype.$translate('natTab.nat_dev_online'),
  monitor_nat_natDevReg: Vue.prototype.$translate('natTab.nat_dev_reg'),
  monitor_nat_natDevSmallHeat: Vue.prototype.$translate(
    'natTab.nat_dev_small_heat'
  ),
  monitor_nat_natDevUpdateIp: Vue.prototype.$translate(
    'natTab.nat_dev_update_ip'
  ),
  monitor_nat_natDevBigHeatV6: Vue.prototype.$translate(
    'natTab.nat_dev_big_heat_v6'
  ),
  monitor_nat_natDevSmallHeatV6: Vue.prototype.$translate(
    'natTab.nat_dev_small_heat_v6'
  ),
  monitor_nat_natDevUpdateIpV6: Vue.prototype.$translate(
    'natTab.nat_dev_update_ip_v6'
  ),
  monitor_nat_natDevOnlineDec: Vue.prototype.$translate(
    'natTab.nat_dev_online_dec'
  ),
  monitor_udt_redirectClient: Vue.prototype.$translate(
    'redirectTab.redirect_client'
  ),
  monitor_udt_redirectClientFullList: Vue.prototype.$translate(
    'redirectTab.redirect_client_full_list'
  ),
  monitor_udt_redirectDev: Vue.prototype.$translate('redirectTab.redirect_dev'),
  monitor_udt_redirectDevInc: Vue.prototype.$translate(
    'redirectTab.redirect_dev_inc'
  ),
  monitor_relay_relayClientReq: Vue.prototype.$translate(
    'relayTab.relay_client_req'
  ),
  monitor_relay_relayDevReq: Vue.prototype.$translate('relayTab.relay_dev_req'),
  monitor_relay_relaySession: Vue.prototype.$translate(
    'relayTab.relay_session'
  ),
  monitor_relay_relaySessionEnd: Vue.prototype.$translate(
    'relayTab.relay_session_end'
  ),
  monitor_relay_relaySpeedLimitCount: Vue.prototype.$translate(
    'relayTab.relay_speed_limit_count'
  ),
  monitor_natwebsocket_heartbeat: Vue.prototype.$translate(
    'websocketTab.heartbeat'
  ),
  monitor_natwebsocket_disconnects: Vue.prototype.$translate(
    'websocketTab.disconnects'
  ),
  monitor_dpikey_dpikeySend: Vue.prototype.$translate('dpikeyTab.dpikeySend'),
  monitor_dpikey_dpikeyErr: Vue.prototype.$translate('dpikeyTab.dpikeyErr'),
  monitor_dpikey_errors: Vue.prototype.$translate('dpikeyErrors'),
  monitor_email_emailErr: Vue.prototype.$translate('emailTab.emailErr'),
  monitor_email_emailLimit: Vue.prototype.$translate('emailTab.emailLimit'),
  monitor_email_errors: Vue.prototype.$translate('emailErrors'),
  monitor_email_limits: Vue.prototype.$translate('emailLimits'),
  monitor_email_nums: Vue.prototype.$translate('emailNums'),
  monitor_email_tps: Vue.prototype.$translate('emailTps'),
  monitor_sms_smsErr: Vue.prototype.$translate('textMessageTab.smsErr'),
  monitor_sms_smsLimit: Vue.prototype.$translate('textMessageTab.smsLimit'),
  monitor_sms_errors: Vue.prototype.$translate('smsErrors'),
  monitor_sms_limits: Vue.prototype.$translate('smsLimits'),
  monitor_sms_nums: Vue.prototype.$translate('smsNums'),
  monitor_sms_tps: Vue.prototype.$translate('smsTps'),
  monitor_account_registerErr: Vue.prototype.$translate(
    'accountTab.registerErr'
  ),
  monitor_account_loginErr: Vue.prototype.$translate('accountTab.loginErr'),
  monitor_account_registerErrors: Vue.prototype.$translate(
    'accountTab.registerErrors'
  ),
  monitor_account_loginErrors: Vue.prototype.$translate(
    'accountTab.loginErrors'
  ),
  monitor_account_registers: Vue.prototype.$translate('accountTab.registers'),
  monitor_account_logins: Vue.prototype.$translate('accountTab.logins'),
  monitor_push_pushErr: Vue.prototype.$translate('pushTab.pushErr'),
  monitor_push_pushLimit: Vue.prototype.$translate('pushTab.pushLimit'),
  monitor_push_offlineErrors: Vue.prototype.$translate('pushOfflineErrors'),
  monitor_push_offlineLimits: Vue.prototype.$translate('pushOfflineLimits'),
  monitor_push_offlineTps: Vue.prototype.$translate('pushOfflineTps'),
  monitor_push_onlineErrors: Vue.prototype.$translate('pushOnlineErrors'),
  monitor_push_onlinePushs: Vue.prototype.$translate('pushOnlinePushs'),
  monitor_push_onlineTps: Vue.prototype.$translate('pushOnlineTps'),
  monitor_push_pushs: Vue.prototype.$translate('pushPushs'),
  monitor_push_tps: Vue.prototype.$translate('pushTps'),
  monitor_media_trafficIn: Vue.prototype.$translate('mediaTrafficIn'),
  monitor_media_trafficOut: Vue.prototype.$translate('mediaTrafficOut'),
  monitor_devopswebsocket_heartbeat: Vue.prototype.$translate(
    'websocketTab.heartbeat'
  ),
  monitor_devopswebsocket_disconnects: Vue.prototype.$translate(
    'websocketTab.disconnects'
  ),
}

// 指标算法
// 指标名称
export const ctIndexAlgorithmObj = {
  monitor_host_cpuRate: '使用开源件oshi统一采集，不区分操作系统',
  monitor_host_loadOne:
    'linux中执行uptime，在回显中取load average，不支持window',
  monitor_host_loadFive:
    'linux中执行uptime，在回显中取load average，不支持window',
  monitor_host_loadFifteen:
    'linux中执行uptime，在回显中取load average，不支持window',
  monitor_host_availMemory: '使用开源件oshi统一采集，不区分操作系统',
  monitor_host_memoryRate: '使用开源件oshi统一采集，不区分操作系统',
  monitor_host_availDisk: '使用开源件oshi统一采集，不区分操作系统',
  monitor_host_diskRate: '使用开源件oshi统一采集，不区分操作系统',
  monitor_host_recvmb: '使用开源件oshi统一采集，不区分操作系统',
  monitor_host_sentmb: '使用开源件oshi统一采集，不区分操作系统',
  monitor_host_bandwidthRate: '使用开源件oshi统一采集，不区分操作系统',
  monitor_host_memory:
    '使用开源件oshi统一采集，不区分操作系统，只采集Top10的进程数据',
  monitor_host_processCpuRate:
    '使用开源件oshi统一采集，不区分操作系统，只采集Top10的进程数据',
  monitor_host_processMemoryRate:
    '使用开源件oshi统一采集，不区分操作系统，只采集Top10的进程数据',
  monitor_host_nfsAvailable:
    '在Agent主机执行命令 grep "/nfs/nfs_client_data" /proc/mounts 获取挂载的共享路径和环境配置的nfs_server是否一致。是则NFS可用，否则不可用',
  monitor_microservice_available:
    'java服务查询/service/info接口和Eureka的状态，Web的查询图片接口地址',
  monitor_microservice_cpuRate: 'ps aux | grep ${工程名} 获取%CPU',
  monitor_microservice_memory: 'ps aux | grep ${工程名} 获取RSS',
  monitor_microservice_memoryRate: '根据memory/微服务启动最大内存得到',
  monitor_microservice_restarts: '在微服务每次重启时，记录启动日志到告警日志中',
  monitor_microservice_dmpfiles:
    '在启动脚本中指定生产dmp文件的路径，根据dmp文件获取到',
  monitor_mysql_available: 'mysql连接正常，查询正常',
  monitor_mysql_maxConnections: "查询show variables like '%max_connections%'",
  monitor_mysql_dbConnections:
    'mysql连接后，执行show processlist ，获取具体数据库的已使用连接',
  monitor_mysql_dbAvailConnections: 'mysql配置的最大连接数-已使用连接数',
  monitor_mysql_dbDeadlocks:
    '查询show engine innodb status获取到LATEST DETECTED DEADLOCK次数',
  monitor_mysql_dbSpaces: '登录mysql，查询系统表获取数据',
  monitor_mysql_dbSlows:
    '根据架构中的DBSqlAdapterCBB获取到执行SQL>3秒的SQL信息和次数',
  monitor_redis_available:
    '可连接，设置一条测试数据，查询一条测试数据，删除一条测试数据',
  monitor_redis_memory:
    '使用stringRedisTemplate.execute(redisConnection -> redisConnection.info("memory"))获取used_memory值，单位KB。',
  monitor_redis_memoryRate:
    '使用RedisConnectionFactory#getConnection().info("clients").get("connected_clients")获取。当前连接到Redis服务器的客户端数量connected_clients，如果太低，表示客户端连接可能已经丢失，如果太高，表示并发客户端连接可能会压垮服务器处理能力。',
  monitor_redis_connections:
    '使用RedisConnectionFactory#getConnection().info("clients").get("connected_clients")获取。当前连接到Redis服务器的客户端数量connected_clients，如果太低，表示客户端连接可能已经丢失，如果太高，表示并发客户端连接可能会压垮服务器处理能力。',
  monitor_redis_blockedClients:
    '使用stringRedisTemplate.execute(redisConnection -> redisConnection.info("blocking")) 获取。由于BLPOP,BRPOP,BRPOPLPUSH等阻塞命令导致阻塞的客户端数量blocked_clients。偶尔非零是正常的，但如果持续非零值时，应该引起关注。',
  monitor_redis_rejectedConnections:
    '使用stringRedisTemplate.execute(redisConnection -> redisConnection.info("all")) 获取rejected_connections。达到maxclient限制而被拒绝的连接数,出现连续拒绝时应该告警',
  monitor_redis_clusterDowns:
    '使用 stringRedisTemplate.execute(redisConnection -> redisConnection.clusterNodes()); 获取集群状态（flags为fail的是down状态) 所有节点状态正常，没有挂掉的节点',
  monitor_kafka_available: '生产一条消息，消费一条消息功能正常',
  monitor_kafka_memoryRate: 'ps aux | grep ${工程名} 获取%CPU和RSS',
  monitor_kafka_cpuRate: 'ps aux | grep ${工程名} 获取%CPU和RSS',
  monitor_kafka_lags:
    '使用Kafka自带工具命令获取：cd /{Kafka_Home}/bin && ./kafka-consumer-groups.sh --bootstrap-server IP:Port --list 解析返回，获取LAG值。最后统计每个Topic的消息堆积量。',
  monitor_kafka_clusterDowns: `// bootstrapServers为当前DC环境中的集群节点信息，如：***********:9092,***********:9092,***********:9092
  private int loadClusterDownNum(String bootstrapServers) {
  int clusterSize = bootstrapServers.split(",").length;
  Properties props = new Properties();
  props.put("bootstrap.servers", bootstrapServers);
  int downNum = clusterSize;
  try (AdminClient adminClient = KafkaAdminClient.create(props)) {
  int availableSize = adminClient.describeCluster(new DescribeClusterOptions().timeoutMs(1000)).nodes().get().size();
  downNum = availableSize >= clusterSize ? 0 : clusterSize - availableSize;
  } catch (Exception e) {
  e.printStackTrace();
  }
  return downNum;
  }`,
  monitor_mongo_available: '可正常登录',
  monitor_mongo_spaces:
    '登录mongodb，执行“db.stats()”命令获取，其中storageSize为数据库占用的总存储空间大小（单位：字节）',
  monitor_mongo_connections:
    '登录mongodb，执行“db.serverStatus().connections;”命令获取，其中current为已建立的连接数，available为可用的连接数 总连接数，指当前集群 proxy 收到的连接数',
  monitor_mongo_availConnections:
    '登录mongodb，执行“db.serverStatus().connections;”命令获取，其中current为已建立的连接数，available为可用的连接数 总连接数，指当前集群 proxy 收到的连接数',
  monitor_mongo_memory:
    '登录mongodb，执行“db.serverStatus().mem;”命令，获取resident字段值，即当前进程使用的物理内存量（单位：MB)',
  monitor_mongo_requests:
    '登录mongodb，执行“db.serverStatus().network.numRequests;”获取当前累计请求次数。然后与上一次获取的值计算差值，计算每分钟请求次数。每秒操作数，包含 CRUD 操作',
  monitor_mongo_slows:
    '根据架构中的NosqlAdapterCBB获取到执行MongoApi执行>3秒的接口名和次数',
  monitor_nginx_available: '开启stub_status ，通过nginx本地访问是否成功',
  monitor_nginx_connections:
    '开启stub_status ，通过nginx本地访问获取Active connections值',
  monitor_nginx_availConnections:
    '使用最大连接数-已使用连接数，最大连接数使用worker_connections的值',
  monitor_nginx_incrRequests:
    '开启stub_status ，通过nginx本地访问获取requests值，使用本次采集数据-上次采集数据',
  monitor_oss_available: '可以正常登录连接上OSS',
  monitor_oss_storages:
    '连接上OSS，使用SDK接口ossClient.getBucketStat(桶名); 获取总存储容量 S3的使用sdk接口进行遍历统计，比较耗时',
  monitor_oss_objects:
    '连接上OSS，使用SDK接口ossClient.getBucketStat(桶名); 获取Object数量。S3的使用sdk接口进行遍历统计，比较耗时',
  monitor_domain_available: '能否ping通各域名',
  monitor_domain_remainDays:
    '访问域名获取证书有效剩余时间，参考样例代码：OdsHttpsCertConsumer 域名有效剩余时间',
  monitor_cert_remainDays:
    'Java的KeyStore.load证书文件，通过Certificate的getNotAfter获取过期日期。离线推送证书路径：/nfs/nfs_client_data/customer_data/cert 离线推送证书有效剩余时间',
  monitor_nat_natCleintP2pReqSnOnline: '通过monitor日志采集',
  monitor_nat_natCleintP2pReqTokenOnline: '通过monitor日志采集',
  monitor_nat_natClientOffline: '通过monitor日志采集',
  monitor_nat_natClientOnline: '通过monitor日志采集',
  monitor_nat_natClientReg: '通过monitor日志采集',
  monitor_nat_natDevBigHeat: '通过monitor日志采集',
  monitor_nat_natDevOffline: '通过monitor日志采集',
  monitor_nat_natDevOnline: '通过monitor日志采集',
  monitor_nat_natDevReg: '通过monitor日志采集',
  monitor_nat_natDevSmallHeat: '通过monitor日志采集',
  monitor_nat_natDevUpdateIp: '通过monitor日志采集',
  monitor_nat_natDevBigHeatV6: '通过monitor日志采集',
  monitor_nat_natDevSmallHeatV6: '通过monitor日志采集',
  monitor_nat_natDevUpdateIpV6: '通过monitor日志采集',
  monitor_nat_natDevOnlineDec:
    '5分钟内上线数最大值-当前设备端上线数量，超过阈值告警',
  monitor_udt_redirectClient: '通过monitor日志采集',
  monitor_udt_redirectClientFullList: '通过monitor日志采集',
  monitor_udt_redirectDev: '通过monitor日志采集',
  monitor_udt_redirectDevInc:
    '当前设备端重定向数量-5分钟内重定向数量最大值，超过阈值告警',
  monitor_relay_relayClientReq: '通过monitor日志采集',
  monitor_relay_relayDevReq: '通过monitor日志采集',
  monitor_relay_relaySession: '通过monitor日志采集',
  monitor_relay_relaySessionEnd: '通过monitor日志采集',
  monitor_relay_relaySpeedLimitCount: '通过monitor日志采集',
  monitor_natwebsocket_heartbeat: '当天累计断开次数超过阈值告警',
  monitor_natwebsocket_disconnects: '',
  monitor_dpikey_dpikeySend: '下发失败或是获取失败告警',
  monitor_dpikey_dpikeyErr: '在使用dpikey认证发生dpikey异常时告警',
  monitor_dpikey_errors: '每分钟异常次数',
  monitor_email_emailErr: '发送邮件失败，发送告警',
  monitor_email_emailLimit: '发送邮件超出频率控制，发送告警',
  monitor_email_errors:
    '按照自然分钟数统计发送邮件失败的总数量，调用发送邮件接口出现异常',
  monitor_email_limits: '每分钟邮件发送超出频率次数',
  monitor_email_nums: '按照自然分钟数统计发送邮件的数量',
  monitor_email_tps: '取每分钟内的TPS峰值',
  monitor_sms_smsErr: '发送短信失败，发送告警',
  monitor_sms_smsLimit: '发送短信超出频率控制，发送告警',
  monitor_sms_errors:
    '按照自然分钟数统计发送短信失败的总数量，调用发送短信接口出现异常',
  monitor_sms_limits: '每分钟短信发送超出频率次数',
  monitor_sms_nums: '按照自然分钟数统计发送短信的数量',
  monitor_sms_tps: '取每分钟内的TPS峰值',
  monitor_account_registerErr: '账号注册过程发生异常',
  monitor_account_loginErr: '账号登录过程发生异常',
  monitor_account_registerErrors: '按照自然分钟数统计注册账号异常的数量',
  monitor_account_loginErrors: '按照自然分钟数统计注册账号异常的数量',
  monitor_account_registers: '按照自然分钟数统计注册账号的数量',
  monitor_account_logins: '按照自然分钟数统计账号登录的数量',
  monitor_push_pushErr: '推送流程发生异常，发送告警',
  monitor_push_pushLimit: '推送超出频率控制，发送告警',
  monitor_push_offlineErrors: '按照自然分钟数统计离线推送失败的次数',
  monitor_push_offlineLimits: '按照自然分钟数统计离线推送超出频率限制的次数',
  monitor_push_offlineTps: '取每分钟内离线推送的TPS峰值',
  monitor_push_onlineErrors: '按照自然分钟数统计在线推送失败的次数',
  monitor_push_onlinePushs: '按照自然分钟数统计在线推送超出频率限制的次数',
  monitor_push_onlineTps: '取每分钟内在线推送的TPS峰值',
  monitor_push_pushs: '按照自然分钟数统计推送的次数，包含离线推送和在线推送',
  monitor_push_tps: '取每分钟内推送的TPS峰值',
}

export const alarmConfigData = {
  主机: [
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: 'CPU',
      ctIndexDesccn: 'CPU利用率',
      threshold: '80',
      alarmThreshold: '90',
      algorithm: '使用开源件oshi统一采集，不区分操作系统',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [15, 1],
      thirdColumnSpan: [4, 1],
    },
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: 'CPU',
      ctIndexDesccn: '1分钟平均负载',
      threshold: '',
      alarmThreshold: '',
      algorithm: 'linux中执行uptime，在回显中取load average，不支持window',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: 'CPU',
      ctIndexDesccn: '5分钟平均负载',
      threshold: '',
      alarmThreshold: '',
      algorithm: 'linux中执行uptime，在回显中取load average，不支持window',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: 'CPU',
      ctIndexDesccn: '15分钟平均负载',
      threshold: '',
      alarmThreshold: '',
      algorithm: 'linux中执行uptime，在回显中取load average，不支持window',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: '内存',
      ctIndexDesccn: '可用内存',
      threshold: '1024',
      alarmThreshold: '512',
      algorithm: '使用开源件oshi统一采集，不区分操作系统',
      alarmEmail: '',
      alarmSms: '',
      thirdColumnSpan: [2, 1],
    },
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: '内存',
      ctIndexDesccn: '内存使用率',
      threshold: '80',
      alarmThreshold: '90',
      algorithm: '使用开源件oshi统一采集，不区分操作系统',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: '磁盘',
      ctIndexDesccn: '剩余空间',
      threshold: '5120',
      alarmThreshold: '1024',
      algorithm: '使用开源件oshi统一采集，不区分操作系统',
      alarmEmail: '',
      alarmSms: '',
      thirdColumnSpan: [2, 1],
    },
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: '磁盘',
      ctIndexDesccn: '空间使用率',
      threshold: '80',
      alarmThreshold: '90',
      algorithm: '使用开源件oshi统一采集，不区分操作系统',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: '主机流量',
      ctIndexDesccn: '每秒入流量',
      threshold: '1024',
      alarmThreshold: '2048',
      algorithm: '使用开源件oshi统一采集，不区分操作系统',
      alarmEmail: '',
      alarmSms: '',
      thirdColumnSpan: [3, 1],
    },
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: '主机流量',
      ctIndexDesccn: '每秒出流量',
      threshold: '1024',
      alarmThreshold: '2048',
      algorithm: '使用开源件oshi统一采集，不区分操作系统',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: '主机流量',
      ctIndexDesccn: '每秒带宽利用率',
      threshold: '80',
      alarmThreshold: '90',
      algorithm: '使用开源件oshi统一采集，不区分操作系统',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: '进程',
      ctIndexDesccn: '进程使用物理内存',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '使用开源件oshi统一采集，不区分操作系统，只采集Top10的进程数据',
      alarmEmail: '',
      alarmSms: '',
      thirdColumnSpan: [3, 1],
    },
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: '进程',
      ctIndexDesccn: '单进程CPU使用率',
      threshold: '80',
      alarmThreshold: '90',
      algorithm:
        '使用开源件oshi统一采集，不区分操作系统，只采集Top10的进程数据',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: '进程',
      ctIndexDesccn: '单进程内存使用率',
      threshold: '80',
      alarmThreshold: '90',
      algorithm:
        '使用开源件oshi统一采集，不区分操作系统，只采集Top10的进程数据',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '主机',
      ctPeriod: '每30秒',
      ctSubtype: 'NFS挂载',
      ctIndexDesccn: 'NFS可使用',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm:
        '在Agent主机执行命令 grep "/nfs/nfs_client_data" /proc/mounts 获取挂载的共享路径和环境配置的nfs_server是否一致。是则NFS可用，否则不可用',
      alarmEmail: '',
      alarmSms: '',
      thirdColumnSpan: [1, 1],
    },
  ],
  微服务: [
    {
      ctType: '微服务',
      ctPeriod: '每30秒',
      ctSubtype: '微服务',
      ctIndexDesccn: '服务可使用',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm:
        'java服务查询/service/info接口和Eureka的状态，Web的查询图片接口地址',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [6, 1],
      thirdColumnSpan: [6, 1],
      eighthColumnSpan: [6, 1],
    },
    {
      ctType: '微服务',
      ctPeriod: '每30秒',
      ctSubtype: '微服务',
      ctIndexDesccn: 'CPU使用率',
      threshold: '80',
      alarmThreshold: '90',
      algorithm: 'ps aux | grep ${工程名} 获取%CPU',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '微服务',
      ctPeriod: '每30秒',
      ctSubtype: '微服务',
      ctIndexDesccn: '已使用内存',
      threshold: '',
      alarmThreshold: '',
      algorithm: 'ps aux | grep ${工程名} 获取RSS',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '微服务',
      ctPeriod: '每30秒',
      ctSubtype: '微服务',
      ctIndexDesccn: '内存使用率',
      threshold: '80',
      alarmThreshold: '90',
      algorithm: '根据memory/微服务启动最大内存得到',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '微服务',
      ctPeriod: '每30秒',
      ctSubtype: '微服务',
      ctIndexDesccn: '重启次数',
      threshold: '1',
      alarmThreshold: '2',
      algorithm: '在微服务每次重启时，记录启动日志到告警日志中',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '微服务',
      ctPeriod: '每30秒',
      ctSubtype: '微服务',
      ctIndexDesccn: '内存溢出文件数',
      threshold: '',
      alarmThreshold: '1',
      algorithm: '在启动脚本中指定生产dmp文件的路径，根据dmp文件获取到',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  Mysql: [
    {
      ctType: 'Mysql',
      ctPeriod: '每1分钟',
      ctSubtype: 'Mysql',
      ctIndexDesccn: 'mysql可使用',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: 'mysql连接正常，查询正常',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [7, 1],
      thirdColumnSpan: [7, 1],
    },
    {
      ctType: 'Mysql',
      ctPeriod: '每1分钟',
      ctSubtype: 'Mysql',
      ctIndexDesccn: 'mysql实例最大连接总数',
      threshold: '',
      alarmThreshold: '1',
      algorithm: "查询show variables like '%max_connections%'",
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Mysql',
      ctPeriod: '每1分钟',
      ctSubtype: 'Mysql',
      ctIndexDesccn: '数据库已使用连接数',
      threshold: '',
      alarmThreshold: '1',
      algorithm:
        'mysql连接后，执行show processlist ，获取具体数据库的已使用连接',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Mysql',
      ctPeriod: '每1分钟',
      ctSubtype: 'Mysql',
      ctIndexDesccn: '数据库连接数',
      threshold: '20',
      alarmThreshold: '1',
      algorithm: 'mysql配置的最大连接数-已使用连接数',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Mysql',
      ctPeriod: '每1分钟',
      ctSubtype: 'Mysql',
      ctIndexDesccn: '数据库死锁次数',
      threshold: '1',
      alarmThreshold: '2',
      algorithm:
        '查询show engine innodb status获取到LATEST DETECTED DEADLOCK次数',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Mysql',
      ctPeriod: '每1分钟',
      ctSubtype: 'Mysql',
      ctIndexDesccn: '数据库占用空间',
      threshold: '',
      alarmThreshold: '',
      algorithm: '登录mysql，查询系统表获取数据',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Mysql',
      ctPeriod: '每1分钟',
      ctSubtype: 'Mysql',
      ctIndexDesccn: '慢查询次数',
      threshold: '5',
      alarmThreshold: '10',
      algorithm: '根据架构中的DBSqlAdapterCBB获取到执行SQL>3秒的SQL信息和次数',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  Redis: [
    {
      ctType: 'Redis',
      ctPeriod: '每1分钟',
      ctSubtype: 'Redis',
      ctIndexDesccn: 'redis可使用',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '可连接，设置一条测试数据，查询一条测试数据，删除一条测试数据',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [7, 1],
      thirdColumnSpan: [7, 1],
    },
    {
      ctType: 'Redis',
      ctPeriod: '每1分钟',
      ctSubtype: 'Redis',
      ctIndexDesccn: '已使用内存',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '使用stringRedisTemplate.execute(redisConnection -> redisConnection.info("memory"))\r\n获取used_memory值，单位KB。',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Redis',
      ctPeriod: '每1分钟',
      ctSubtype: 'Redis',
      ctIndexDesccn: '内存利用率',
      threshold: '80',
      alarmThreshold: '90',
      algorithm:
        '使用stringRedisTemplate.execute(redisConnection -> redisConnection.info("memory"))\r\n获取maxmemory值，单位KB。再计算：\r\n内存利用率 = 内存使用量used_memory / 最大可用内存maxmemory',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Redis',
      ctPeriod: '每1分钟',
      ctSubtype: 'Redis',
      ctIndexDesccn: '客户端连接数',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '使用RedisConnectionFactory#getConnection().info("clients").get("connected_clients")获取。\r\n当前连接到Redis服务器的客户端数量connected_clients，如果太低，表示客户端连接可能已经丢失，如果太高，表示并发客户端连接可能会压垮服务器处理能力。',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Redis',
      ctPeriod: '每1分钟',
      ctSubtype: 'Redis',
      ctIndexDesccn: '阻塞连接数',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '使用stringRedisTemplate.execute(redisConnection -> redisConnection.info("blocking")) 获取。\r\n由于BLPOP,BRPOP,BRPOPLPUSH等阻塞命令导致阻塞的客户端数量blocked_clients。偶尔非零是正常的，但如果持续非零值时，应该引起关注。',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Redis',
      ctPeriod: '每1分钟',
      ctSubtype: 'Redis',
      ctIndexDesccn: '拒绝连接数',
      threshold: '5',
      alarmThreshold: '10',
      algorithm:
        '使用stringRedisTemplate.execute(redisConnection -> redisConnection.info("all")) 获取rejected_connections。\r\n达到maxclient限制而被拒绝的连接数,出现连续拒绝时应该告警',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Redis',
      ctPeriod: '每1分钟',
      ctSubtype: 'Redis',
      ctIndexDesccn: 'redis集群状态down的节点数',
      threshold: '',
      alarmThreshold: '1',
      algorithm:
        '使用 stringRedisTemplate.execute(redisConnection -> redisConnection.clusterNodes()); 获取集群状态（flags为fail的是down状态)\r\n所有节点状态正常，没有挂掉的节点',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  Kafka: [
    {
      ctType: 'Kafka',
      ctPeriod: '每1分钟',
      ctSubtype: 'Kafka',
      ctIndexDesccn: 'kafka可使用',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '生产一条消息，消费一条消息功能正常',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [5, 1],
      thirdColumnSpan: [5, 1],
    },
    {
      ctType: 'Kafka',
      ctPeriod: '每1分钟',
      ctSubtype: 'Kafka',
      ctIndexDesccn: '内存利用率',
      threshold: '80',
      alarmThreshold: '90',
      algorithm: 'ps aux | grep ${工程名} 获取%CPU和RSS',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Kafka',
      ctPeriod: '每1分钟',
      ctSubtype: 'Kafka',
      ctIndexDesccn: 'CPU利用率',
      threshold: '80',
      alarmThreshold: '90',
      algorithm: 'ps aux | grep ${工程名} 获取%CPU和RSS',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Kafka',
      ctPeriod: '每1分钟',
      ctSubtype: 'Kafka',
      ctIndexDesccn: '堆积消息数',
      threshold: '5000',
      alarmThreshold: '10000',
      algorithm: `使用Kafka自带工具命令获取：\r\ncd /{Kafka_Home}/bin && ./kafka-consumer-groups.sh --bootstrap-server IP:Port --list\r\n解析返回，获取LAG值。最后统计每个Topic的消息堆积量。`,
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Kafka',
      ctPeriod: '每1分钟',
      ctSubtype: 'Kafka',
      ctIndexDesccn: 'kafka集群状态down的节点数',
      threshold: '',
      alarmThreshold: '1',
      algorithm: `// bootstrapServers为当前DC环境中的集群节点信息，如：***********:9092,***********:9092,***********:9092\r\n
      private int loadClusterDownNum(String bootstrapServers) {\r\n
        int clusterSize = bootstrapServers.split(",").length;\r\n
        Properties props = new Properties();\r\n
        props.put("bootstrap.servers", bootstrapServers);\r\n
        int downNum = clusterSize;\r\n
        try (AdminClient adminClient = KafkaAdminClient.create(props)) {\r\n
          int availableSize = adminClient.describeCluster(new DescribeClusterOptions().timeoutMs(1000)).nodes().get().size();\r\n
          downNum = availableSize >= clusterSize ? 0 : clusterSize - availableSize;\r\n
          } catch (Exception e) {\r\n
            e.printStackTrace();\r\n
            }\r\n
            return downNum;\r\n
      }`,
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  Mongo: [
    {
      ctType: 'Mongo',
      ctPeriod: '每1分钟',
      ctSubtype: 'Mongo',
      ctIndexDesccn: 'mongo可使用',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '可正常登录',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [7, 1],
      thirdColumnSpan: [7, 1],
    },
    {
      ctType: 'Mongo',
      ctPeriod: '每1分钟',
      ctSubtype: 'Mongo',
      ctIndexDesccn: '数据库占用空间',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '登录mongodb，执行“db.stats()”命令获取，其中storageSize为数据库占用的总存储空间大小（单位：字节）',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Mongo',
      ctPeriod: '每1分钟',
      ctSubtype: 'Mongo',
      ctIndexDesccn: '已使用连接数',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '登录mongodb，执行“db.serverStatus().connections;”命令获取，其中current为已建立的连接数，available为可用的连接数\r\n总连接数，指当前集群 proxy 收到的连接数',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Mongo',
      ctPeriod: '每1分钟',
      ctSubtype: 'Mongo',
      ctIndexDesccn: '剩余可用连接数',
      threshold: '50',
      alarmThreshold: '20',
      algorithm:
        '登录mongodb，执行“db.serverStatus().connections;”命令获取，其中current为已建立的连接数，available为可用的连接数\r\n总连接数，指当前集群 proxy 收到的连接数',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Mongo',
      ctPeriod: '每1分钟',
      ctSubtype: 'Mongo',
      ctIndexDesccn: '已使用内存',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '登录mongodb，执行“db.serverStatus().mem;”命令，获取resident字段值，即当前进程使用的物理内存量（单位：MB)',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Mongo',
      ctPeriod: '每1分钟',
      ctSubtype: 'Mongo',
      ctIndexDesccn: '每分钟请求次数',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '登录mongodb，执行“db.serverStatus().network.numRequests;”获取当前累计请求次数。然后与上一次获取的值计算差值，计算每分钟请求次数。\r\n每秒操作数，包含 CRUD 操作',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Mongo',
      ctPeriod: '每1分钟',
      ctSubtype: 'Mongo',
      ctIndexDesccn: '慢查询次数',
      threshold: '5',
      alarmThreshold: '10',
      algorithm:
        '根据架构中的NosqlAdapterCBB获取到执行MongoApi执行>3秒的接口名和次数',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  Nginx: [
    {
      ctType: 'Nginx',
      ctPeriod: '每1分钟',
      ctSubtype: 'Nginx',
      ctIndexDesccn: 'nginx可使用',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '开启stub_status ，通过nginx本地访问是否成功',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [4, 1],
      thirdColumnSpan: [4, 1],
    },
    {
      ctType: 'Nginx',
      ctPeriod: '每1分钟',
      ctSubtype: 'Nginx',
      ctIndexDesccn: '已使用连接数',
      threshold: '',
      alarmThreshold: '',
      algorithm: '开启stub_status ，通过nginx本地访问获取Active connections值',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Nginx',
      ctPeriod: '每1分钟',
      ctSubtype: 'Nginx',
      ctIndexDesccn: '剩余可用连接数',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '使用最大连接数-已使用连接数，最大连接数使用worker_connections的值',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'Nginx',
      ctPeriod: '每1分钟',
      ctSubtype: 'Nginx',
      ctIndexDesccn: 'nginx请求数',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '开启stub_status ，通过nginx本地访问获取requests值，使用本次采集数据-上次采集数据',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  'OSS/S3': [
    {
      ctType: 'OSS/S3',
      ctPeriod: '每天',
      ctSubtype: 'OSS/S3',
      ctIndexDesccn: '桶可使用',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '可以正常登录连接上OSS',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [3, 1],
      thirdColumnSpan: [3, 1],
    },
    {
      ctType: 'OSS/S3',
      ctPeriod: '每天',
      ctSubtype: 'OSS/S3',
      ctIndexDesccn: '桶已使用存储容量',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '连接上OSS，使用SDK接口ossClient.getBucketStat(桶名); 获取总存储容量\r\nS3的使用sdk接口进行遍历统计，比较耗时',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'OSS/S3',
      ctPeriod: '每天',
      ctSubtype: 'OSS/S3',
      ctIndexDesccn: '桶已使用存储对象数',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '连接上OSS，使用SDK接口ossClient.getBucketStat(桶名); 获取Object数量。\r\nS3的使用sdk接口进行遍历统计，比较耗时',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  Https域名: [
    {
      ctType: 'Https域名',
      ctPeriod: '每天',
      ctSubtype: '域名',
      ctIndexDesccn: '域名可使用',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '能否ping通各域名',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [2, 1],
      thirdColumnSpan: [2, 1],
    },
    {
      ctType: 'Https域名',
      ctPeriod: '每天',
      ctSubtype: '域名',
      ctIndexDesccn: '有效剩余时间',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '访问域名获取证书有效剩余时间，参考样例代码：OdsHttpsCertConsumer 域名有效剩余时间',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  证书: [
    {
      ctType: '证书',
      ctPeriod: '每天',
      ctSubtype: '离线推送证书',
      ctIndexDesccn: '有效剩余时间',
      threshold: '',
      alarmThreshold: '',
      algorithm: `Java的KeyStore.load证书文件，通过Certificate的getNotAfter获取过期日期。
    \r\n离线推送证书路径：/nfs/nfs_client_data/customer_data/cert
    \r\n离线推送证书有效剩余时间`,
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [1, 1],
      thirdColumnSpan: [1, 1],
    },
  ],
  NatServer: [
    {
      ctType: 'NatServer',
      ctPeriod: '每30秒',
      ctSubtype: 'NatServer',
      ctIndexDesccn: '客户端通过sn请求连接的数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [14, 1],
      thirdColumnSpan: [14, 1],
    },
    {
      ctType: 'NatServer',
      ctPeriod: '每30秒',
      ctSubtype: 'NatServer',
      ctIndexDesccn: '客户端通过datoken请求连接的数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'NatServer',
      ctPeriod: '每30秒',
      ctSubtype: 'NatServer',
      ctIndexDesccn: '客户端掉线数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'NatServer',
      ctPeriod: '每30秒',
      ctSubtype: 'NatServer',
      ctIndexDesccn: '客户端上线数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'NatServer',
      ctPeriod: '每30秒',
      ctSubtype: 'NatServer',
      ctIndexDesccn: '客户端注册数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'NatServer',
      ctPeriod: '每30秒',
      ctSubtype: 'NatServer',
      ctIndexDesccn: '设备端大心跳数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'NatServer',
      ctPeriod: '每30秒',
      ctSubtype: 'NatServer',
      ctIndexDesccn: '设备端掉线数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'NatServer',
      ctPeriod: '每30秒',
      ctSubtype: 'NatServer',
      ctIndexDesccn: '设备端上线数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'NatServer',
      ctPeriod: '每30秒',
      ctSubtype: 'NatServer',
      ctIndexDesccn: '设备端注册数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'NatServer',
      ctPeriod: '每30秒',
      ctSubtype: 'NatServer',
      ctIndexDesccn: '设备端小心跳数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'NatServer',
      ctPeriod: '每30秒',
      ctSubtype: 'NatServer',
      ctIndexDesccn: '设备端心跳协商数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'NatServer',
      ctPeriod: '每30秒',
      ctSubtype: 'NatServer',
      ctIndexDesccn: '30秒设备上线数量降低数',
      threshold: '1000',
      alarmThreshold: '2000',
      algorithm:
        '设备上线数量降低数，上次采集数据-本次采集数据的数量，超过阈值告警',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'NatServer',
      ctPeriod: '每30秒',
      ctSubtype: 'NatServer',
      ctIndexDesccn: '60秒设备上线数量降低数',
      threshold: '1000',
      alarmThreshold: '2000',
      algorithm:
        '设备上线数量降低数，60秒前采集数据-本次采集数据的数量，超过阈值告警',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'NatServer',
      ctPeriod: '每30秒',
      ctSubtype: 'NatServer',
      ctIndexDesccn: '120秒设备上线数量降低数',
      threshold: '1000',
      alarmThreshold: '2000',
      algorithm:
        '设备上线数量降低数，120秒前采集数据-本次采集数据的数量，超过阈值告警',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  RedirectServer: [
    {
      ctType: 'RedirectServer',
      ctPeriod: '每30秒',
      ctSubtype: 'RedirectServer',
      ctIndexDesccn: '客户端重定向数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [6, 1],
      thirdColumnSpan: [6, 1],
    },
    {
      ctType: 'RedirectServer',
      ctPeriod: '每30秒',
      ctSubtype: 'RedirectServer',
      ctIndexDesccn: '客户端重定向请求服务器全列表数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'RedirectServer',
      ctPeriod: '每30秒',
      ctSubtype: 'RedirectServer',
      ctIndexDesccn: '设备端重定向数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'RedirectServer',
      ctPeriod: '每30秒',
      ctSubtype: 'RedirectServer',
      ctIndexDesccn: '30秒设备端重定向新增数',
      threshold: '1000',
      alarmThreshold: '2000',
      algorithm:
        '设备端重定向30秒增量，本次采集数据-上次采集数据的新增数量，超过阈值告警',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'RedirectServer',
      ctPeriod: '每30秒',
      ctSubtype: 'RedirectServer',
      ctIndexDesccn: '60秒设备端重定向新增数',
      threshold: '1000',
      alarmThreshold: '2000',
      algorithm:
        '设备端重定向60秒增量，本次采集数据-60秒以前采集数据的新增数量，超过阈值告警',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'RedirectServer',
      ctPeriod: '每30秒',
      ctSubtype: 'RedirectServer',
      ctIndexDesccn: '120秒设备端重定向新增数',
      threshold: '1000',
      alarmThreshold: '2000',
      algorithm:
        '设备端重定向120秒增量，本次采集数据-120秒以前采集数据的新增数量，超过阈值告警',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  RelayServer: [
    {
      ctType: 'RelayServer',
      ctPeriod: '每30秒',
      ctSubtype: 'RelayServer',
      ctIndexDesccn: '客户端请求转发数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [5, 1],
      thirdColumnSpan: [5, 1],
    },
    {
      ctType: 'RelayServer',
      ctPeriod: '每30秒',
      ctSubtype: 'RelayServer',
      ctIndexDesccn: '设备端请求转发数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'RelayServer',
      ctPeriod: '每30秒',
      ctSubtype: 'RelayServer',
      ctIndexDesccn: '转发连接数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'RelayServer',
      ctPeriod: '每30秒',
      ctSubtype: 'RelayServer',
      ctIndexDesccn: '转发连接结束的数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'RelayServer',
      ctPeriod: '每30秒',
      ctSubtype: 'RelayServer',
      ctIndexDesccn: '设备端转发限速的连接数量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '通过monitor日志采集',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  WebSocket: [
    {
      ctType: 'Websocket',
      ctPeriod: '每30秒',
      ctSubtype: 'Websocket',
      ctIndexDesccn: 'NAT和设备服务的心跳正常',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '心跳断开发送告警',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [2, 1],
      thirdColumnSpan: [2, 1],
    },
    {
      ctType: 'Websocket',
      ctPeriod: '每30秒',
      ctSubtype: 'Websocket',
      ctIndexDesccn: 'NAT和设备服务的断开次数',
      threshold: '3',
      alarmThreshold: '5',
      algorithm: '当天累计断开次数超过阈值告警',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  dpikey: [
    {
      ctType: 'dpikey',
      ctPeriod: '每1分钟',
      ctSubtype: 'dpikey',
      ctIndexDesccn: 'dpikey下发',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '下发失败或是获取失败告警',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [3, 1],
      thirdColumnSpan: [3, 1],
    },
    {
      ctType: 'dpikey',
      ctPeriod: '每1分钟',
      ctSubtype: 'dpikey',
      ctIndexDesccn: 'dpikey异常日志',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '在使用dpikey认证发生dpikey异常时告警',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: 'dpikey',
      ctPeriod: '每1分钟',
      ctSubtype: 'dpikey',
      ctIndexDesccn: 'dpikey异常日志次数',
      threshold: '',
      alarmThreshold: '',
      algorithm: '每分钟异常次数',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  邮件: [
    {
      ctType: '邮件',
      ctPeriod: '每1分钟',
      ctSubtype: '邮件',
      ctIndexDesccn: '邮件发送失败存在异常告警',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '发送邮件失败，发送告警',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [6, 1],
      thirdColumnSpan: [6, 1],
    },
    {
      ctType: '邮件',
      ctPeriod: '每1分钟',
      ctSubtype: '邮件',
      ctIndexDesccn: '邮件发送超出频率告警',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '发送邮件超出频率控制，发送告警',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '邮件',
      ctPeriod: '每1分钟',
      ctSubtype: '邮件',
      ctIndexDesccn: '邮件发送失败量',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '按照自然分钟数统计发送邮件失败的总数量，调用发送邮件接口出现异常',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '邮件',
      ctPeriod: '每1分钟',
      ctSubtype: '邮件',
      ctIndexDesccn: '邮件发送超出频率次数',
      threshold: '',
      alarmThreshold: '',
      algorithm: '每分钟邮件发送超出频率次数',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '邮件',
      ctPeriod: '每1分钟',
      ctSubtype: '邮件',
      ctIndexDesccn: '邮件发送总量',
      threshold: '',
      alarmThreshold: '',
      algorithm: '按照自然分钟数统计发送邮件的数量',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '邮件',
      ctPeriod: '每1分钟',
      ctSubtype: '邮件',
      ctIndexDesccn: '邮件发送的TPS的峰值',
      threshold: '',
      alarmThreshold: '',
      algorithm: '取每分钟内的TPS峰值',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  短信: [
    {
      ctType: '短信',
      ctPeriod: '每1分钟',
      ctSubtype: '短信',
      ctIndexDesccn: '短信发送失败存在异常告警',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '发送短信失败，发送告警',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [6, 1],
      thirdColumnSpan: [6, 1],
    },
    {
      ctType: '短信',
      ctPeriod: '每1分钟',
      ctSubtype: '短信',
      ctIndexDesccn: '短信发送超出频率控制告警',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '发送短信超出频率控制，发送告警',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '短信',
      ctPeriod: '每1分钟',
      ctSubtype: '短信',
      ctIndexDesccn: '每分钟短信发送失败存在异常次数',
      threshold: '',
      alarmThreshold: '',
      algorithm:
        '按照自然分钟数统计发送短信失败的总数量，调用发送短信接口出现异常',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '短信',
      ctPeriod: '每1分钟',
      ctSubtype: '短信',
      ctIndexDesccn: '每分钟短信发送超出频率次数',
      threshold: '',
      alarmThreshold: '',
      algorithm: '每分钟短信发送超出频率次数',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '短信',
      ctPeriod: '每1分钟',
      ctSubtype: '短信',
      ctIndexDesccn: '每分钟短信发送总次数',
      threshold: '',
      alarmThreshold: '',
      algorithm: '按照自然分钟数统计发送短信的数量',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '短信',
      ctPeriod: '每1分钟',
      ctSubtype: '短信',
      ctIndexDesccn: '每分钟发送短信的TPS的峰值',
      threshold: '',
      alarmThreshold: '',
      algorithm: '取每分钟内的TPS峰值',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  账号: [
    {
      ctType: '账号',
      ctPeriod: '每1分钟',
      ctSubtype: '账号',
      ctIndexDesccn: '账号注册过程发生异常告警',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '账号注册过程发生异常',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [6, 1],
      thirdColumnSpan: [6, 1],
    },
    {
      ctType: '账号',
      ctPeriod: '每1分钟',
      ctSubtype: '账号',
      ctIndexDesccn: '账号登录过程发生异常告警',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '账号登录过程发生异常',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '账号',
      ctPeriod: '每1分钟',
      ctSubtype: '账号',
      ctIndexDesccn: '每分钟账号注册异常数',
      threshold: '',
      alarmThreshold: '',
      algorithm: '按照自然分钟数统计注册账号异常的数量',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '账号',
      ctPeriod: '每1分钟',
      ctSubtype: '账号',
      ctIndexDesccn: '每分钟账号登录异常数',
      threshold: '',
      alarmThreshold: '',
      algorithm: '按照自然分钟数统计账号登录异常的数量',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '账号',
      ctPeriod: '每1分钟',
      ctSubtype: '账号',
      ctIndexDesccn: '注册账号数',
      threshold: '',
      alarmThreshold: '',
      algorithm: '按照自然分钟数统计注册账号的数量',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '账号',
      ctPeriod: '每1分钟',
      ctSubtype: '账号',
      ctIndexDesccn: '登录账号次数',
      threshold: '',
      alarmThreshold: '',
      algorithm: '按照自然分钟数统计账号登录的数量',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
  '推送2.0': [
    {
      ctType: '推送2.0',
      ctPeriod: '每1分钟',
      ctSubtype: '推送2.0',
      ctIndexDesccn: '离线推送发送失败存在异常告警',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '推送流程发生异常，发送告警',
      alarmEmail: '',
      alarmSms: '',
      firstColumnSpan: [10, 1],
      thirdColumnSpan: [10, 1],
    },
    {
      ctType: '推送2.0',
      ctPeriod: '每1分钟',
      ctSubtype: '推送2.0',
      ctIndexDesccn: '离线推送超出频率控制告警',
      threshold: '',
      alarmThreshold: 'fail',
      algorithm: '推送超出频率控制，发送告警',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '推送2.0',
      ctPeriod: '每1分钟',
      ctSubtype: '推送2.0',
      ctIndexDesccn: '每分钟离线推送失败次数',
      threshold: '',
      alarmThreshold: '',
      algorithm: '按照自然分钟数统计离线推送失败的次数',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '推送2.0',
      ctPeriod: '每1分钟',
      ctSubtype: '推送2.0',
      ctIndexDesccn: '每分钟离线推送超出频率次数',
      threshold: '',
      alarmThreshold: '',
      algorithm: '按照自然分钟数统计离线推送超出频率限制的次数',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '推送2.0',
      ctPeriod: '每1分钟',
      ctSubtype: '推送2.0',
      ctIndexDesccn: '每分钟离线推送的TPS的峰值',
      threshold: '',
      alarmThreshold: '',
      algorithm: '取每分钟内离线推送的TPS峰值',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '推送2.0',
      ctPeriod: '每1分钟',
      ctSubtype: '推送2.0',
      ctIndexDesccn: '每分钟在线推送失败次数',
      threshold: '',
      alarmThreshold: '',
      algorithm: '按照自然分钟数统计在线推送失败的次数',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '推送2.0',
      ctPeriod: '每1分钟',
      ctSubtype: '推送2.0',
      ctIndexDesccn: '每分钟在线推送总次数',
      threshold: '',
      alarmThreshold: '',
      algorithm: '按照自然分钟数统计在线推送超出频率限制的次数',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '推送2.0',
      ctPeriod: '每1分钟',
      ctSubtype: '推送2.0',
      ctIndexDesccn: '每分钟在线推送的TPS的峰值',
      threshold: '',
      alarmThreshold: '',
      algorithm: '取每分钟内在线推送的TPS峰值',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '推送2.0',
      ctPeriod: '每1分钟',
      ctSubtype: '推送2.0',
      ctIndexDesccn: '每分钟推送总数',
      threshold: '',
      alarmThreshold: '',
      algorithm: '按照自然分钟数统计推送的次数，包含离线推送和在线推送',
      alarmEmail: '',
      alarmSms: '',
    },
    {
      ctType: '推送2.0',
      ctPeriod: '每1分钟',
      ctSubtype: '推送2.0',
      ctIndexDesccn: '每分钟推送的TPS的峰值',
      threshold: '',
      alarmThreshold: '',
      algorithm: '取每分钟内推送的TPS峰值',
      alarmEmail: '',
      alarmSms: '',
    },
  ],
}

export const alarmConfigColumns = [
  {
    label: Vue.prototype.$translate('alarmConfigTab.alarmType'),
    prop: 'ctTypeDesc',
    width: 120,
  },
  // {
  //   label: Vue.prototype.$translate('alarmConfigTab.ctPeriod'),
  //   prop: 'ctPeriodDesc',
  //   width: 100,
  // },
  {
    label: Vue.prototype.$translate('alarmConfigTab.alarmIndexDesc'),
    prop: 'ctSubtype',
    width: 110,
  },
  {
    label: Vue.prototype.$translate('alarmConfigTab.ctIndexDesccn'),
    prop: 'ctIndexFieldName',
    width: 130,
  },
  {
    label: Vue.prototype.$translate('alarmConfigTab.threshold'),
    prop: 'threshold',
    width: 100,
  },
  {
    label: Vue.prototype.$translate('alarmConfigTab.alarmThreshold'),
    prop: 'alarmThreshold',
    width: 110,
  },
  {
    label: Vue.prototype.$translate('alarmConfigTab.algorithm'),
    prop: 'algorithm',
    'min-width': 380,
  },
  {
    label: Vue.prototype.$translate('alarmConfigTab.alarmEmail'),
    prop: 'alarmEmail',
    width: 140,
  },
  {
    label: Vue.prototype.$translate('alarmConfigTab.alarmSms'),
    prop: 'alarmSms',
    width: 140,
  },
  {
    label: Vue.prototype.$translate('operation'),
    prop: 'operate',
    slotName: 'bodyCell',
    width: 100,
  },
]

export const alarmHistoryColumns = [
  {
    label: Vue.prototype.$translate('alarmConfigTab.alarmType'),
    prop: 'monitorType',
    slotName: 'bodyCell',
    width: lang === 'zh-CN' ? 130 : 150,
    unSearchAble: true,
  },
  {
    label: Vue.prototype.$translate('alarmConfigTab.alarmIndexDesc'),
    prop: 'ctIndexField',
    slotName: 'bodyCell',
    width: lang === 'zh-CN' ? 150 : 170,
    unSearchAble: true,
  },
  {
    label: Vue.prototype.$translate('alarmHistoryTab.host'),
    prop: 'host',
    width: lang === 'zh-CN' ? 150 : 150,
  },
  {
    label: Vue.prototype.$translate('alarmHistoryTab.ip'),
    prop: 'ip',
    width: 110,
  },
  {
    label: Vue.prototype.$translate('alarmHistoryTab.dcName'),
    prop: 'dcName',
    width: lang === 'zh-CN' ? 80 : 100,
    unSearchAble: true,
  },
  {
    label: Vue.prototype.$translate('natTab.clusterName'),
    prop: 'clusterName',
    width: lang === 'zh-CN' ? 100 : 140,
  },
  {
    label: Vue.prototype.$translate('microServeTab.projectName'),
    prop: 'projectName',
    'min-width': 150,
  },
  {
    label: Vue.prototype.$translate('microServeTab.instanceId'),
    prop: 'instanceId',
    width: lang === 'zh-CN' ? 80 : 120,
  },
  {
    label: Vue.prototype.$translate('alarmHistoryTab.threshold'),
    prop: 'threshold',
    width: lang === 'zh-CN' ? 80 : 130,
  },
  {
    label: Vue.prototype.$translate('alarmHistoryTab.actual'),
    prop: 'actual',
    width: lang === 'zh-CN' ? 80 : 120,
  },
  {
    label: Vue.prototype.$translate('alarmHistoryTab.ct'),
    prop: 'st',
    slotName: 'bodyCell',
    width: 160,
  },
  {
    label: Vue.prototype.$translate('alarmHistoryTab.msg'),
    prop: 'emailMsg',
    'min-width': lang === 'zh-CN' ? 300 : 250,
  },
  {
    label: Vue.prototype.$translate('alarmHistoryTab.sendEmai'),
    prop: 'sendEmailNotice',
    slotName: 'bodyCell',
    width: lang === 'zh-CN' ? 110 : 130,
  },
  {
    label: Vue.prototype.$translate('alarmHistoryTab.sendSms'),
    prop: 'sendSmsNotice',
    slotName: 'bodyCell',
    width: lang === 'zh-CN' ? 110 : 120,
  },
]

export const alarmDataColumns = [
  {
    label: Vue.prototype.$translate('alarmDataTab.systemMonitortType'),
    prop: 'systemMonitortType',
    width: lang === 'zh-CN' ? 140 : 200,
    unSearchAble: true,
  },
  {
    label: Vue.prototype.$translate('alarmConfigTab.alarmType'),
    prop: 'monitorType',
    slotName: 'bodyCell',
    width: 130,
    unSearchAble: true,
  },
  {
    label: Vue.prototype.$translate('configTab.dcName'),
    prop: 'dcName',
    width: 100,
  },
  {
    label: Vue.prototype.$translate('alarmDataTab.alarmObject'),
    prop: 'node',
    'min-width': 120,
  },
  {
    label: Vue.prototype.$translate('alarmDataTab.warnStartTime'),
    prop: 'startTime',
    slotName: 'bodyCell',
    width: 160,
  },
  {
    label: Vue.prototype.$translate('warnTab.warnInfo'),
    prop: 'alertMsg',
    'min-width': 200,
  },
]

// 资源层(resource)，应用层(application)、业务层(business)、P2P(p2p)指标分类
export const ctyTypeClassify = {
  // 资源层
  monitor_host: 'resource',
  monitor_disk: 'resource',
  monitor_process: 'resource',
  monitor_domain: 'resource',
  monitor_cert: 'resource',
  // 应用层
  monitor_microservice: 'application',
  monitor_microservice_java: 'application',
  monitor_microservice_cplus: 'application',
  monitor_microservice_web: 'application',
  monitor_mysql: 'application',
  monitor_mysql_instance: 'application',
  monitor_mysql_db: 'application',
  monitor_redis: 'application',
  monitor_kafka: 'application',
  monitor_kafka_node: 'application',
  monitor_kafka_cluster: 'application',
  monitor_mongo: 'application',
  monitor_nginx: 'application',
  monitor_oss: 'application',
  monitor_queue: 'application',
  monitor_apache: 'application',
  monitor_wsConnect: 'application',
  // 业务层
  monitor_email: 'business',
  monitor_sms: 'business',
  monitor_account: 'business',
  monitor_push: 'business',
  monitor_video_flow: 'business',
  monitor_websocket: 'business',
  monitor_devopswebsocket: 'business',
  monitor_dpikey: 'business',
  // P2P
  monitor_nat: 'p2p',
  monitor_udt: 'p2p',
  monitor_relay: 'p2p',
  monitor_stun: 'p2p',
  monitor_access: 'p2p',
  monitor_stunRelay: 'p2p',
  monitor_nat_cluster: 'p2p',
  monitor_relay_cluster: 'p2p',
  monitor_stun_cluster: 'p2p',
  monitor_stun_relay_cluster: 'p2p',
}
