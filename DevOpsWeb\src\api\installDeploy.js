import request from '@/api/request'
const baseUrl = '/dev-ops'

// 获取环境与DC名称
export const getEnvDcName = (data) =>
  request.post(`${baseUrl}/deploy/env-info`, data)

/*
部署资源规划
*/

// 删除环境
export const envDelete = (data) =>
  request.post(`${baseUrl}/resource-plan/env/delete`, data)

// 分页获取资源规划列表
export const resList = (data) =>
  request.post(`${baseUrl}/deploy/plan-list`, data)

// 新增资源规划
export const resAdd = (data) =>
  request.post(`${baseUrl}/resource-plan/res/add`, data)

// 删除资源规划
export const resDelete = (data) =>
  request.post(`${baseUrl}/resource-plan/res/delete`, data)

// 获取集群统计信息
export const getQuotaSummary = (data) =>
  request.post(`${baseUrl}/monitor/dc-summary`, data)

// 部署补丁包
export const deployPatch = (data) =>
  request.post(`${baseUrl}/deploy/deploy-patch`, data)

/*
部署包版本配套
*/

// 分页获取版本配套列表
export const serviceVersionList = (data) =>
  request.post(`${baseUrl}/service-version/list`, data)

// 归档包
export const serviceVersionPlace = (data) =>
  request.post(`${baseUrl}/deploy/save-tag`, data)

// 获取包标签接口
export const getPackageTagList = (data, signal) =>
  request.post(`${baseUrl}/deploy/tag-name-list`, data, { signal })

// 获取部署配套选择项
export const getOssPackage = (data, signal) =>
  request.post(`${baseUrl}/deploy/oss-package-list`, data, { signal })

// 分页获取版本配套列表-新
export const getVersionMatchList = (data) =>
  request.post(`${baseUrl}/deploy/tag-list`, data)

// 获取版本配套表详情
export const getPackageDetail = (data) =>
  request.post(`${baseUrl}/service-version/package-detail`, data)

// 版本配套删除
export const deleteVersionMatch = (data) =>
  request.post(`${baseUrl}/deploy/delete-tag`, data)

/*
部署规划
*/

// 获取待部署服务列表
export const deployTaskServiceList = (data, signal) =>
  request.post(`${baseUrl}/deploy/todeploy-list`, data, { signal })

// 加入部署前进行IP的联调性检测
export const checkPreDeploy = (data) =>
  request.post(`${baseUrl}/deploy/check-pre-deploy`, data)

// 分页获取部署任务列表
export const deployTaskTaskList = (data) =>
  request.post(`${baseUrl}/deploy/task-list`, data)

// 批量执行部署
export const excuteDeployTask = (data) =>
  request.post(`${baseUrl}/deploy/execute-deploy`, data)

// 批量删除
export const deployTaskDelete = (data) =>
  request.post(`${baseUrl}/deploy-task/delete`, data)

/*
服务状态
*/

// 获取微服务状态列表
export const serviceStatusList = (data) =>
  request.post(`${baseUrl}/service-status/list`, data)

// 微服务状态列表
export const serviceStatusOperate = (data) =>
  request.post(`${baseUrl}/deploy/service-operate`, data)

/**
 * 部署环境资源规划 主机初始化
 */
// 获取主机初始化列表接口
export const getInitHostList = (data) =>
  request.post(`${baseUrl}/deploy/host-list`, data)

// 主机初始化接口
export const excuteInitHost = (data) =>
  request.post(`${baseUrl}/deploy/host-init`, data)

// 导入部署模板
export const importTemplate = (data) =>
  request.post(`${baseUrl}/deploy/template-import`, data)

// 配置参数管理
export const getBusParamList = (data) =>
  request.post(`${baseUrl}/config/datasource-list`, data)

// 配置参数更新
export const updateBusParam = (data) =>
  request.post(`${baseUrl}/config/datasource-edit`, data)

// 配置参数发布
export const publishBusParam = (data) =>
  request.post(`${baseUrl}/config/datasource-publish`, data)

/**
 * 部署P2P Web
 */

// 上传文件
export const uploadP2PWebPackage = (data) =>
  request.post(`${baseUrl}/deploy/upload-p2pweb-package`, data)

// 获取P2P Web部署列表
export const getP2PDeployList = (data) =>
  request.post(`${baseUrl}/deploy/todeploy-p2pweb-list`, data)

// 部署P2P Web
export const excuteP2PDeploy = (data) =>
  request.post(`${baseUrl}/deploy/deploy-p2pweb`, data)
