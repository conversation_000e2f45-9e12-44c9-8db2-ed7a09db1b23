const path = require('path')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')

const devMode = process.env.NODE_ENV !== 'production'

exports.cssLoader = {
  test: /\.(sa|sc|c)ss$/,
  use: [
    // Creates `style` nodes from JS strings
    devMode ? 'style-loader' : MiniCssExtractPlugin.loader,
    // Translates CSS into CommonJS
    'css-loader',
    'postcss-loader',
    // Compiles Sass to CSS
    {
      loader: 'sass-loader',
      options: {
        additionalData(source, loaderContext) {
          const { resourcePath, rootContext } = loaderContext
          const absolutePath = path.resolve(rootContext, 'src/theme/vars.scss')

          // All scss files ending with imports.scss
          // will not re-import additionalData
          if (resourcePath === absolutePath) return source
          // Use additionalData from legacy nuxt scss options
          return `@import 'theme/vars.scss'; ${source}`
        },
        // additionalData: "@import 'theme/vars.scss';",
        sassOptions: {
          includePaths: [path.resolve(__dirname, '../src')],
        },
      },
    },
  ],
}

exports.alias = {}
