<!--
  * 树形下拉框（基于elementui二次封装）
  * <AUTHOR>
  * @version 1.0
  * @datetime 2022/06/21
  * 使用方式：1.目标项目引入tvtcloudbaiscwidget架包   2.目标项目挂载插件：Vue.use(tvtcloudbaiscwidget) 3.注册方法同Vue组件注册一致
  * Props:
  *  id：组件唯一标识
  *  label: 选择框 label
  *  required: 是否必填（嵌入表单时传）
  *  options: 下拉列表 （不传options，渲染自定义插槽内容）
  *  其他参数同element一致
-->
<template>
  <div
    :id="id"
    ref="tvtSelectTreeWrapperRef"
    class="tvt-select"
    :style="cssVars"
    :class="[
      isFocus ? 'isFocus' : '',
      size ? `tvt-select-${size}` : '',
      isOutlined ? 'isOutlined' : '',
      !label ? '' : 'has-label',
    ]"
  >
    <div class="tvt-select-inner">
      <div v-if="!label && !prefix" class="tvt-select-prefix">
        <slot name="prefix" />
      </div>
      <span v-if="!label && prefix" class="tvt-select-prefix"
        >{{ prefix }} :</span
      >
      <div class="input-box">
        <label class="label" :for="labelForId">
          <div
            :class="[
              'label-text-wrapper',
              size ? `label-text-wrapper-${size}` : '',
            ]"
          >
            <span :class="['label-text', required ? 'required' : '']">{{
              label
            }}</span>
          </div>
        </label>
        <el-select
          :id="labelForId"
          ref="tvtSelectTreeRef"
          v-model="innerValue"
          v-bind="$attrs"
          :placeholder="label ? '' : $attrs.placeholder"
          :popper-append-to-body="false"
          :multiple="multiple"
          :filter-method="handleFilter"
          v-on="$listeners"
          @clear="clearHandle"
          @focus="handleFocus"
          @blur="handleBlur"
          @change="handleChange"
          @visible-change="handleVisibleChange"
        >
          <el-option
            v-for="item in optionData(options)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            style="display: none"
          />
          <!-- 多选树 -->
          <el-tree
            v-if="multiple"
            id="tree-option"
            :ref="`${id}-tree`"
            class="multi-select-el-tree"
            :data="treeOptions"
            show-checkbox
            :node-key="defaultProps.value"
            :default-expand-all="defaultExpandAll"
            :default-expanded-keys="defaultExpandedKeys"
            :props="defaultProps"
            :filter-node-method="filterNode"
            @check-change="handleCheckChange"
          >
            <span slot-scope="{ data }" class="custom-tree-node">
              <span :title="data[defaultProps.label]">{{
                data[defaultProps.label]
              }}</span>
            </span>
          </el-tree>
          <!-- 单选树 -->
          <el-tree
            v-else
            id="tree-option"
            :ref="`${id}-tree`"
            class="single-select-el-tree"
            :data="treeOptions"
            :node-key="defaultProps.value"
            :default-expand-all="defaultExpandAll"
            :default-expanded-keys="defaultExpandedKeys"
            :props="defaultProps"
            highlight-current
            :filter-node-method="filterNode"
            @node-click="handleNodeClick"
          >
            <span slot-scope="{ data }" class="custom-tree-node">
              <span
                :class="{
                  'active-tree-node': data[defaultProps.value] === value,
                }"
                :title="data[defaultProps.label]"
                >{{ data[defaultProps.label] }}</span
              >
            </span>
          </el-tree>
        </el-select>
        <fieldset class="tvt-field-set">
          <legend>
            <span>{{ label }}</span>
          </legend>
        </fieldset>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'TvtSelectTree',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    id: {
      type: String,
      default: 'tvtSelectTree' + Date.now() + Math.random(),
    },
    options: {
      // 下拉框选项
      type: Array,
      default: () => [],
    },
    label: {
      type: String,
      default: '',
    },
    value: {
      // 双向绑定的值
      type: [String, Number, Array],
      default: null,
    },
    required: {
      // 是否必填
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    defaultProps: {
      type: Object,
      default: () => ({
        label: 'label',
        value: 'value',
        children: 'children',
      }),
    },
    prefix: {
      type: String,
      default: '',
    },
    defaultExpandAll: {
      // 是否默认展开所有树节点
      type: Boolean,
      default: false,
    },
    defaultExpandedKeys: {
      // 默认展开的节点的 key 的数组
      type: Array,
      default: () => [],
    },
    format: {
      type: Object,
      default: null,
    },
    fitDropdownWidth: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      labelForId: `tvt-select-tree_${this._uid}`, // 当前组件唯一id值
      isFocus: false, // 是否聚焦
      innerValue: null,
      size: '',
      isVisible: false, // 下拉框显示/隐藏
      reference: null,
      cssVars: {
        '--popper-min-wdith': '0px',
        '--popper-left': '0px',
      },
      filterText: null, // 过滤时的输入,
      selectKey: null, // 单选树中选中的key
    }
  },
  computed: {
    treeOptions() {
      return this.options
    },
    isOutlined() {
      // console.log('this.label', this.label, 'this.isVisible', this.isVisible, 'this.multiple', this.multiple, 'this.innerValue', this.innerValue, 'this.isFocus')
      if (!this.label) return false
      if (this.isVisible) return true
      if (this.multiple) {
        return this.innerValue && this.innerValue.length ? true : this.isFocus
      }
      return this.innerValue !== '' &&
        this.innerValue !== undefined &&
        this.innerValue !== null
        ? true
        : this.isFocus
    },
    resetInputWidth() {
      if (this.reference) {
        return this.reference.inputWidth
      }
      return 0
    },
  },
  watch: {
    value: {
      handler(val) {
        if (JSON.stringify(val) !== JSON.stringify(this.innerValue)) {
          // 值不一样则更新
          this.$nextTick(() => {
            this.innerValue = val
              ? Array.isArray(val)
                ? val.slice()
                : val
              : null
          })
          if (this.multiple) {
            // 多选
            this.$refs[`${this.id}-tree`] &&
              this.$refs[`${this.id}-tree`].setCheckedKeys(val)
          } else {
            // 单选
            this.$refs[`${this.id}-tree`] &&
              this.$refs[`${this.id}-tree`].setCurrentKey(val)
            this.selectKey = val
          }
        }
      },
      deep: true,
      immediate: true,
    },
    isVisible: {
      handler(val, oldVal) {
        if (!val && oldVal && this.multiple) this.isFocus = false
      },
      immediate: true,
    },
    '$attrs.size': {
      handler(val) {
        this.size = val || ''
      },
      immediate: true,
    },
    resetInputWidth: {
      handler(val) {
        if (this.reference) {
          this.reference.popperElm.style.width = val + 'px'
        }
      },
      deep: true,
    },
    filterText(val) {
      this.$refs[`${this.id}-tree`].filter(val)
    },
  },
  mounted() {
    if (this.value && this.$refs[`${this.id}-tree`]) {
      if (this.multiple) {
        this.$refs[`${this.id}-tree`].setCheckedKeys(this.value)
      } else {
        this.$refs[`${this.id}-tree`].setCurrentKey(this.value)
        this.selectKey = this.value
      }
    }
    this.$nextTick(() => {
      if (this.fitDropdownWidth) {
        // 弹窗中的下拉框宽度不能超过输入框
        this.reference = this.$refs.tvtSelectTreeRef
      }
    })
  },
  methods: {
    /**
     * 树形转平面的迭代方法
     * option 1的el-option需要此方法绑定数据
     */
    optionData(array, result = []) {
      array.forEach((item) => {
        result.push({
          label: item[this.defaultProps.label],
          value: item[this.defaultProps.value],
        })
        if (
          item[this.defaultProps.children] &&
          item[this.defaultProps.children].length !== 0
        ) {
          this.optionData(item[this.defaultProps.children], result)
        }
      })
      return JSON.parse(JSON.stringify(result))
    },
    // 获取焦点--解决默认值未高亮问题--单选时用
    handleFocus() {
      this.isFocus = true
      if (!this.multiple) {
        this.$nextTick(() => {
          // console.log('获取焦点时value', this.value)
          this.$refs[`${this.id}-tree`].setCurrentKey(this.value)
        })
      }
      this.$emit('focus')
    },
    // 失去焦点
    handleBlur() {
      // console.log('失去焦点')
      if (this.multiple) {
        if (!this.isVisible) {
          this.isFocus = false
        }
      } else {
        this.isFocus = false
      }
      this.$emit('blur')
    },
    handleChange(val) {
      if (this.multiple) {
        // console.log('选择框变化')
        this.innerValue = val
        // this.$emit('change', val)
        this.$refs[`${this.id}-tree`] &&
          this.$refs[`${this.id}-tree`].setCheckedKeys(val)
      }
      this.$nextTick(() => {
        // 单选切换选项时强制失焦
        if (!this.multiple) {
          this.$refs.tvtSelectTreeRef.blur()
          this.isFocus = false
        } else {
          this.innerValue = val
          // this.$emit('change', val)
          this.$refs[`${this.id}-tree`] &&
            this.$refs[`${this.id}-tree`].setCheckedKeys(val)
        }
      })
    },
    // 点击树形选择节点--多选
    handleCheckChange() {
      // 参数 node, checked, indeterminate
      // const checkedKeys = this.$refs[`${this.id}-tree`].getCheckedKeys(true)
      // console.log('选中节点的key值', checkedKeys)
      const checkedNodes = this.$refs[`${this.id}-tree`].getCheckedNodes(true)
      // console.log('选中节点的Node值', checkedNodes)
      const valueArr = checkedNodes.map((node) => node[this.defaultProps.value])
      this.innerValue = valueArr
      // this.$emit('change', valueArr)
      if (JSON.stringify(valueArr) !== JSON.stringify(this.value)) {
        // 两者值不一样则触发，避免初始化value传入时触发checkChange事件
        this.$emit('change', valueArr)
      }
    },
    // 点击树形选择节点--单选
    handleNodeClick(node) {
      // 参数 node, checked, indeterminate
      // console.log('node, checked', node, checked)
      if (node[this.defaultProps.children]) {
        // 说明是父节点
        // 需要重置选中的节点
        // console.log('currentKey', this.selectKey)
        this.$refs[`${this.id}-tree`].setCurrentKey(this.selectKey)
        return
      }
      this.innerValue = node[this.defaultProps.value]
      this.$refs[`${this.id}-tree`].setCurrentKey(node[this.defaultProps.value])
      this.selectKey = node[this.defaultProps.value]
      this.$emit('change', node[this.defaultProps.value])
      this.$refs.tvtSelectTreeRef.blur() // 失去焦点
      this.isFocus = false
    },
    // 清空所有标签
    clearHandle() {
      if (this.multiple) {
        // 多选
        this.$refs[`${this.id}-tree`].setCheckedKeys([])
        this.innerValue = []
        this.$emit('change', [])
      } else {
        // 单选
        this.$refs[`${this.id}-tree`].setCurrentKey(null)
        this.selectKey = null
        this.innerValue = null
        this.$emit('change', null)
      }
    },
    handleVisibleChange(flag) {
      // console.log('显隐性变化')
      this.isVisible = flag
      this.$emit('visible-change', flag)
      if (flag) {
        this.$nextTick(() => {
          const tvtSelectTree = this.$refs.tvtSelectTreeWrapperRef
          const popBox = tvtSelectTree.querySelector(
            '.el-select-dropdown.el-popper'
          )
          const prefixBox = tvtSelectTree.querySelector('.tvt-select-prefix')
          if (prefixBox) {
            this.cssVars = {
              '--popper-left': `${
                parseFloat(popBox.style.left || 0) - prefixBox.clientWidth
              }px`,
              '--popper-min-width': `${tvtSelectTree.clientWidth}px`,
            }
          } else {
            this.cssVars = {
              '--popper-left': popBox.style.left,
              '--popper-min-width': `${tvtSelectTree.clientWidth}px`,
            }
          }
        })
      } else {
        // 单选切换选项时强制失焦
        if (!this.multiple) {
          this.$refs.tvtSelectTreeRef.blur()
        }
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data[this.defaultProps.label].indexOf(value) !== -1
    },
    handleFilter(query, option) {
      // console.log('过滤字段', query)
      // option始终返回所有，让树去做过滤
      // return option.label.toLowerCase().indexOf(query.toLowerCase()) >= 0
      this.filterText = query
      return option
    },
  },
}
</script>
<style lang="scss" scoped>
.tvt-select {
  &.has-label {
    ::v-deep .el-select__tags {
      padding-top: 6px;
    }
  }
  .tvt-select-inner {
    display: flex;
    align-items: center;
    .tvt-select-prefix {
      padding-left: 5px;
      color: #999;
      user-select: none;
    }
    > .input-box {
      flex: 1;
    }
  }
  .label {
    cursor: pointer;
  }
  ::v-deep .el-select__tags {
    .el-tag {
      max-width: 60%; // 标签超长处理
    }
  }
  ::v-deep .el-select-dropdown.el-popper {
    position: absolute;
    min-width: var(--popper-min-width) !important;
    left: var(--popper-left) !important;
  }
  ::v-deep .single-select-el-tree .el-tree-node__content {
    font-size: 14px;
  }
}
</style>
<style lang="scss">
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  /* background-color: #baf !important; */
  color: #409eff;
}

.el-select .el-tag {
  max-width: 150px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
}

.el-select .el-tag__close.el-icon-close {
  right: 2px;
  position: absolute;
  top: 2px !important;
}

.el-select .el-select__tags {
  cursor: pointer;
}

.el-select .el-tag--small {
  padding: 0px 20px 0px 8px;
}
</style>
