<template>
  <tvt-dialog
    :title="$t('edit')"
    :show.sync="showFlag"
    width="1100px"
    custom-class="alarm-config-dialog"
    :cancel-text="$t('cancel')"
    :submit-text="$t('confirm')"
    :close-on-click-modal="false"
    @close="closeDialog"
    @Cancel="closeDialog"
    @Submit="btnSave"
  >
    <div class="alarm-edit-wrap">
      <el-form
        ref="receiverForm"
        :inline="true"
        :model="alarmConfigForm"
        label-position="left"
        :rules="alarmRules"
        label-width="120px"
      >
        <div class="common-config-wrap">
          <el-row>
            <el-col :span="24">
              <el-form-item
                prop="alarmReceivers"
                :label="$t('alarmConfigTab.alarmReceiver')"
              >
                <el-button
                  type="primary"
                  style="border-radius: 16px; height: 36px"
                  @click="handleAdd"
                  >{{ $t('add') }}</el-button
                >
                <tvt-table
                  ref="tvtTableRef"
                  v-loading="tableLoading"
                  :data="alarmConfigForm.alarmReceivers"
                  :columns="alarmReceiverColumns"
                  :border="true"
                  :border-bottom="true"
                  height="180"
                  style="width: 850px"
                  :header-cell-style="{ 'text-align': 'center' }"
                  :pagination="false"
                >
                  <!--可编辑列-->
                  <template #bodyCell="{ column, $index }">
                    <template v-if="column.type === 'input'">
                      <el-form-item :prop="`${column.prop}_${$index}`">
                        <tvt-input
                          v-model="
                            alarmConfigForm.alarmReceivers[$index][column.prop]
                          "
                          :placeholder="`${
                            column.prop === 'email'
                              ? $t('alarmConfigTab.alarmEmailInput')
                              : $t('alarmConfigTab.alarmSmsInput')
                          }`"
                          maxlength="100"
                          style="width: 255px"
                        />
                      </el-form-item>
                    </template>
                    <template v-else-if="column.type === 'select'">
                      <el-form-item :prop="`${column.prop}_${$index}`">
                        <tvt-select
                          v-model="
                            alarmConfigForm.alarmReceivers[$index][column.prop]
                          "
                          :options="langOptions"
                          :placeholder="`${$t(
                            'alarmConfigTab.alarmLangSelect'
                          )}`"
                          clearable
                          style="width: 100%"
                        />
                      </el-form-item>
                    </template>
                    <template v-else>
                      <el-button
                        type="danger"
                        style="border-radius: 16px; height: 36px"
                        @click="handleDelete($index)"
                        >{{ $t('delete') }}</el-button
                      >
                    </template>
                  </template>
                </tvt-table>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <div slot="footer" class="alarm-dialog-footer">
      <el-button size="medium" @click="closeDialog">{{
        $t('cancel')
      }}</el-button>
      <el-button
        size="medium"
        :loading="loading"
        type="primary"
        @click="btnSave"
        >{{ $t('confirm') }}</el-button
      >
    </div>
  </tvt-dialog>
</template>
<script>
import { langOptions } from '@/lang'
export default {
  name: 'AlarmConfigEdit',
  data() {
    return {
      langOptions,
      alarmConfigForm: {
        alarmReceivers: [],
      },
      alarmRules: {
        alarmReceivers: [
          {
            required: true,
            message: this.$t('alarmConfigTab.alarmEmailInput'),
            trigger: 'blur',
          },
          { validator: this.validateEamil, trigger: 'blur,change' },
        ],
      },
      alarmReceiverColumns: [
        {
          label: this.$t('alarmConfigTab.alarmEmail'),
          prop: 'email',
          slotName: 'bodyCell',
          type: 'input',
          width: 274,
        },
        {
          label: this.$t('alarmConfigTab.alarmSms'),
          prop: 'sms',
          slotName: 'bodyCell',
          type: 'input',
          width: 274,
        },
        {
          label: this.$t('alarmConfigTab.alarmLang'),
          prop: 'lang',
          slotName: 'bodyCell',
          type: 'select',
          width: 200,
        },
        {
          label: this.$t('operation'),
          prop: 'operation',
          slotName: 'bodyCell',
          width: 100,
        },
      ],
      tableLoading: false,
      showFlag: false,
      loading: false,
    }
  },
  methods: {
    validateEamil(rule, value, callback) {
      const { alarmReceivers = [] } = this.alarmConfigForm
      const filterReceivers = alarmReceivers.filter(
        (item) => item.email || item.lang
      )
      if (filterReceivers.length === 0) {
        callback(new Error(this.$t('alarmConfigTab.alarmEmailInput')))
        return
      }
      let flag = true
      // 判断是否每个邮件接收人、短信接收人都有语言
      alarmReceivers.forEach((item) => {
        if (
          ((item.email || item.sms) && !item.lang) ||
          (!item.email && !item.sms && item.lang)
        ) {
          flag = false
        }
      })
      if (!flag) {
        callback(new Error(this.$t('alarmConfigTab.emailLangEmpty')))
      } else {
        callback()
      }
    },
    open() {
      this.showFlag = true
    },
    clearParams() {
      this.$refs.receiverForm.resetFields()
      this.loading = false
    },
    btnSave() {
      this.$refs.receiverForm.validate((valid) => {
        if (valid) {
          this.loading = true
          const { alarmReceivers = [] } = this.alarmConfigForm
          const filterReceivers = alarmReceivers.filter(
            (item) => item.email || item.lang
          )
          this.$emit('btnSave', { alarmReceivers: filterReceivers })
        }
      })
    },
    closeDialog() {
      this.clearParams()
      this.showFlag = false
      this.alarmConfigForm = {
        alarmReceivers: '',
      }
    },
    handleAdd() {
      const { alarmReceivers = [] } = this.alarmConfigForm
      if (alarmReceivers.length >= 10) {
        this.$message.warning(this.$t('alarmConfigTab.alarmReceiverLimit'))
        return
      }
      this.$refs.receiverForm.resetFields()
      this.alarmConfigForm = {
        ...this.alarmConfigForm,
        alarmReceivers: [...alarmReceivers, { email: '', sms: '', lang: '' }],
      }
    },
    handleDelete(index) {
      const alarmReceivers = [...(this.alarmConfigForm.alarmReceivers || [])]
      alarmReceivers.splice(index, 1)
      this.alarmConfigForm = {
        ...this.alarmConfigForm,
        alarmReceivers: [...alarmReceivers],
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.alarm-edit-wrap {
  flex: 1;
  padding: 20px;
  .common-config-wrap {
    width: 100%;
  }
}

.alarm-config-table {
  width: 100%;
}

::v-deep .el-dialog__wrapper {
  align-items: start;
  .el-dialog {
    margin-top: 15vh !important;
  }
}

::v-deep .alarm-config-dialog .common-config-wrap .el-form-item__content {
  line-height: 34px;
}

::v-deep .alarm-config-dialog .common-config-wrap .el-form-item {
  margin-bottom: 18px;
}

::v-deep .alarm-config-dialog .common-config-wrap .el-form-item__label {
  text-align: justify; // label标签占满
  text-align-last: justify;
}

::v-deep
  .alarm-config-dialog
  .el-table
  td.el-table__cell
  div
  .el-form-item__content {
  line-height: 34px;
}

::v-deep .alarm-config-dialog .el-table td.el-table__cell div .el-form-item {
  margin-top: 10px;
  margin-bottom: 10px;
}

::v-deep
  .alarm-config-dialog
  .el-table
  td.el-table__cell
  div
  .el-form-item__error {
  top: 70%;
}

::v-deep .alarm-config-dialog .el-table .el-table__body .el-table__cell {
  padding: 0px;
}
::v-deep .alarm-config-dialog .disable-form-item .tvt-select .tvt-select-inner {
  background-color: #f5f7fa;
}
::v-deep .alarm-config-dialog .alarm-dialog-footer {
  display: inline-block;
  width: 100%;
  height: 52px;
  text-align: right;
  border-top: 1px solid #e0e0e0;
  padding: 10px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-size: 14px;
  padding: 5px 20px;
  button {
    border-radius: 16px;
    height: 36px;
  }
}
</style>
<style>
.alarm-config-dialog .el-table__body tr.hover-row > td.el-table__cell {
  background-color: unset !important;
}
.alarm-config-dialog
  .el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell {
  background-color: unset !important;
}
.alarm-config-dialog .el-table__body .cell {
  white-space: pre-line;
}
</style>
