// 权限和国际化map
export const AUTH_I18N_MAP = {
  co_info_qry: 'corpInfoQry',
  co_info_mgr: 'corpInfoManage',
  user_qry: 'userInfoQry',
  user_mgr: 'userInfoOp',
  cloud_upgrade_qry: 'cloudUpgradeQry',
  cloud_upgrade_mgr: 'cloudUpgradeOp',
  cloud_storage_qry: 'cloudStorageQry',
  cloud_storage_mgr: 'cloudStorageOp',
  // 'cloud_vms_qry': 'cloudVMSQry',
  cloud_vms_mgr: 'cloudVMSOp',
  rmr_qry: 'rmrQry',
  rmr_mgr: 'rmrOp',
  // 'cmdb_qry': 'cmdbQry',
  cmdb_mgr: 'cmdbOp',
  monitor_mgr: 'monitorOp',
  message_qry: 'msgQry',
  message_mgr: 'msgOp',
  host_sshkey_qry: 'secretKeyQry',
  host_sshkey_mgr: 'secretOp',
  domain_qry: 'domainQry',
  domain_mgr: 'domainOp',
  dev_info_qry: 'devQry',
  partner_app_mgr: 'partnerOp',
  cus_code_qry: 'basicDataQry',
  cus_code_mgr: 'basicDataOp',
  cus_sms_qry: 'smsQry',
  cus_sms_mgr: 'smsOp',
  cus_email_qry: 'emailQry',
  cus_email_mgr: 'emailOp',
  cus_appid_qry: 'appIdQry',
  cus_appid_mgr: 'appIdManage',
  redirect_data_qry: 'addressMapQry',
  redirect_data_mgr: 'addressMapManage',
  feedback_mgr: 'feedbackManage',
  use_qry: 'useQry',
  use_mgr: 'useMgr',
  diagnose_qry: 'diagnoseQry',
  diagnose_mgr: 'diagnoseMgr',
  data_dict_qry: 'dataDictQry',
  data_dict_mgr: 'dataDictMgr',
  config_property_qry: 'configPropertyQry',
  config_property_mgr: 'configPropertyMgr',
}
