# cloud-data-analysis

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve 
或者 npm run dev
```

### Compiles and minifies for production
```
npm run build
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).

### 打包后上传代码说明
打包后 将dist中得 static  favicon.ico  index.html 拖到  
TVTCloudDevOps\DevOpsService\src\main\resources\web 目录下替换 然后上传代码
