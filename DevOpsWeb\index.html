<!DOCTYPE html>
<html lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width,initial-scale=1.0">
        <!-- 强制请求，禁止使用缓存 -->
        <meta http-equiv="Expires" content="0">
        <meta http-equiv="Pragma" content="no-cache">
        <meta http-equiv="Cache-Control" content="no-cache,no-store,must-revalidate">
        <meta http-equiv="Cache" content="no-cache">
        <link rel="icon" href="static/favicon.ico">
        <title>Cloud DevOps</title>
        <!-- 确实需要在某次发布时强制更新所有缓存，可以在 HTML 模板中添加版本号查询参数, 能在需要时手动控制缓存刷新，而不是每次构建都强制刷新 -->
        <script defer src="<%= htmlWebpackPlugin.files.js[0] %>?v=<%= Date.now() %>"></script>
    </head>
    <body>
        <div id="app"></div>
        <!-- built files will be auto injected -->
    </body>
</html>
