<template>
  <div>
    <div class="install-deploy">
      <router-view />
    </div>
  </div>
</template>

<script>
export default {
  name: 'InstallDeploy',
  components: {},
  computed: {},
  mounted() {},
  methods: {},
}
</script>
<style lang="scss" scoped>
.install-deploy {
  width: 100%;
  height: 100%;
  overflow: auto;
}
</style>
<style lang="scss">
.tu-input .search.blur-search:hover {
  border: 1px solid #e0e0e0 !important;
}
</style>
