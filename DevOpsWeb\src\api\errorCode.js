const ERROR_CODE = {
  1005: '图片验证码错误',
  1007: '需要图片验证码',
  1008: '验证码已过期',
  1009: '验证码错误',
  1011: '未正确填写参数',
  1012: '数据库操作失败',
  1013: '手动上传部署包失败',
  1014: '文件归档失败',
  1015: '不存在部署包依赖关系数据',
  1016: '下载开源件参数配置模板失败',
  1017: '部署包下载失败',
  1018: '请下载正确的导入模板',
  1019: '删除归档数据失败',
  1020: '归档次数超限',
  1021: '必须包含OpenSourceDependency、TVTCloudBasicCBB组件',
  1022: '必须包含OpenSourceDependency、StarVisionCloudCBB组件',
  1404: '查询的信息在RDC不存在',
  401: '未经许可，需要通过HTTP认证',
  403: '服务器拒绝该次访问',
  404: '请求的资源（网页等）不存在',
  408: '服务器内部错误，无法完成请求',
  500: '系统异常',
  502: '服务器请求失败',
  503: '服务器异常',
  504: '服务器请求超时',
  12344: '网络连接失败',
  12345: '网络连接超时', // 前端axios设置超时自用code
  10001: '系统异常！',
  1023: '导入配置失败或注册中心ip错误',
  7092: '账号或密码错误',
  1025: '账号或密码错误',
  7003: 'Token不合法',
  7004: 'Token过期，请重新登录',
  ERR_BAD_RESPONSE: '系统异常',
  3001: '缺少参数url',
  5001: '无权限访问',
  3002: 'redis key不存在',
  1033: '文件不存在',
  2002: '项目部署包重复',
}

const ERROR_CODE_EN = {
  1005: 'Image verification code error',
  1007: 'Picture verification code is required',
  1008: 'The verification code has expired',
  1009: 'Verification code error',
  1011: 'The parameter is not filled in correctly',
  1012: '数据库操作失败',
  1013: '手动上传部署包失败',
  1014: '文件归档失败',
  1015: '不存在部署包依赖关系数据',
  1016: '下载开源件参数配置模板失败',
  1017: '部署包下载失败',
  1018: '请下载正确的导入模板',
  1019: '删除归档数据失败',
  1020: '归档次数超限',
  1021: '必须包含OpenSourceDependency、TVTCloudBasicCBB组件',
  1022: '必须包含OpenSourceDependency、StarVisionCloudCBB组件',
  1404: '查询的信息在RDC不存在',
  401: '未经许可，需要通过HTTP认证',
  403: '服务器拒绝该次访问',
  404: '请求的资源（网页等）不存在',
  408: '服务器内部错误，无法完成请求',
  500: '系统异常',
  502: '服务器请求失败',
  503: '服务器异常',
  504: '服务器请求超时',
  12344: '网络连接失败',
  12345: '网络连接超时', // 前端axios设置超时自用code
  10001: '系统异常！',
  1023: '导入配置失败或注册中心ip错误',
  7092: 'Wrong account or password!',
  1025: 'Wrong account or password!',
  7003: 'Token is error',
  7004: 'Token expired, please log in again',
  ERR_BAD_RESPONSE: 'System exception',
  3001: 'url param is need',
  5001: 'No permission to the resource',
  3002: 'redis key not exist',
  1033: 'The files not exists',
  2002: 'Project deployment package is duplicated',
}

const errorCode = {
  'en-US': ERROR_CODE_EN,
  'zh-CN': ERROR_CODE,
}

export default errorCode
