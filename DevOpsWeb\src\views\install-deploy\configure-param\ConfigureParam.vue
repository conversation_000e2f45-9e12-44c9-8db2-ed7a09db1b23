<template>
  <!-- 配置参数 -->
  <div class="config-param">
    <div class="search-wrapper">
      <div class="search-left-wrapper">
        <tvt-select
          v-model="paramType"
          :options="sourceOptions"
          :mock-placeholder="$t('configParamTab.sourceTypePhd')"
          clearable
          style="margin-right: 20px"
          @change="getConfigParam"
        />
      </div>
    </div>
    <!-- 配置参数表格 -->
    <div class="table-wrapper">
      <tvt-table
        v-myLoading="loading"
        :data="configParamList"
        :columns="configParamColumns"
        :border="true"
        :border-bottom="true"
        :pagination="false"
        @onFetchData="getConfigParam"
      >
        <template #bodyCell="{ row, column, $index }">
          <template v-if="column.prop === 'configStatus'">
            <span>{{ statusObj[row[column.prop]] }}</span>
          </template>
          <template v-if="column.prop === 'operate'">
            <span class="btn-text" @click="configEdit(row, $index)">{{
              $t('edit')
            }}</span>
            <!-- 待生效才有发布按钮 -->
            <span
              v-if="row.configStatus === 0"
              class="btn-text"
              @click="configRelease(row, $index)"
              >{{ $t('configParamTab.configRelease') }}</span
            >
          </template>
        </template>
      </tvt-table>
    </div>
    <!-- 配置参数编辑弹窗 -->
    <config-param-edit
      ref="configParamDialog"
      @btnSave="configSave"
      @onClose="handleCancel"
    />
    <!-- 配置发布确认弹窗 -->
    <tip-dialog
      ref="tipDialogRef"
      title="configTab.info"
      :tip-msg="$t('configParamTab.releaseTip')"
      @onClose="handleCancel"
      @submit="handleRelease"
    />
  </div>
</template>
<script>
import ConfigParamEdit from './ConfigParamEdit.vue'
import TipDialog from '@/components/common/TipDialog.vue'
import {
  getBusParamList,
  updateBusParam,
  publishBusParam,
} from '@/api/installDeploy.js'
export default {
  name: 'Hello',
  components: {
    ConfigParamEdit,
    TipDialog,
  },
  data() {
    return {
      paramType: '',
      sourceOptions: [
        { label: 'Mysql', value: 'Mysql' },
        { label: 'Redis', value: 'Redis' },
        { label: 'Mongo', value: 'Mongo' },
        { label: 'Nfs', value: 'Nfs' },
        { label: 'Oss', value: 'Oss' },
        { label: 'S3', value: 'S3' },
      ],
      loading: false,
      statusObj: {
        0: this.$t('configParamTab.pendingEffective'),
        1: this.$t('configParamTab.effective'),
      },
      configParamList: [],
      configParamColumns: [
        {
          label: this.$t('configParamTab.dataSourceType'),
          prop: 'datasourceType',
          width: 150,
        },
        {
          label: this.$t('configParamTab.dataSourceIdenti'),
          prop: 'datasourceMark',
          width: 230,
        },
        {
          label: this.$t('configParamTab.configuration'),
          prop: 'configuration',
          minWidth: 400,
        },
        {
          label: this.$t('alarmStatus'),
          prop: 'configStatus',
          slotName: 'bodyCell',
          width: 150,
        },
        {
          label: this.$t('operation'),
          prop: 'operate',
          slotName: 'bodyCell',
          fixed: 'right',
          width: 150,
          hide: !this.$isAuthorityExist(['devops_deploy_datasourceConfig_mgr']),
        },
      ],
      record: null, // 当前编辑的记录
    }
  },
  created() {},
  mounted() {
    this.getConfigParam()
  },
  methods: {
    async getConfigParam() {
      this.loading = true
      try {
        const res = await getBusParamList({ paramType: this.paramType })
        console.log('res', res)
        this.loading = false
        const { data = [] } = res
        const configParamList = []
        data.forEach((item) => {
          const { datasourceType, configMap } = item
          let configuration = []
          switch (datasourceType) {
            case 'Mysql':
              configuration = [
                `url:${configMap['mysql-ip-port']}`,
                `username:${configMap['mysql-user']}`,
                `password:${configMap['mysql-cipher']}`,
              ]
              break
            case 'Redis':
              configuration = [
                `url:${configMap['redis-nodes']}`,
                `password:${configMap['redis-cipher']}`,
                `model:${configMap['redis-mode']}`,
              ]
              break
            case 'Mongo':
              configuration = [
                `url:${configMap['mongodb-ip-port']}`,
                `username:${configMap['mongodb-user']}`,
                `password:${configMap['mongodb-cipher']}`,
              ]
              break
            case 'Nfs':
              configuration = [`url:${configMap['nfs-server']}`]
              break
            default:
              Object.entries(configMap).forEach(([key, value]) => {
                configuration.push(`${key}:${value}`)
              })
              break
          }
          configParamList.push({
            ...item,
            configuration: configuration.join('\n'),
          })
        })
        this.configParamList = configParamList
      } catch (err) {
        this.loading = false
        console.log(err)
      }
    },
    configEdit(row) {
      this.record = row
      this.$refs.configParamDialog.open(row)
    },
    // 配置发布
    configRelease(row) {
      this.record = row
      this.$refs.tipDialogRef.open()
    },
    async configSave(configMap) {
      try {
        const { datasourceType, datasourceMark } = this.record
        const params = {
          datasourceType,
          datasourceMark,
          configMap,
        }
        await updateBusParam(params)
        this.$refs.configParamDialog.closeDialog()
        this.$message.success(this.$t('editSuccess'))
        // 刷新表格
        this.getConfigParam()
      } catch (error) {
        console.log('error', error)
      } finally {
        this.record = null
      }
    },
    handleCancel() {
      this.record = null
    },
    // 配置发布确认
    async handleRelease() {
      try {
        const { datasourceType, datasourceMark } = this.record
        const params = { datasourceType, datasourceMark }
        await publishBusParam(params)
        // console.log('配置发布res', res)
        this.$message.success(this.$t('configParamTab.configReleaseSucccess'))
        this.handleCancel()
        // 刷新表格
        this.getConfigParam()
      } catch (error) {
        console.log('error', error)
      } finally {
        this.record = null
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.config-param {
  margin: 24px;
  width: calc(100% - 48px);
  .search-wrapper {
    width: 100%;
    margin: 20px 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    .search-left-wrapper {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
    }
    .search-right-wrapper {
      width: max-content;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
  }

  .btn-text {
    color: #429efd;
    cursor: pointer;

    + .btn-text {
      margin-left: 20px;
    }
  }

  .pre-detail {
    line-height: 24px;
    white-space: pre-line;
  }

  .no-detail {
    width: 600px;
    text-align: center;
  }
}
</style>
<style>
.config-param .table-wrapper .el-table__body tr.hover-row > td.el-table__cell {
  background-color: unset !important;
}
.config-param
  .table-wrapper
  .el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell {
  background-color: unset !important;
}
.config-param .table-wrapper .el-table__body .cell {
  white-space: pre-line;
}
.config-param .el-input__inner {
  height: 32px;
  line-height: 32px;
}

.search-wrapper .el-input__inner {
  width: 250px;
}

.config-param .el-table .el-table__cell {
  padding: 8px 0 !important;
}
.config-param .search-wrapper .el-input__inner {
  height: 32px;
  line-height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 16px;
}
.config-param .search-wrapper .el-input__icon {
  line-height: 34px;
}

.config-param .search-wrapper .tvt-input .tvt-field-set {
  border: none;
}
.config-param .search-wrapper .tvt-select .tvt-field-set {
  border: none;
}

.config-param .table-wrapper2 .el-table .el-table__cell {
  padding: 8px 0 !important;
}
</style>
