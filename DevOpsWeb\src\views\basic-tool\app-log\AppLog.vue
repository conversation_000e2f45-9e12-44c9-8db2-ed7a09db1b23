<template>
  <!-- APP Log -->
  <div class="page-wrapper">
    <div class="search-wrapper">
      <search-input
        v-model="filterCond.logid"
        :placeholder="$t('appLogTab.logIDInput')"
        clearable
        size="small"
        maxlength="32"
        style="width: 250px; margin-right: 20px"
        @change="getAppLogList(1)"
      />
      <tu-search-time
        ref="tuSearchTimeRef"
        v-model="filterCond.timeRange"
        class="search-time"
        :default="defaultTimeRange"
        @change="getAppLogList(1)"
      />
      <search-input
        v-model="filterCond.hwmodel"
        :placeholder="$t('appLogTab.phoneTypeInput')"
        clearable
        size="small"
        maxlength="32"
        style="width: 250px; margin-right: 20px"
        @change="getAppLogList(1)"
      />
      <search-input
        v-model="filterCond.appver"
        :placeholder="$t('appLogTab.softVersionInput')"
        clearable
        size="small"
        maxlength="32"
        style="width: 250px; margin-right: 20px"
        @change="getAppLogList(1)"
      />
      <search-input
        v-model="filterCond.errors"
        :placeholder="$t('appLogTab.errorCodeInput')"
        clearable
        size="small"
        maxlength="32"
        style="width: 250px; margin-right: 20px"
        @change="getAppLogList(1)"
      />
      <search-input
        v-model="filterCond.mcc"
        :placeholder="$t('appLogTab.mccCodeInput')"
        clearable
        size="small"
        maxlength="32"
        style="width: 250px; margin-right: 20px"
        @change="getAppLogList(1)"
      />
      <search-input
        v-model="filterCond.sn"
        :placeholder="$t('appLogTab.deviceSnInput')"
        clearable
        size="small"
        maxlength="32"
        style="width: 250px; margin-right: 20px"
        @change="getAppLogList(1)"
      />
      <div class="search-btn-box">
        <el-button type="primary" size="small" round @click="handleReset">{{
          $t('reset')
        }}</el-button>
        <el-button type="primary" size="small" round @click="handleRefresh">{{
          $t('refresh')
        }}</el-button>
      </div>
      <div class="search-left-wrapper"></div>
    </div>
    <!-- APP Log表格 -->
    <div class="table-wrapper">
      <tvt-table
        ref="myTable"
        v-myLoading="loading"
        :data="appLogList"
        :columns="appLogColumn"
        :border="true"
        :border-bottom="true"
        :pagination="{
          total,
          current: filterCond.current,
          size: filterCond.size,
          'page-sizes': $tablePageSizes,
          background: true,
        }"
        @onFetchData="getAppLogList"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.prop === 'downloadLog'">
            <!-- <div class="applog-table-icon">
              <i class="el-icon-download" @click="downloadFile(row)"></i>
            </div> -->
            <span class="btn-text" @click="downloadFile(row)">{{
              $t('download')
            }}</span>
          </template>
          <template v-if="column.prop === 'detail'">
            <!-- <div class="applog-table-icon">
              <i class="el-icon-document" @click="handleDetail(row)"></i>
            </div> -->
            <span class="btn-text" @click="handleDetail(row)">{{
              $t('detail')
            }}</span>
          </template>
        </template>
      </tvt-table>
    </div>
    <!-- APP Log日志详情 -->
    <app-log-detail ref="logDetailRef" />
  </div>
</template>
<script>
import { debounce, dateTimeRangeStamp, hanldeDownload } from '@/utils/common'
import TuSearchTime from '@/components/common/TuSearchTime.vue'
import AppLogDetail from './AppLogDetail.vue'
import { getAppLogList, downloadAppLog } from '@/api/basicTool'
import { getLocale } from '@/lang'
import errorCode from '@/api/errorCode.js'
export default {
  name: 'AppLog',
  components: {
    TuSearchTime,
    AppLogDetail,
  },
  data() {
    return {
      areaOptions: [], // DC选择
      defaultTimeRange: [],
      filterCond: {
        current: 1,
        size: 20,
        logid: '',
        hwmodel: '',
        appver: '',
        errors: '',
        mcc: '',
        sn: '',
        timeRange: dateTimeRangeStamp(),
      },
      loading: false,
      total: 0,
      appLogList: [],
      appLogColumn: [
        {
          label: this.$t('appLogTab.logid'),
          prop: 'logid',
          minWidth: 240,
        },
        {
          label: this.$t('appLogTab.hwmodel'),
          prop: 'hwmodel',
          width: 140,
        },
        {
          label: this.$t('appLogTab.firmver'),
          prop: 'appver',
          width: 140,
        },
        {
          label: this.$t('appLogTab.errors'),
          prop: 'errors',
          width: 140,
        },
        {
          label: this.$t('appLogTab.mcc'),
          prop: 'mcc',
          width: 140,
        },
        {
          label: this.$t('appLogTab.sn'),
          prop: 'sn',
          minWidth: 140,
        },
        {
          label: this.$t('appLogTab.time'),
          prop: 'time',
          width: 170,
        },
        {
          label: this.$t('appLogTab.downloadLog'),
          prop: 'downloadLog',
          slotName: 'bodyCell',
          fixed: 'right',
          width: 120,
          hide: !this.$isAuthorityExist(['devops_tool_appLog_mgr']),
        },
        {
          label: this.$t('detail'),
          prop: 'detail',
          slotName: 'bodyCell',
          fixed: 'right',
          width: 100,
        },
      ],
      downloadSet: new Set(), // 记录下载的日志
    }
  },
  mounted() {
    this.getAppLogList()
  },
  methods: {
    // 获取APP日志列表
    getAppLogList: debounce(async function (pageSize) {
      this.loading = true
      try {
        if (pageSize == 1) {
          this.filterCond.current = 1
        } else if (pageSize && pageSize.current) {
          this.filterCond.current = pageSize.current
          this.filterCond.size = pageSize.size
        }
        const { current, size, timeRange } = this.filterCond
        const params = {
          pageNum: current,
          pageSize: size,
        }

        // 使用循环处理字符串参数
        const stringParams = [
          'logid',
          'hwmodel',
          'appver',
          'errors',
          'mcc',
          'sn',
        ]
        stringParams.forEach((key) => {
          const value = this.filterCond[key]
          params[key] = value ? value.trim() : null
        })
        if (timeRange.length) {
          params.startTime = window
            .moment(timeRange[0])
            .format('YYYY-MM-DD HH:mm:ss')
          params.endTime = window
            .moment(timeRange[1])
            .format('YYYY-MM-DD HH:mm:ss')
        }
        const { data = {} } = await getAppLogList(params)
        const { records = [], total = 0 } = data
        this.appLogList = records
        this.total = Number(total)
        this.loading = false
      } catch (error) {
        console.error(error)
        this.loading = false
      }
    }, 200),
    // 获取APP日志详情
    handleDetail(row) {
      // 展示详情弹窗
      this.$refs.logDetailRef.open(row)
    },
    // 下载日志
    async downloadFile(row) {
      const { name, downloadDomain } = row
      const params = {
        url: name,
        type: 2, // 1 OSS类 2 日志类
      }
      if (downloadDomain) {
        params.httpUrl = `https://${encodeURIComponent(
          downloadDomain.trim()
        )}/dev-ops/basic/app-log-download`
      }
      // 判断url是否在10s内点击过，如果点击过则提示频率太快
      if (this.downloadSet.has(name)) {
        this.$message.warning(this.$t('microLogTab.downloadFreq'))
        return
      }
      this.downloadSet.add(name)

      let loading = null

      // 只在500ms后还没完成时才显示loading
      const loadingTimer = setTimeout(() => {
        loading = this.$loading({
          lock: true,
          background: 'rgba(0, 0, 0, .5)',
          text: this.$t('LoadingDownload'),
        })
      }, 500)

      try {
        const resBlob = await downloadAppLog(params)

        // 清理loading
        clearTimeout(loadingTimer)
        if (loading) {
          setTimeout(() => loading.close(), 200) // 简单延迟200ms
        }

        const pathArr = name.split('/')
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            // 尝试解析返回内容
            const result = JSON.parse(e.target.result)
            if (result.basic && result.basic.code) {
              const errorObj = errorCode[getLocale()]
              // 解析成功，提示错误信息
              this.$message.error(errorObj[result.basic.code])
              // 出错时需要清除下载限制
              this.downloadSet.delete(name)
              return
            }
          } catch (parseError) {
            // 如果无法解析为JSON，说明是正常的文件内容，直接下载
            hanldeDownload(resBlob, pathArr[pathArr.length - 1])
            // 定时器10s后放开下载限制
            setTimeout(() => {
              this.downloadSet.delete(name)
            }, 10000)
          }
        }
        reader.readAsText(resBlob)
      } catch (error) {
        console.error('下载文件失败:', error)
        clearTimeout(loadingTimer)
        if (loading) loading.close()
        this.downloadSet.delete(name)
      }
    },
    handleReset() {
      // 条件重置
      this.filterCond = {
        ...this.filterCond,
        current: 1,
        pageSize: 20,
        logid: '',
        hwmodel: '',
        appver: '',
        errors: '',
        mcc: '',
        sn: '',
        timeRange: dateTimeRangeStamp(),
      }
      this.$nextTick(() => {
        this.getAppLogList(1)
      })
    },
    handleRefresh() {
      this.getAppLogList()
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .applog-table-icon {
  width: 100%;
  i {
    cursor: pointer;
  }
}
</style>
