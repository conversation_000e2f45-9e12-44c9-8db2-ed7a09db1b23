<template>
  <tvt-dialog
    :title="$t('deployPlanTab.executeP2pWeb')"
    :show.sync="showFlag"
    width="1200px"
    :cancel-text="$t('cancel')"
    :submit-text="$t('confirm')"
    :close-on-click-modal="false"
    @close="closeDialog"
    @Cancel="closeDialog"
    @Submit="btnSave"
  >
    <div class="execute-deployment">
      <div class="search-wrapper execute-search-wrapper">
        <tvt-select
          v-model="filterCond.projectName"
          :mockplaceholder="$t('deployPlanTab.chooseDeployService')"
          clearable
          style="margin-right: 20px"
        >
          <el-option
            v-for="(item, index) in deployTypeOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </tvt-select>
        <tvt-select
          v-model="filterCond.packageSource"
          :mockplaceholder="$t('deployPlanTab.choosePackageSource')"
          clearable
          style="margin-right: 20px"
        >
          <el-option
            v-for="(item, index) in fileTypeOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </tvt-select>
        <search-input
          v-if="filterCond.packageSource === '1'"
          v-model="filterCond.packageUrl"
          :placeholder="
            $t('pleaseEnter', { type: $t('deployPlanTab.fileUrl') })
          "
          clearable
          maxlength="32"
          style="width: 600px; margin-right: 20px"
        />
        <el-upload
          v-if="filterCond.packageSource === '2'"
          class="upload-demo"
          action="#"
          :show-file-list="false"
          accept=".zip"
          :http-request="handleSelectFile"
          :file-list="filterCond.fileList"
          style="margin-right: 20px"
        >
          <el-button type="primary" round size="small">{{
            $t('deployPlanTab.clickUpload')
          }}</el-button>
        </el-upload>
        <el-button type="primary" round size="small" @click="handleSearch">
          {{ $t('searchText') }}
        </el-button>
      </div>
      <div class="table-wrapper1">
        <tvt-table
          ref="tableSelection"
          v-myLoading="loading"
          :data="sourceList"
          :columns="searchParams.packageSource === '3' ? radioColumns : columns"
          :border-bottom="true"
          :border="true"
          max-height="500"
          :pagination="false"
          @selection-change="handleSelectionChange"
        >
          <template #bodyCell="{ row, column }">
            <template v-if="column.prop === 'checkbox'">
              <el-checkbox
                v-model="row.checked"
                @change="handleCheckChange(row)"
              ></el-checkbox>
            </template>
            <template v-if="column.prop === 'startXmx'">
              <span>{{
                row.projectName.indexOf('web') != -1 ||
                row.projectName.indexOf('Web') != -1
                  ? '- -'
                  : row.startXmx
              }}</span>
            </template>
          </template>
        </tvt-table>
      </div>
    </div>
  </tvt-dialog>
</template>
<script>
import { getBasic } from '@/utils/basic.js'
import { uploadP2PWebPackage, getP2PDeployList } from '@/api/installDeploy.js'
export default {
  name: 'DeployP2pDialog',
  data() {
    return {
      showFlag: false,
      filterCond: {
        projectName: '',
        packageSource: '',
        packageUrl: '',
        fileList: [],
        packageName: '',
      },
      searchParams: {
        projectName: '',
        packageSource: '',
        packageUrl: '',
        packageName: '',
      },
      deployTypeOptions: [
        { label: 'P2PPcWeb', value: 'P2PPcWeb' },
        { label: 'P2PAppWeb', value: 'P2PAppWeb' },
        { label: 'P2PCgiWeb', value: 'P2PCgiWeb' },
      ],
      fileTypeOptions: [
        { label: this.$t('deployPlanTab.urlDownload'), value: '1' },
        { label: this.$t('deployPlanTab.uploadFile'), value: '2' },
        {
          label: this.$t('deployPlanTab.archiveVersion'),
          value: '3',
        },
      ],
      loading: false,
      multipleSelection: [],
      columns: [
        { type: 'selection', width: 60 },
        { label: this.$t('index'), type: 'index', width: 80 },
        {
          label: this.$t('deployPlanTab.projectName'),
          prop: 'projectName',
          minWidth: 120,
        },
        {
          label: this.$t('deployPlanTab.version'),
          prop: 'version',
          width: 120,
        },
        {
          label: this.$t('deployPlanTab.hostIp'),
          prop: 'hostIp',
          width: 120,
        },
        {
          label: this.$t('deployPlanTab.deployPackageName'),
          prop: 'packageName',
          minWidth: 160,
        },
        {
          label: this.$t('deployPlanTab.instanceId'),
          prop: 'instanceId',
          width: 100,
        },
      ],
      radioColumns: [
        { label: '', prop: 'checkbox', slotName: 'bodyCell', width: 40 },
        { label: this.$t('index'), type: 'index', width: 80 },
        {
          label: this.$t('deployPlanTab.projectName'),
          prop: 'projectName',
          minWidth: 120,
        },
        {
          label: this.$t('deployPlanTab.version'),
          prop: 'version',
          width: 120,
        },
        {
          label: this.$t('deployPlanTab.hostIp'),
          prop: 'hostIp',
          width: 120,
        },
        {
          label: this.$t('deployPlanTab.deployPackageName'),
          prop: 'packageName',
          minWidth: 160,
        },
        {
          label: this.$t('deployPlanTab.instanceId'),
          prop: 'instanceId',
          width: 100,
        },
      ],
      sourceList: [],
      total: 0,
      currentSelectRow: null,
    }
  },
  methods: {
    // 检查文件地址
    checkFileUrl(rule, value, callback) {
      if (value) {
        if (value.indexOf('http') === -1) {
          // 检查文件名格式
          callback(new Error(this.$t('deployPlanTab.checkFileUrl')))
        } else {
          // 检查文件是否存在--调用后台接口
          callback()
        }
      } else {
        callback()
      }
    },
    // 弹框打开时 清除第一次的自动校验回调
    open() {
      this.showFlag = true
      this.filterCond = {
        projectName: '',
        packageSource: '',
        packageUrl: '',
        fileList: [],
        packageName: '',
      }
      this.multipleSelection = []
      this.sourceList = []
    },
    // 上传文件
    handleSelectFile(e) {
      if (e.file.size > 10 * 1024 * 1024) {
        this.$message.error(this.$t('fileSizeLimits', { size: 10, unit: 'M' }))
        return false
      }
      let token = JSON.stringify({
        basic: getBasic(),
        data: '',
      })
      let formData = new FormData()
      formData.append('file', e.file)
      formData.append('requestData', token)
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, .5)',
        text: this.$t('uploading'),
      })
      uploadP2PWebPackage({
        data: formData,
        type: 'upload',
      })
        .then((res) => {
          loading.close()
          this.$message.success('uploadSuccess')
          this.filterCond.packageName = res.data
        })
        .catch((error) => {
          loading.close()
          this.$message.error(error.msg || this.$t('uploadError'))
        })
    },
    // 查询
    async handleSearch() {
      console.log('查询条件', this.filterCond)
      const { projectName, packageSource, packageUrl, packageName } =
        this.filterCond
      if (!projectName) {
        this.$message.warning(this.$t('deployPlanTab.chooseDeployService'))
        return
      }
      if (!packageSource) {
        this.$message.warning(this.$t('deployPlanTab.choosePackageSource'))
        return
      }
      if (Number(packageSource) === 1 && !packageUrl) {
        this.$message.warning(this.$t('deployPlanTab.inputFileUrl'))
        return
      }
      if (Number(packageSource) === 2 && !packageName) {
        this.$message.warning(this.$t('deployPlanTab.pleaseUploadFile'))
        return
      }
      this.loading = true
      try {
        const params = {
          projectName,
          packageSource,
        }
        if (Number(packageSource) === 1) {
          params.packageUrl = packageUrl
        }
        if (Number(packageSource) === 2) {
          params.packageName = packageName
        }
        const res = await getP2PDeployList(params)
        if (res.basic && res.basic.code == 200) {
          this.sourceList = res.data.map((item) => {
            item.checked = false
            return item
          })
        } else {
          this.sourceList = []
        }
        this.searchParams = { ...params }
        this.multipleSelection = []
        this.currentSelectRow = null
      } catch (error) {
        this.sourceList = []
        console.log(error)
      } finally {
        this.loading = false
      }
    },
    handleSelectionChange(rows) {
      //表格选择
      this.multipleSelection = rows
    },
    // 勾选变化
    handleCheckChange(row) {
      const sourceList = this.sourceList.map((item) => {
        const temp = { ...item }
        if (
          item.packageName === row.packageName &&
          item.instanceId === row.instanceId
        ) {
          if (
            this.currentSelectRow &&
            this.currentSelectRow.packageName === row.packageName &&
            this.currentSelectRow.instanceId === row.instanceId
          ) {
            // 当前已经选中了，则取消选中
            temp.checked = false
            this.currentSelectRow = null
          } else {
            temp.checked = true
            this.currentSelectRow = temp
          }
        } else {
          temp.checked = false
        }
        return temp
      })
      this.sourceList = sourceList
    },
    // 点击确定
    btnSave() {
      const { projectName, packageSource, packageUrl, packageName } =
        this.searchParams
      if (packageSource === '3') {
        // 历史版本则使用当前选中的行记录
        if (!this.currentSelectRow) {
          this.$message.error(this.$t('versionTab.needChooseList'))
          return false
        }
      } else {
        if (this.multipleSelection.length == 0) {
          this.$message.error(this.$t('versionTab.needChooseList'))
          return false
        }
      }
      const payload = {
        projectName,
        packageSource,
        packageUrl,
        packageName,
        instanceIdList: this.multipleSelection.map((item) => item.instanceId),
      }
      if (packageSource === '3') {
        // 历史版本则使用当前选中的行记录
        payload.packageName = this.currentSelectRow.packageName
        payload.instanceIdList = [this.currentSelectRow.instanceId]
      } else {
        payload.instanceIdList = this.multipleSelection.map(
          (item) => item.instanceId
        )
      }
      this.$emit('btnSave', payload)
      this.showFlag = false
    },
    // 关闭弹框
    closeDialog() {
      this.showFlag = false
      this.filterCond = {
        projectName: '',
        packageSource: '',
        packageUrl: '',
        fileList: [],
        packageName: '',
      }
      this.searchParams = {
        projectName: '',
        packageSource: '',
        packageUrl: '',
        packageName: '',
      }
      this.sourceList = []
      this.total = 0
      this.currentSelectRow = null
    },
  },
}
</script>
<style lang="scss" scoped>
.execute-deployment {
  flex: 1;
  padding: 20px;
  .search-wrapper {
    width: 100%;
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}
::v-deep .el-dialog__wrapper {
  align-items: start;
  .el-dialog {
    margin-top: 15vh !important;
  }
}
</style>
<style>
.execute-deployment .search-wrapper .el-input__inner {
  height: 32px;
  line-height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 16px;
}

.execute-deployment .execute-search-wrapper .el-input__inner {
  width: 200px;
}

.execute-deployment .search-wrapper .tvt-input .tvt-field-set {
  border: none;
}
.execute-deployment .search-wrapper .tvt-select .tvt-field-set {
  border: none;
}
.execute-deployment .search-wrapper .tu-input .search.blur-search:hover {
  border: 1px solid #409eff !important;
}
</style>
