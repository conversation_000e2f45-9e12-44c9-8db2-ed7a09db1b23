<template>
  <div class="bread-flex">
    <div class="collapse-icon" @click="isCollapse = !isCollapse">
      <i
        :class="isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"
        class="icon-fold"
      />
    </div>
    <el-breadcrumb class="breadcrumb-container" separator="/">
      <el-breadcrumb-item
        v-for="(item, index) in list"
        :key="item.path"
        class="breadcrumb-item"
      >
        <span
          v-if="item.redirect === 'noRedirect' || index == list.length - 1"
          class="no-redirect"
          @click="$router.push(item.path)"
          >{{ $t(`nav.${item.name}`) }}</span
        >
        <a v-else class="pre" @click.prevent="handleLink(item)">{{
          $t(`nav.${item.name}`)
        }}</a>
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script>
import EventBus from '@/utils/eventBus.js'
export default {
  name: 'BreadCrumb',
  data() {
    return {
      list: this.getBreadcrumb(),
      isCollapse: false, //展开
    }
  },
  watch: {
    $route: {
      handler(newVal) {
        if (newVal) {
          this.list = this.getBreadcrumb()
        }
      },
      deep: true,
      immediate: true,
    },
    isCollapse: {
      handler(newVal) {
        EventBus.$emit('changeSiderBar', { value: newVal })
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getBreadcrumb() {
      return this.$route.matched.filter(
        (item) => item.name && item.name != 'main'
      )
    },
    handleLink(item) {
      const { redirect } = item
      if (redirect) {
        this.$router.push(redirect)
        return
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.collapse-icon {
  cursor: pointer;
  padding-left: 20px;
  padding-right: 20px;
  .icon-fold {
    position: absolute;
    color: #909399;
    top: 12px;
    font-size: 24px;
  }
}
.bread-flex {
  width: 400px;
  display: flex;
}
.breadcrumb-container {
  height: 48px;
  line-height: 48px;
  .el-breadcrumb {
    margin-left: 20px;
  }
  .no-redirect {
    font-weight: 600;
    cursor: text;
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
  }
  .pre {
    color: rgba(255, 255, 255, 0.9);
  }
}

:deep(.el-breadcrumb__item .el-breadcrumb__inner a) {
  display: flex;
  float: left;
  font-weight: normal;
}

:deep(.el-breadcrumb__item .el-breadcrumb__inner i) {
  margin-right: 3px;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner a) {
  color: #999;
}
</style>
