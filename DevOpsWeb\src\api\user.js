import request from '@/api/request'
const baseUrl = '/dev-ops'

export const devopsLogin = (data) => request.post(`${baseUrl}/user/login`, data)
export const devopsModifyPass = (data) =>
  request.post(`${baseUrl}/devops-user/modify-pass`, data)

// 获取用户权限
export const devopsUserAuthFunc = (data) =>
  request.post(`${baseUrl}/user/res-list`, data)

// token续签
export const RenewToken = (data) =>
  request.post(`${baseUrl}/user/token-renewal`, data)

// 图片验证码校验
export const checkImgCode = (data) =>
  request.post(`${baseUrl}/user/check-verify-code`, data)

// 获取验证码图片
export const getVerifyImg = (data) =>
  request.post(`${baseUrl}/user/verify-code-get`, data)
