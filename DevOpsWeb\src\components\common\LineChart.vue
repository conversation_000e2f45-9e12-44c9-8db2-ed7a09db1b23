<template>
  <div
    :id="`linechart-${echartKey}`"
    class="echart-wrapper"
    style="background-color: #fff"
  />
</template>

<script>
import { throttle } from '@/utils/common'

const highColorList = [
  'rgba(145, 204, 117, 0.8)',
  'rgba(250, 200, 88, 0.8)',
  'rgba(238, 102, 102, 0.8)',
  'rgba(115, 192, 222, 0.8)',
  'rgba(59, 162, 144, 0.8)',
  'rgba(252, 132, 82, 0.8)',
  'rgba(154, 96, 180, 0.8)',
  'rgba(234, 124, 204, 0.8)',
  'rgba(84, 112, 198, 0.8)',
]
// const lowColorList = [
//   'rgba(145, 204, 117, 0.3)',
//   'rgba(250, 200, 88, 0.3)',
//   'rgba(238, 102, 102, 0.3)',
//   'rgba(115, 192, 222, 0.3)',
//   'rgba(59, 162, 144, 0.3)',
//   'rgba(252, 132, 82, 0.3)',
//   'rgba(154, 96, 180, 0.3)',
//   'rgba(234, 124, 204, 0.3)',
//   'rgba(84, 112, 198, 0.3)',
// ]
export default {
  props: {
    echartKey: {
      type: String,
      default: '',
    },
    seriesObj: {
      type: Object,
      default: () => null,
    },
    title: {
      type: String,
      default: '',
    },
    showFullScreen: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      chartInstance: null, // echarts实例
      resizeObserver: null, // 监听尺寸变化的实例
    }
  },
  watch: {
    seriesObj: {
      handler(val) {
        if (val) {
          if (!this.chartInstance) {
            this.$nextTick(() => {
              // 确保dom元素已经加载完成
              this.initCharts()
            })
          } else {
            this.$nextTick(() => {
              // 确保dom元素已经加载完成
              // this.chartInstance.clear()
              const option = this.getOption(val)
              this.chartInstance.setOption(option, { notMerge: true })
              // 无需手点缩放,自动触发
              this.chartInstance.dispatchAction({
                type: 'takeGlobalCursor',
                key: 'dataZoomSelect',
                dataZoomSelectActive: true,
              })
              this.chartResize()
            })
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    // this.initCharts()
    if (!this.chartInstance) {
      this.initCharts()
    }
  },
  beforeDestroy() {
    const dom = document.getElementById(`linechart-${this.echartKey}`)
    if (this.resizeObserver && dom) {
      this.resizeObserver.unobserve(dom)
      this.resizeObserver = null
    }
    if (this.chartInstance) {
      this.chartInstance.dispose()
      this.chartInstance = null
    }
  },
  methods: {
    // 生成图的options配置
    getOption(seriesObj) {
      const series = seriesObj || this.seriesObj
      const { xAxis = [], seriesData = [] } = series || {}
      // console.log('seriesData', seriesData)
      const option = {
        title: {
          left: 'center',
          text: this.title,
          textStyle: {
            fontSize: 14,
          },
        },
        grid: {
          top: 60, // 默认60
          bottom: 30, // 默认60
          left: 80, // 默认10%
          right: 80, // 默认10%
        },
        toolbox: {
          itemSize: 14, // 图标大小
          feature: {
            dataZoom: {
              yAxisIndex: 'none',
              title: {
                zoom: this.$t('zoom'),
                // back: '',
              },
              icon: {
                // zoom: '',
                back: 'image://', // 设置一个不存在的图片路径，使图标不显示
              }, // 可以修改缩放和还原的图标
              iconStyle: {
                borderWidth: 1.5,
              },
            },
            myTool1: {
              // 自定义按钮
              show: true,
              title: this.$t('restoration'),
              icon: 'path://M417.312 252.688a16 16 0 0 0-22.632 0L251.656 395.712a40 40 0 0 0 0 56.576l143.024 143.024a16 16 0 1 0 22.632-22.624L284.624 440H598.32c1.384 0 2.72-0.176 4-0.504 1.28 0.328 2.616 0.504 4 0.504 42.28 0 72.248 8.92 93.92 23.64 21.584 14.664 36.512 36.16 46.8 64.272C768.104 585.44 768.32 666.104 768.32 760a16 16 0 0 0 32 0v-2.56c0-90.736 0.008-177.048-23.224-240.528-11.96-32.688-30.4-60.4-58.88-79.744C689.816 417.888 653.04 408 606.32 408c-1.384 0-2.72 0.176-4 0.504a16.04 16.04 0 0 0-4-0.504H284.624l132.688-132.688a16 16 0 0 0 0-22.624z',
              iconStyle: {
                borderWidth: 1,
              },
              onclick: () => {
                this.$emit('timeZoom', {})
              },
            },
            restore: {
              show: false,
            },
            saveAsImage: {
              title: this.$t('save'),
              iconStyle: {
                borderWidth: 1.5,
              },
            },
            myFull: {
              // 全屏
              show: this.showFullScreen,
              title: this.$t('fullScreen'),
              icon: 'path://M160 96h192q14.016 0.992 23.008 10.016t8.992 22.496-8.992 22.496T352 160H160v192q0 14.016-8.992 23.008T128 384t-23.008-8.992T96 352V96h64z m0 832H96v-256q0-14.016 8.992-23.008T128 640t23.008 8.992T160 672v192h192q14.016 0 23.008 8.992t8.992 22.496-8.992 22.496T352 928H160zM864 96h64v256q0 14.016-8.992 23.008T896 384t-23.008-8.992T864 352V160h-192q-14.016 0-23.008-8.992T640 128.512t8.992-22.496T672 96h192z m0 832h-192q-14.016-0.992-23.008-10.016T640 895.488t8.992-22.496T672 864h192v-192q0-14.016 8.992-23.008T896 640t23.008 8.992T928 672v256h-64z',
              onclick: () => {
                this.$emit('fullScreen')
              },
            },
          },
        },
        legend: {
          show: true,
          type: 'scroll', //过多类型时自适应分页显示
          orient: 'horizontal', //方向horizontal水平显示，vertical垂直显示
          top: 30,
          data: (seriesData || []).map((item) => item.name),
          itemStyle: {
            // 图例上点的设置
            opacity: 0,
          },
          lineStyle: {
            // 图例上线的设置
            type: 'solid',
          },
        },
        tooltip: {
          trigger: 'axis',
          appendToBody: false,
          position: function (point, params, dom, rect, size) {
            // point: 鼠标位置 [x, y]。
            // params: 与当前鼠标位置相关的数据信息对象。
            // dom: tooltip 的 DOM 节点。
            // rect: 绑定了 tooltip 的图形的包围盒。
            // size:  包括 size.contentSize 和 size.viewSize，其中每个都是 [width, height] 形式的数组。
            // 返回值是 [x, y] 形式的数组，表示 tooltip 的位置。
            // 以下代码确保 tooltip 不会超出边界
            var result = [point[0], point[1]]
            if (result[0] + size.contentSize[0] > size.viewSize[0]) {
              result[0] = size.viewSize[0] - size.contentSize[0]
            }
            if (result[1] + size.contentSize[1] > size.viewSize[1]) {
              result[1] = size.viewSize[1] - size.contentSize[1]
            }
            return result
          },
          // extraCssText: 'z-index:999'
        },
        xAxis: {
          type: 'category',
          data: xAxis.slice(),
        },
        yAxis: {
          type: 'value',
          // scale: true, // 设置成 true 后坐标刻度不会强制包含零刻度
          scale: (() => {
            // 获取所有数据点
            const allData = seriesData.reduce(
              (acc, curr) => acc.concat(curr.data),
              []
            )
            const min = Math.min(...allData)
            const max = Math.max(...allData)
            const range = max - min

            // 判断数据是否远离0
            // 如果最小值距离0的比例超过数据范围的20%，则认为数据远离0
            return Math.abs(min) > range * 0.2
          })(),
          // minInterval: 1, // 最小间隔设为1，避免出现小数刻度
          ...(() => {
            // 获取所有数据点
            const allData = seriesData.reduce(
              (acc, curr) => acc.concat(curr.data),
              []
            )
            // 检查是否所有数据都是整数
            const allInteger = allData.every((num) =>
              Number.isInteger(Number(num))
            )
            // 如果全是整数，返回minInterval配置，否则不返回任何配置
            return allInteger ? { minInterval: 1 } : {}
          })(),
          // 设置上下边界间隙 当scale为true时（即数据远离0时）才添加boundaryGap
          ...(() => {
            // 获取所有数据点
            const allData = seriesData.reduce(
              (acc, curr) => acc.concat(curr.data),
              []
            )
            const min = Math.min(...allData)
            const max = Math.max(...allData)
            const range = max - min

            // 只有当scale为true时（即数据远离0时）才添加boundaryGap
            return Math.abs(min) > range * 0.2
              ? { boundaryGap: ['20%', '20%'] }
              : {}
          })(),
          // splitNumber: 5, // 设置期望的分割段数，使刻度更合理
        },
        series: seriesData.map((item, index) => {
          return {
            type: 'line',
            name: item.name,
            smooth: true,
            connectNulls: true, // 断数强制连接
            // symbol: 'none', // 不展示线上的点
            symbol: 'circle',
            showAllSymbol: true, // 展示所有数据点 默认值:'auto'
            symbolSize: 2,
            itemStyle: {
              color: highColorList[index],
            },
            // areaStyle: {
            //   color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
            //     {
            //       offset: 0,
            //       color: highColorList[index % highColorList.length],
            //     },
            //     {
            //       offset: 1,
            //       color: lowColorList[index % highColorList.length],
            //     },
            //   ]),
            // },
            data: item.data.slice(),
          }
        }),
      }
      return option
    },
    // 初始化图表
    initCharts() {
      this.chartInstance = this.$echarts.init(
        document.getElementById(`linechart-${this.echartKey}`)
      )
      const option = this.getOption()
      this.chartInstance.setOption(option, { notMerge: true })
      this.chartResize()
      const dom = document.getElementById(`linechart-${this.echartKey}`)
      if (this.resizeObserver) {
        this.resizeObserver.unobserve(dom)
      }
      const resizeThrottled = throttle(this.chartResize, 100)
      this.resizeObserver = new ResizeObserver(() => resizeThrottled())
      this.resizeObserver.observe(
        document.getElementById(`linechart-${this.echartKey}`)
      )
      // 先取消监听dataZoom事件
      this.chartInstance.off('dataZoom', this.chartDataZoom)
      // 监听dataZoom事件
      this.chartInstance.on('dataZoom', this.chartDataZoom)
    },
    // 监听父元素尺寸变化
    chartResize() {
      // 根据容器大小自动响应图表大小
      const dom = document.getElementById(`linechart-${this.echartKey}`)
      if (dom) {
        let { clientWidth: width, clientHeight: height } = dom.parentElement
        this.chartInstance && this.chartInstance.resize({ width, height })
      }
    },
    // 监听区域缩放事件
    chartDataZoom(event) {
      // console.log('dataZoom', Date.now(), event); // 打印事件详细信息
      // 你可以在这里根据event做进一步的处理
      const { batch } = event
      const { startValue, endValue, start, end } = batch[0]
      if (start === 0 && end === 100) {
        // 说明是还原
        this.$emit('timeZoom', {})
      } else {
        // 说明是缩放
        // console.log('startValue', startValue, 'endValue', endValue)
        // 找到在xAxis中对应的时间
        const { xAxis } = this.seriesObj
        const startTime = xAxis[startValue]
        const endTime = xAxis[endValue]
        // console.log('startTime', startTime, 'endTime', endTime)
        this.$emit('timeZoom', { startTime, endTime })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.echart-wrapper {
  width: 100%;
  height: 100%;
}
</style>
