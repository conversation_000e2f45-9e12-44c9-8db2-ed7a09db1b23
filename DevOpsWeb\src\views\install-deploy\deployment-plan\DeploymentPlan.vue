<template>
  <!-- 部署规划 -->
  <div class="deployment-plan">
    <div class="search-wrapper">
      <div class="search-left-wrapper">
        <tvt-select
          v-model="filterCond.packageTag"
          :placeholder="$t('versionTab.packageTagInput')"
          clearable
          style="margin-right: 20px"
          @change="getExecuteList(1)"
        >
          <el-option
            v-for="(item, index) in packageTagList"
            :key="index"
            :label="item"
            :value="item"
          />
        </tvt-select>
        <search-input
          v-model="filterCond.projectName"
          :placeholder="$t('deployPlanTab.microserviceName')"
          clearable
          size="small"
          maxlength="32"
          style="width: 250px; margin-right: 20px"
          @change="getExecuteList(1)"
        />
        <el-button
          v-if="$isAuthorityExist(['devops_deploy_deploy_mgr_java'])"
          type="primary"
          round
          size="small"
          @click="handleExecute(1)"
        >
          {{ $t('deployPlanTab.executeDeployment') }}
        </el-button>
        <el-button
          v-if="$isAuthorityExist(['devops_deploy_deploy_mgr_java'])"
          type="primary"
          round
          size="small"
          @click="handleExecute(2)"
        >
          {{ $t('deployPlanTab.executeOpenSource') }}
        </el-button>
        <el-button
          v-if="$isAuthorityExist(['devops_deploy_deploy_mgr_p2p'])"
          type="primary"
          round
          size="small"
          @click="handleExecute(3)"
        >
          {{ $t('deployPlanTab.executeCPlusService') }}
        </el-button>
        <el-button
          v-if="$isAuthorityExist(['devops_deploy_deploy_mgr_p2p'])"
          type="primary"
          round
          size="small"
          @click="handleExecute(4)"
        >
          {{ $t('deployPlanTab.executeP2pWeb') }}
        </el-button>
      </div>
    </div>
    <!-- 执行部署表格 -->
    <div class="table-wrapper2">
      <tvt-table
        v-myLoading="loading"
        :data="executeList"
        :columns="executeColumns"
        :border-bottom="true"
        :border="true"
        :pagination="{
          total: executeTotal,
          current: filterCond.current,
          size: filterCond.size,
          'page-sizes': $tablePageSizes,
          background: true,
        }"
        :row-class-name="tableRowClassName"
        @onFetchData="getExecuteList"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.prop === 'deployStatus'">
            <span>{{ dealStatus(row.deployStatus) }}</span>
          </template>
          <template v-if="column.prop === 'sqlUpStatus'">
            <span>{{ dealStatus(row.sqlUpStatus) }}</span>
          </template>
          <template v-if="column.prop === 'endTime'">
            <span>{{ stampToStrLongMethod(row.endTime) }}</span>
          </template>
          <template v-if="column.prop === 'sqlUpCost'">
            <span>{{ costToStrMethod(row.sqlUpCost) }}</span>
          </template>
          <template v-if="column.prop === 'deployCost'">
            <span>{{ costToStrMethod(row.deployCost) }}</span>
          </template>
          <template v-if="column.prop === 'logs'">
            <span class="btn-text" round @click="deployLogDetail(row)">{{
              $t('detail')
            }}</span>
          </template>
        </template>
      </tvt-table>
    </div>
    <tvt-dialog
      :title="$t('deployPlanTab.deployLog')"
      :show.sync="showDetail"
      width="900px"
      :cancel-show="false"
      :submit-text="$t('close')"
      :modal-append-to-body="false"
      @close="closeDetail"
      @Submit="closeDetail"
    >
      <div
        v-if="contentDetail"
        v-dompurify-html="contentDetail"
        class="pre-detail"
      />
      <div v-if="!contentDetail" class="no-detail">
        {{ $t('noData') }}
      </div>
    </tvt-dialog>
    <tvt-dialog
      :title="joinDeployTitle"
      :show.sync="showJoinDeployDialog"
      width="500px"
      :cancel-show="false"
      :submit-text="$t('close')"
      :modal-append-to-body="false"
      @close="closeJoinDeployDialog"
      @Submit="closeJoinDeployDialog"
    >
      <ul>
        <li v-for="(item, index) in showJoinDeployErrors" :key="index">
          {{ item }}
        </li>
      </ul>
    </tvt-dialog>
    <tip-dialog
      ref="tipDialogRef"
      title="configTab.info"
      :tip-msg="$t('configTab.delete')"
      @submit="handleDelete"
    />
    <execute-deployment ref="executeDeployment" @btnSave="configSave" />
    <execute-p2p-deployment ref="executeP2pDeployment" @btnSave="executeP2p" />
  </div>
</template>

<script>
import {
  deployTaskDelete,
  deployTaskTaskList,
  getPackageTagList,
  checkPreDeploy,
  excuteDeployTask,
  deployPatch,
  excuteP2PDeploy,
} from '@/api/installDeploy.js'
import { addObjKey, debounce, stampToStrLong } from '@/utils/common'
import TipDialog from '@/components/common/TipDialog.vue'
import { mapState } from 'vuex'
import executeDeployment from '@/views/install-deploy/deployment-plan/ExecuteDeployment'
import executeP2pDeployment from '@/views/install-deploy/deployment-plan/ExecuteP2pDeployment'
import { getLocale } from '@/lang'

const lang = getLocale() || 'zh-CN'

export default {
  name: 'DeploymentPlan',
  components: {
    TipDialog,
    executeDeployment,
    executeP2pDeployment,
  },
  data() {
    return {
      packageTagList: [], //包标签下拉框
      orderList: [],
      executeList: [],
      loading: false,
      executeColumns: [
        { label: this.$t('index'), type: 'index', width: 60 },
        {
          label: this.$t('deployPlanTab.tableName'),
          prop: 'packageTag',
          width: lang === 'zh-CN' ? 150 : 140,
        },
        {
          label: this.$t('deployPlanTab.deployType'),
          prop: 'deployType',
          width: lang === 'zh-CN' ? 140 : 120,
        },
        {
          label: this.$t('deployPlanTab.projectName'),
          prop: 'projectName',
          'min-width': lang === 'zh-CN' ? 180 : 160,
        },
        {
          label: this.$t('deployPlanTab.version'),
          prop: 'version',
          width: lang === 'zh-CN' ? 220 : 200,
        },
        {
          label: this.$t('deployPlanTab.hostIp'),
          prop: 'hostIp',
          width: lang === 'zh-CN' ? 140 : 120,
        },
        {
          label: this.$t('deployPlanTab.instanceId'),
          prop: 'instanceId',
          width: 100,
        },
        {
          label: this.$t('deployPlanTab.projectPort'),
          prop: 'projectPort',
          width: lang === 'zh-CN' ? 100 : 80,
        },
        {
          label: this.$t('deployPlanTab.startMaxMemory'),
          prop: 'startXmx',
          width: 140,
        },
        {
          label: this.$t('deployPlanTab.deployStatus'),
          prop: 'deployStatus',
          slotName: 'bodyCell',
          width: lang === 'zh-CN' ? 120 : 180,
        },
        {
          label: this.$t('deployPlanTab.deployEndTime'),
          prop: 'endTime',
          slotName: 'bodyCell',
          width: 170,
        },
        {
          label: this.$t('deployPlanTab.totalCost'),
          prop: 'deployCost',
          slotName: 'bodyCell',
          width: lang === 'zh-CN' ? 120 : 170,
        },
        {
          label: this.$t('deployPlanTab.remark'),
          prop: 'deployRemark',
          width: lang === 'zh-CN' ? 140 : 120,
        },
        {
          label: this.$t('operation'),
          prop: 'logs',
          slotName: 'bodyCell',
          fixed: 'right',
          width: 100,
        },
      ],
      filterCond: {
        current: 1,
        size: 20,
        projectName: '',
        packageTag: '',
      },
      executeTotal: 0,
      execute_Id: [], //表格select复选框选中的id
      showDetail: false,
      contentDetail: '',
      deployTaskDeleteId: null,
      deleteIndex: null,
      deleteLength: null,
      showJoinDeployDialog: false,
      joinDeployTitle: '',
      showJoinDeployErrors: [],
    }
  },
  computed: {
    ...mapState('params', ['envDcInfo', 'environmentCode', 'dcName']),
  },
  mounted() {
    this.packageTagListOption()
    this.getExecuteList(1)
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    tableRowClassName({ row }) {
      if (row.deployStatus === '3') {
        // 部署失败
        return 'warning-row'
      }
      return ''
    },
    // 单数据删除
    deployLogDelete(row, index) {
      this.deployTaskDeleteId = row.id
      this.deleteIndex = index
      this.deleteLength = this.executeList.length
      this.$refs.tipDialogRef.open()
    },
    handleDelete() {
      this.deleteDeployTask(this.deployTaskDeleteId)
    },
    handleQuery() {
      this.getExecuteList(1)
    },
    deleteDeployTask(id) {
      let data = [id]
      deployTaskDelete(data)
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            this.$message.success(this.$t('deleteSuccess'))
            if (this.deleteIndex == 0 && this.deleteLength == 1) {
              this.getExecuteList(1) //如果当前页只有一项 就把页面归到第一页
            } else {
              this.getExecuteList()
            }
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    stampToStrLongMethod(time) {
      let intTime = parseInt(time) //接口给的字符串时间
      return stampToStrLong(intTime)
    },
    costToStrMethod(cost) {
      if (!cost) {
        return '--'
      }
      return cost
    },
    closeDetail() {
      this.showDetail = false
    },
    closeJoinDeployDialog() {
      this.showJoinDeployDialog = false
    },
    // 查看详情
    deployLogDetail(row) {
      this.showDetail = true
      let detail = ''
      if (row.logs) {
        detail = row.logs.replace(/\\n/g, '</br>')
      }
      this.contentDetail = detail
    },
    // 回显状态
    dealStatus(index) {
      let intIndex = parseInt(index)
      let arr = [
        this.$t('deployPlanTab.status0'),
        this.$t('deployPlanTab.status1'),
        this.$t('deployPlanTab.status2'),
        this.$t('deployPlanTab.status3'),
      ]
      return arr[intIndex]
    },
    // 获取配套表标签接口
    packageTagListOption(data = []) {
      getPackageTagList(data)
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            this.packageTagList = res.data
          } else {
            this.packageTagList = []
          }
        })
        .catch((err) => {
          this.packageTagList = []
          console.log(err)
        })
    },
    // 分页获取部署任务列表
    getExecuteList: debounce(async function (pageSize) {
      this.loading = true
      if (pageSize == 1) {
        this.filterCond.current = 1
      } else if (pageSize && pageSize.current) {
        this.filterCond.current = pageSize.current
        this.filterCond.size = pageSize.size
      }
      const data = addObjKey(this.filterCond)
      // 查询过滤参数
      data.pageNum = this.filterCond.current
      data.pageSize = this.filterCond.size
      const { environmentCode, dcName } = this
      data.environmentCode = environmentCode
      data.dcName = dcName || undefined
      delete data['current']
      delete data['size']
      data.lang = lang
      return deployTaskTaskList(data)
        .then((res) => {
          this.loading = false
          if (res.basic && res.basic.code == 200) {
            if (res.data) {
              this.executeList = res.data.records
              this.executeTotal = parseInt(res.data.total)
            } else {
              this.executeList = []
            }
            // 定时器刷新 -- 如果有部署中的任务，则刷新列表直到部署完成/失败
            // 执行定时器
            this.timerRefresh()
          }
        })
        .catch((err) => {
          this.loading = false
          console.log(err)
        })
    }, 500),
    handleExecute(deployType) {
      if (deployType === 4) {
        // 部署P2P服务单独处理
        this.$refs.executeP2pDeployment.open()
      } else {
        this.$refs.executeDeployment.open(deployType)
      }
    },
    handleDeployPatch() {
      this.$refs.deployPatch.open()
    },
    // 执行部署
    configSave(saveData) {
      this.$TvtLoading.show({ text: this.$t('LoadingMsg') })

      // 从新的数据结构中提取数据
      const {
        deploymentMode,
        packageTag,
        multipleSelection,
        deployType,
        packages,
        deployRemark,
      } = saveData

      // 判断是否为补丁部署
      if (deploymentMode === 'patch') {
        // 补丁部署逻辑，参照 DeployPatch.vue 中的 btnSave 函数
        // packages需要根据multipleSelection来处理,注意：选择了java服务需要额外保留openSource包
        const projectNameSet = new Set(
          multipleSelection.map((item) => item.projectName)
        )

        // 检查是否选择了java服务
        const hasJavaService = multipleSelection.some(
          (item) => item.projectName && item.projectName.includes('Service')
        )

        let processedPackages = packages.filter((item) =>
          projectNameSet.has(item.projectName)
        )

        // 特殊判断：当选择了java服务时，需要额外保留openSource服务
        if (hasJavaService) {
          const openSourcePackages = packages.filter(
            (item) =>
              item.name &&
              item.name.toLowerCase().includes('opensourcedependency')
          )
          // 合并java服务包和openSource包，去重
          const packageMap = new Map()

          // 先添加已选择的包
          processedPackages.forEach((pkg) => packageMap.set(pkg.path, pkg))
          // 再添加openSource包（如果path相同会自动覆盖，实现去重）
          openSourcePackages.forEach((pkg) => packageMap.set(pkg.path, pkg))

          processedPackages = Array.from(packageMap.values())
        }
        const projectNames = multipleSelection.map((item) => {
          return {
            projectName: item.projectName,
            instanceId: item.instanceId,
          }
        })

        // 调用补丁部署API
        deployPatch({
          packages: processedPackages,
          projectNames,
          deployRemark,
          serviceType: 'patch',
        })
          .then((res) => {
            this.$TvtLoading.hide()
            if (res.basic && res.basic.code == 200) {
              // 刷新部署列表
              this.getExecuteList(1)
            }
          })
          .catch((err) => {
            this.$TvtLoading.hide()
            console.log(err)
          })
      } else {
        // 配套表部署逻辑（原有逻辑）
        checkPreDeploy(multipleSelection).then((res) => {
          // 验证
          this.$TvtLoading.hide()
          if (res.data && res.data.length > 0) {
            this.showJoinDeployDialog = true
            this.joinDeployTitle = this.$t('deployPlanTab.joinDeployError')
            this.showJoinDeployErrors = res.data
            return
          }
          this.executeDeployment(
            packageTag,
            multipleSelection,
            deployType,
            packages
          )
        })
      }
    },
    // 定时器刷新 -- 如果有部署中的任务，则刷新列表直到部署完成/失败
    timerRefresh() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      let excuteFlag = false
      for (const element of this.executeList) {
        if (element.deployStatus == 0 || element.deployStatus == 1) {
          excuteFlag = true //部署列表 存在待部署， 部署中
        }
      }
      const that = this
      if (excuteFlag) {
        that.timer = setInterval(() => {
          that.getExecuteList(1)
        }, 10000)
      } else {
        clearInterval(that.timer)
        that.timer = null
      }
    },
    executeDeployment(tagName, joinData, deployType, patchPackages = []) {
      let projectNames = joinData.map((item) => {
        return {
          projectName: item.projectName,
          instanceId: item.instanceId,
        }
      })

      // 构建请求参数，包含补丁包信息
      const requestData = {
        deployType,
        tagName,
        projectNames,
        lang,
      }

      // 如果有补丁包，添加到请求参数中
      if (patchPackages && patchPackages.length > 0) {
        requestData.patchPackages = patchPackages.map((item) => {
          return {
            name: item.name,
            path: item.path,
            packageName: item.businessParams && item.businessParams.packageName,
            projectName: item.businessParams && item.businessParams.projectName,
            version: item.businessParams && item.businessParams.version,
          }
        })
      }

      excuteDeployTask(requestData)
        .then((res) => {
          this.loading = false
          if (res.basic && res.basic.code == 200) {
            if (res.data && res.data.length > 0) {
              this.showJoinDeployDialog = true
              this.joinDeployTitle = this.$t('deployPlanTab.executeDeployError')
              this.showJoinDeployErrors = res.data
              this.getExecuteList(1)
              return
            }
            // 执行部署成功  定时器 刷 部署状态的结果
            this.getExecuteList(1)
          }
        })
        .catch((err) => {
          this.loading = false
          console.log(err)
        })
    },
    // 部署P2P Web
    async executeP2p(params) {
      this.loading = true
      try {
        const res = await excuteP2PDeploy(params)
        this.loading = false
        let timer = null
        if (res.basic && res.basic.code == 200) {
          if (res.data && res.data.length > 0) {
            this.showJoinDeployDialog = true
            this.joinDeployTitle = this.$t('deployPlanTab.executeDeployError')
            this.showJoinDeployErrors = res.data
            this.getExecuteList(1)
            return
          }
          // 执行部署成功  定时器 刷 部署状态的结果
          this.getExecuteList(1)
          setTimeout(() => {
            timer = setInterval(() => {
              let excuteFlag = false
              for (const element of this.executeList) {
                if (element.deployStatus == 0 || element.deployStatus == 1) {
                  excuteFlag = true //部署列表 存在待部署， 部署中
                }
              }
              if (excuteFlag) {
                this.getExecuteList(1)
              } else {
                clearInterval(timer)
              }
            }, 10000)
          }, 10000)
        }
      } catch (err) {
        this.loading = false
        console.log(err)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.deployment-plan {
  margin: 24px;
  width: calc(100% - 48px);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;

  .search-wrapper {
    width: 100%;
    margin: 0px 0px 20px 0;
    .search-left-wrapper {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
    }
    .executeBtn {
      float: right;
      margin-bottom: 20px;
    }

    .executeBtnDelete {
      float: right;
      margin-right: 20px;
    }
  }

  .table-wrapper2 {
    flex: 1;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    min-height: 600px;
  }

  .btn-text {
    color: #429efd !important;
    cursor: pointer;

    + .btn-text {
      margin-left: 20px;
    }
  }

  .pre-detail {
    line-height: 24px;
    white-space: pre-line;
  }

  .no-detail {
    width: 600px;
    text-align: center;
  }
}
</style>
<style>
.deployment-plan .el-input__inner {
  height: 32px;
  line-height: 32px;
}

.search-wrapper .el-input__inner {
  width: 250px;
}

.deployment-plan .el-table .el-table__cell {
  padding: 8px 0 !important;
}
</style>
<style>
.deployment-plan .search-wrapper .el-input__inner {
  height: 32px;
  line-height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 16px;
}

.deployment-plan .search-wrapper .el-input__icon {
  line-height: 34px;
}

.deployment-plan .search-wrapper .tvt-input .tvt-field-set {
  border: none;
}
.deployment-plan .search-wrapper .tvt-select .tvt-field-set {
  border: none;
}

.deployment-plan .table-wrapper2 .el-table .el-table__cell {
  padding: 8px 0 !important;
}
.deployment-plan .warning-row {
  background: #f56c6c;
}
</style>
