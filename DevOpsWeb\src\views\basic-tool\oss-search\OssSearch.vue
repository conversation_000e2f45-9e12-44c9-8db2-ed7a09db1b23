<template>
  <!-- oss/s3日志 -->
  <div class="page-wrapper">
    <div class="search-wrapper oss-search-warpper">
      <tvt-select
        v-model="filterCond.bucketName"
        :options="bucketOptions"
        :mockplaceholder="$t('ossSearchTab.bucketSelect')"
        :clearable="false"
        style="width: 300px; margin-right: 20px"
        @change="handleBucketName"
      />
      <search-input
        v-model="filterCond.keyword"
        :placeholder="$t('kafkaSearchTab.prefixFilter')"
        clearable
        size="small"
        maxlength="32"
        style="width: 300px; margin-right: 20px"
        @change="getOssList"
      />
      <div class="search-btn-box">
        <el-button type="primary" size="small" round @click="handleReset">{{
          $t('reset')
        }}</el-button>
        <el-button type="primary" size="small" round @click="handleRefresh">{{
          $t('refresh')
        }}</el-button>
      </div>
      <div class="search-left-wrapper"></div>
    </div>
    <div class="oss-breadcrumb">
      <div class="breadcrumb-box">
        <div class="breakcrumb-item">
          <span class="breadcrumb-separator">/</span>
        </div>
        <div
          v-for="(path, index) of pathList"
          :key="path"
          class="breakcrumb-item"
        >
          <span class="breadcrumb-text" @click="clickBread(index)">{{
            path
          }}</span>
          <span class="breadcrumb-separator">/</span>
        </div>
      </div>
    </div>
    <!-- oss/s3表格 -->
    <div class="table-wrapper">
      <tvt-table
        ref="myTable"
        v-myLoading="loading"
        :data="ossList"
        :columns="ossColumn"
        :border="true"
        :border-bottom="true"
        :pagination="false"
        @onFetchData="getOssList"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.prop === 'key'">
            <span
              :class="row.size === '0B' ? 'oss-folder' : 'oss-file'"
              @click="row.size === '0B' ? clickFolder(row) : null"
            >
              <i
                v-if="row.size === '0B'"
                class="el-icon-folder"
                style="color: #429efd"
              ></i>
              <span>{{ row[column.prop] }}</span>
            </span>
          </template>
          <template v-if="column.prop === 'size'">
            <span>{{
              row.size === '0B' ? $t('ossSearchTab.folder') : row[column.prop]
            }}</span>
          </template>
          <template v-if="column.prop === 'operation'">
            <span
              v-if="row.size !== '0B'"
              class="btn-text"
              @click="downloadFile(row)"
              >{{ $t('download') }}</span
            >
          </template>
        </template>
      </tvt-table>
    </div>
  </div>
</template>
<script>
import { debounce, hanldeDownload } from '@/utils/common'
import {
  getOssBucketList,
  getOssFileList,
  downloadOssObject,
} from '@/api/basicTool'
export default {
  name: 'OssSearch',
  components: {},
  data() {
    return {
      total: 0,
      filterCond: {
        bucketName: '',
        keyword: '',
      },
      loading: false,
      pathList: [],
      bucketTypeObj: [], // 桶名称及type对应关系
      bucketOptions: [], // 桶下拉选项
      ossList: [],
      ossColumn: [
        {
          label: this.$t('ossSearchTab.name'),
          prop: 'key',
          slotName: 'bodyCell',
          minWidth: 150,
        },
        {
          label: this.$t('ossSearchTab.typeSize'),
          prop: 'size',
          slotName: 'bodyCell',
          width: 200,
        },
        {
          label: this.$t('ossSearchTab.modifyTime'),
          prop: 'lastModified',
          width: 200,
        },
        {
          label: this.$t('operation'),
          prop: 'operation',
          slotName: 'bodyCell',
          fixed: 'right',
          width: 100,
          hide: !this.$isAuthorityExist(['devops_tool_oss_mgr']),
        },
      ],
      downloadSet: new Set(), // 记录下载的日志
    }
  },
  async mounted() {
    await this.getBucketList()
    this.getOssList()
  },
  methods: {
    // 获取桶列表
    async getBucketList() {
      try {
        const { data = [] } = await getOssBucketList({})
        const bucketTypeObj = {}
        const bucketOptions = []
        data.forEach((item) => {
          bucketTypeObj[item.bucketName] = item.type
          bucketOptions.push({
            label: item.bucketName,
            value: item.bucketName,
          })
        })
        this.bucketTypeObj = bucketTypeObj
        this.bucketOptions = bucketOptions
        // 桶列表默认选中第一个
        this.filterCond.bucketName = data[0].bucketName
      } catch (error) {
        console.error(error)
      }
    },
    // 获取OSS文件列表
    getOssList: debounce(async function () {
      this.loading = true
      try {
        const { bucketName, keyword } = this.filterCond
        const params = {}
        if (bucketName) {
          params.bucketName = bucketName
          params.type = this.bucketTypeObj[bucketName]
        }
        let path = ''
        if (this.pathList && this.pathList.length) {
          path = this.pathList.slice().join('/') + '/'
        }
        if (keyword) {
          path += keyword
        }
        if (path.length) {
          params.key = path
        }
        const { data = [] } = await getOssFileList(params)
        if (this.pathList.length) {
          // 进入某个目录则加一个返回上一级
          data.unshift({
            key: '..',
            size: '0B',
            lastModified: null,
          })
        }
        this.ossList = data
        this.loading = false
      } catch (error) {
        console.error(error)
        this.loading = false
      }
    }, 200),
    handleBucketName(val) {
      console.log('桶变化', val)
      this.pathList = []
      this.getOssList()
    },
    handleReset() {
      this.pathList = []
      this.filterCond = {
        bucketName: this.bucketOptions[0].value,
        keyword: '',
      }
      this.getOssList()
    },
    handleRefresh() {
      this.getOssList()
    },
    clickRoot() {
      this.pathList = []
      this.$nextTick(() => {
        this.getOssList()
      })
    },
    clickBread(index) {
      if (index < this.pathList.length - 1) {
        const pathList = this.pathList.slice(0, index + 1)
        this.pathList = pathList
        this.$nextTick(() => {
          this.getOssList()
        })
      }
    },
    clickFolder(row) {
      if (row.key === '..') {
        // 表示返回上一级
        const pathList = this.pathList.slice()
        pathList.pop()
        this.pathList = pathList
        this.$nextTick(() => {
          this.getOssList()
        })
      } else {
        // 其他目录情况
        const { key } = row
        const pathList = key.split('/').filter((item) => item)
        this.pathList = pathList
        this.$nextTick(() => {
          this.getOssList()
        })
      }
    },
    async downloadFile(row) {
      const { bucketName } = this.filterCond
      const { key } = row
      const params = {
        url: key,
        type: 1, // 1 OSS类 2 日志类
        bucketName,
        bucketType: this.bucketTypeObj[bucketName],
      }
      // 判断url是否在10s内点击过，如果点击过则提示频率太快
      if (this.downloadSet.has(key)) {
        this.$message.warning(this.$t('microLogTab.downloadFreq'))
        return
      }
      this.downloadSet.add(key)

      // 添加loading效果
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, .5)',
        text: this.$t('LoadingDownload'), // 或者使用 '下载中...'
      })

      try {
        const resBlob = await downloadOssObject(params)
        let reader = new FileReader()
        reader.onload = () => {
          hanldeDownload(resBlob, `${key}`)
          // 定时器10s后放开下载限制
          setTimeout(() => {
            this.downloadSet.delete(key)
          }, 10000)
          loading.close()
        }
        reader.readAsText(resBlob)
      } catch (error) {
        console.error('下载文件失败:', error)
        this.downloadSet.delete(key)
        loading.close()
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.oss-breadcrumb {
  width: 100%;
  padding: 5px 24px;
  box-sizing: border-box;
  .breadcrumb-box {
    font-size: 14px;
    line-height: 1;
    display: inline-block;
    justify-content: flex-start;
    align-items: center;
    .breakcrumb-item {
      display: inline;
      .breadcrumb-separator {
        display: inline-block;
        padding: 0 9px;
        font-weight: bold;
        color: #429efd;
      }
      .breadcrumb-text {
        cursor: pointer;
        font-weight: bold;
        text-decoration: none;
        transition: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        color: #303133;
      }
    }
  }
}
::v-deep .oss-search-warpper .el-input__inner {
  width: 300px;
}

::v-deep .oss-search-warpper .search-input {
  width: 300px;
}
::v-deep .oss-folder {
  color: #429efd;
  cursor: pointer;
}

::v-deep .btn-text {
  color: #429efd !important;
  cursor: pointer;
  + .btn-text {
    margin-left: 20px;
  }
}
</style>
