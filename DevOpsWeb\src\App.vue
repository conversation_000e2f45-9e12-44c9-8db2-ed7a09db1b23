<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App',
  components: {},
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}
html,
body,
#app {
  width: 100%;
  height: 100%;
}
* {
  padding: 0;
  margin: 0;
  border: 0;
}
ul,
li {
  list-style: none;
}
/* loading 颜色 */
.loading-box .loadingImg {
  color: #429efd !important;
}
.loading-content {
  color: #429efd !important;
}
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  cursor: pointer;
}
/* 滚动槽 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: rgba(0, 0, 0, 0.3);
  box-shadow: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  cursor: pointer;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(17, 16, 16, 0.13);
  -webkit-box-shadow: rgba(0, 0, 0, 0.9);
  box-shadow: rgba(0, 0, 0, 0.5);
  cursor: pointer;
}
::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(211, 173, 209, 0.4);
  cursor: pointer;
}

/* Element UI滚动条手形鼠标样式 */
.el-scrollbar__bar {
  cursor: pointer;
}
.el-scrollbar__bar .el-scrollbar__thumb {
  cursor: pointer;
}

/* 表格滚动条手形鼠标样式 */
.el-table__body-wrapper::-webkit-scrollbar {
  cursor: pointer;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  cursor: pointer;
}
.el-table__body-wrapper::-webkit-scrollbar-track {
  cursor: pointer;
}
</style>
