<template>
  <div
    :class="[
      'tu-date-custom',
      isSelect ? 'select-color' : '',
      clearable ? '' : 'tu-date-un-clearable',
      from == 2 ? 'search-use' : '',
      isActive ? 'tu-date-custom-active' : '',
    ]"
  >
    <div ref="labelRef" class="label">
      <span v-if="showCustomDate(timeRange)" class="label-time">{{
        showCustomDate(timeRange)
      }}</span>
      <span v-else class="label-time-placeholder">{{
        $t('timePlaceholder')
      }}</span>
    </div>
    <div
      :class="['date-time-picker', isActive ? 'is-active' : '']"
      :style="`width: ${mockwidth}`"
    >
      <el-date-picker
        ref="dateTimePicker"
        v-model="timeRange"
        :clearable="false"
        type="datetimerange"
        range-separator="-"
        format="yyyy-MM-dd HH:mm:ss"
        :popper-class="'tu-date-custom-search ' + uniqueClass"
        :default-time="defaultTime"
        :picker-options="optionType === 1 ? pickerOptions : pickerOptions2"
        :start-placeholder="$t('startDate')"
        :end-placeholder="$t('endDate')"
        size="small"
        @blur="handleBlur"
        @change="handleChange"
      />
    </div>
    <div
      v-if="clearable && isSelect"
      class="close-all"
      @click.prevent.stop="closeTime"
    >
      <svg-img viewBox="0 0 20 20" width="20" height="20">
        <icon-close icon-fill="#2C5BFA" />
      </svg-img>
    </div>
    <div
      :class="['date-suffix-icon', isActive ? 'is-active' : '']"
      @click="handleFocus"
    >
      <svg-img viewBox="0 0 26 26" width="26" height="26" class="icon-arrow">
        <icon-arrow :icon-fill="isSelect ? '#2C5BFA' : '#4A4A4A'" />
      </svg-img>
    </div>
    <div class="data-picker-modal" @click="handleClick" />
  </div>
</template>
<script>
import SvgImg from '@/components/svg-icon/SvgIcon.vue'
import IconArrow from '@/components/svg-icon/alerts/IconArrow'
import IconClose from '@/components/svg-icon/alerts/IconClose'
import { getLocale } from '@/lang'
const dateTimeRange = (date, num = 0, type = 'days', format) => {
  const endTime = (date ? window.moment(date) : window.moment()).set({
    hour: 23,
    minute: 59,
    second: 59,
  })
  const startTime = (date ? window.moment(date) : window.moment())
    .subtract(num, type)
    .set({ hour: 0, minute: 0, second: 0 })
  if (!format) {
    return [startTime.toDate(), endTime.toDate()]
  } else {
    return [startTime.format(format), endTime.format(format)]
  }
}
const formatDate = (d) => {
  return dateTimeRange('', d)
}

const formatMonth = (d) => {
  return dateTimeRange('', d, 'months')
}

const formatYear = (d) => {
  return dateTimeRange('', d, 'years')
}

// 过去一小时
const dateTimeRangeHour = (num) => {
  let start = new Date()
  start.setHours(start.getHours() - num)
  let end = new Date()
  return [start, end]
}

export default {
  name: 'TuDateCustom',
  components: {
    SvgImg,
    IconArrow,
    IconClose,
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    default: {
      //初始化默认查多久的时间 一般是当天或者近7天
      type: Array,
      default: () => [],
    },
    value: {
      type: Array,
      default: () => [],
    },
    isConnectPlugin: {
      //是否涉及插件 跟插件有关的要进行挖去
      type: Boolean,
      default: false,
    },
    uniqueClass: {
      //唯一的class 主要用于插件的唯一识别区
      type: String,
      default: '',
    },
    from: {
      type: Number,
      default: 2, //来自哪里用 控制样式 默认报警用 不控制宽度 搜索：2
    },
    backToDefault: {
      //默认回到近七天 可以通过传参 今天传0
      type: Number,
      default: 6,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    optionType: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      mockwidth: '140px',
      isActive: false,
      isSelect: false,
      isNotClear: true, //是否是清除 默认不是 点了清除按钮才是
      defaultTime: ['00:00:00', '23:59:59'],
      cond: {
        // 查询条件
        startTime: '',
        endTime: '',
      },
      timeRange: [],
      chooseType: 'custom', //默认是按时间的  近1小时 : 1   今天 :2  昨天：3  近7天：4  近30天 ：5
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() >= dateTimeRange('', 0)[1].valueOf()
        },
        shortcuts: [
          {
            text: this.$t('lastHour'),
            onClick: (picker) => this.handleHourClick(picker, 1),
          },
          {
            text: this.$t('last3Hour'),
            onClick: (picker) => this.handleHourClick(picker, 3),
          },
          {
            text: this.$t('last6Hour'),
            onClick: (picker) => this.handleHourClick(picker, 6),
          },
          {
            text: this.$t('last12Hour'),
            onClick: (picker) => this.handleHourClick(picker, 12),
          },
          {
            text: this.$t('last24Hours'),
            onClick: (picker) => this.handleHourClick(picker, 24),
          },
          {
            text: this.$t('today'),
            onClick: (picker) => this.handleformatDateClick(picker, 0),
          },
          {
            text: this.$t('yesterday'),
            onClick: (picker) => this.handleYesterdayClick(picker),
          },
          {
            text: this.$t('last7d'),
            onClick: (picker) => this.handleformatDateClick(picker, 6),
          },
          {
            text: this.$t('last30d'),
            onClick: (picker) => this.handleformatDateClick(picker, 29),
          },
          {
            text: this.$t('last3m'),
            onClick: (picker) => this.handleformatMonthClick(picker, 3),
          },
          {
            text: this.$t('last6m'),
            onClick: (picker) => this.handleformatMonthClick(picker, 6),
          },
          {
            text: this.$t('last1y'),
            onClick: (picker) => this.handleformatYearClick(picker, 1),
          },
          {
            text: this.$t('last2y'),
            onClick: (picker) => this.handleformatYearClick(picker, 2),
          },
        ],
      },
      pickerOptions2: {
        disabledDate: (time) => {
          return time.getTime() >= dateTimeRange('', 0)[1].valueOf()
        },
        shortcuts: [
          {
            text: this.$t('lastHour'),
            onClick: (picker) => this.handleHourClick(picker, 1),
          },
          {
            text: this.$t('last3Hour'),
            onClick: (picker) => this.handleHourClick(picker, 3),
          },
          {
            text: this.$t('last6Hour'),
            onClick: (picker) => this.handleHourClick(picker, 6),
          },
          {
            text: this.$t('last12Hour'),
            onClick: (picker) => this.handleHourClick(picker, 12),
          },
          {
            text: this.$t('last24Hours'),
            onClick: (picker) => this.handleHourClick(picker, 24),
          },
          {
            text: this.$t('today'),
            onClick: (picker) => this.handleformatDateClick(picker, 0),
          },
          {
            text: this.$t('yesterday'),
            onClick: (picker) => this.handleYesterdayClick(picker),
          },
          {
            text: this.$t('last7d'),
            onClick: (picker) => this.handleformatDateClick(picker, 6),
          },
          {
            text: this.$t('last30d'),
            onClick: (picker) => this.handleformatDateClick(picker, 29),
          },
        ],
      },
    }
  },
  computed: {
    combinedValue() {
      return { timeRange: this.timeRange, chooseType: this.chooseType }
    },
  },
  watch: {
    value: {
      handler(val) {
        // console.log('val', val)
        if (JSON.stringify(val) !== JSON.stringify(this.timeRange)) {
          // 值不一样则更新
          this.timeRange = val || []
          this.chooseType = 'custom' // 值变化驱动的均重置chooseType
        }
      },
      deep: true,
      immediate: true,
    },
    timeRange: {
      handler(val, oldVal) {
        // 处理宽度
        if (val && val.length && this.isNotClear) {
          this.isSelect = true
        } else {
          this.isSelect = false
        }
        this.$nextTick(() => {
          if (this.$refs.labelRef) {
            this.mockwidth = this.$refs.labelRef.offsetWidth - 18 + 'px'
          }
        })
        // 处理时间
        if (val && val.length) {
          // if (val[1] - val[0] > 3600 * 1000 * 24 * 31) { // 最大时间范围为31天
          //   this.$message.warning(this.$t('limit31d'))
          //   this.timeRange = [oldVal[0], oldVal[1]]
          //   return
          // }
          if (val[0] - val[1] >= 0) {
            // 时间控件自己应该会处理
            const msg = window.errorCode[getLocale()]['33084']
            this.$message.warning(msg)
            this.timeRange = [oldVal[0], oldVal[1]]
            return
          }
        }
      },
      deep: true,
      immediate: true,
    },
    combinedValue: {
      handler(val) {
        const { timeRange, chooseType } = val
        // let newTimeRange = []
        if (timeRange && timeRange.length) {
          // 近1小时是动态变化的 这里要传过去更正时间
          this.cond.chooseType = chooseType
          const startTime = timeRange[0]
            ? new Date(timeRange[0]).getTime()
            : null
          const endTime = timeRange[1] ? new Date(timeRange[1]).getTime() : null
          this.cond = {
            startTime,
            endTime,
            chooseType,
          }
          // newTimeRange = [startTime, endTime]
          // 这里是聚焦失焦的时候不用查接口
          // if (val.chooseType == 1 && oldVal.chooseType == 1 && this.chooseType == 1) return
          // console.log(this.cond, '查询时间')
        } else {
          this.cond = {
            startTime: null,
            endTime: null,
            chooseType,
          }
          // newTimeRange = []
        }
        // this.$emit('change', newTimeRange)
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    // 时间转秒
    trans2sec(date) {
      return parseInt(window.moment(date).format('X'))
      // return parseInt(window.moment(date).getTime())
    },
    handleHourClick(picker, num) {
      picker.$emit('pick', dateTimeRangeHour(num))
      this.chooseType = 1
    },
    handleYesterdayClick(picker) {
      picker.$emit(
        'pick',
        dateTimeRange(window.moment().subtract(1, 'days'), 0)
      )
      this.chooseType = 'custom'
    },
    handleformatDateClick(picker, num) {
      picker.$emit('pick', formatDate(num))
      this.chooseType = 'custom'
    },
    handleformatMonthClick(picker, num) {
      picker.$emit('pick', formatMonth(num))
      this.chooseType = 'custom'
    },
    handleformatYearClick(picker, num) {
      picker.$emit('pick', formatYear(num))
      this.chooseType = 'custom'
    },
    handleChange(val) {
      if (this.chooseType === 1 && val[1] - val[0] === 3600 * 1000) {
        // 针对近一小时进行判断
        this.chooseType = 1
      } else {
        this.chooseType = 'custom'
      }
      if (val && val.length) {
        const startTime = val[0] ? new Date(val[0]).getTime() : null
        const endTime = val[1] ? new Date(val[1]).getTime() : null
        const newTimeRange = [startTime, endTime]
        this.$emit('change', newTimeRange)
      } else {
        this.$emit('change', [])
      }
    },
    showCustomDate(params) {
      let value = params
      return this.timeFormat(value[0], value[1])
      // let lastHour = dateTimeRangeHour(1)
      // let today = formatDate(0)
      // let yesterday = dateTimeRange(window.moment().subtract(1, 'days'), 0)
      // let last7d = formatDate(6)
      // let last30d = formatDate(29)
      // if (this.trans2sec(value[0]) == this.trans2sec(lastHour[0]) && this.trans2sec(value[1]) == this.trans2sec(lastHour[1])) {
      //   return this.$t('LastHour')
      // } else if (this.trans2sec(value[0]) == this.trans2sec(today[0]) && this.trans2sec(value[1]) == this.trans2sec(today[1])) {
      //   return this.$t('today')
      // } else if (this.trans2sec(value[0]) == this.trans2sec(yesterday[0]) && this.trans2sec(value[1]) == this.trans2sec(yesterday[1])) {
      //   return this.$t('yesterday')
      // } else if (this.trans2sec(value[0]) == this.trans2sec(last7d[0]) && this.trans2sec(value[1]) == this.trans2sec(last7d[1])) {
      //   return this.$t('last7d')
      // } else if (this.trans2sec(value[0]) == this.trans2sec(last30d[0]) && this.trans2sec(value[1]) == this.trans2sec(last30d[1])) {
      //   return this.$t('last30d')
      // } else {
      //   return this.timeFormat(value[0], value[1])
      // }
    },
    // 将两个时间按格式拼接起来
    timeFormat(start, end) {
      if (start && end) {
        let startTime = this.formatDateMeth(new Date(start))
        let endTime = this.formatDateMeth(new Date(end))
        return startTime + ' ~ ' + endTime
      } else {
        return
      }
    },
    addZero(str = '') {
      return str.toString().length < 2 ? `0${str}` : str
    },
    formatDateMeth(dateObj) {
      if (!dateObj) return
      let year = dateObj.getFullYear()
      let month = this.addZero(dateObj.getMonth() + 1)
      let day = this.addZero(dateObj.getDate())
      let hour = this.addZero(dateObj.getHours())
      let minute = this.addZero(dateObj.getMinutes())
      let second = this.addZero(dateObj.getSeconds())
      return `${year}/${month}/${day} ${hour}:${minute}:${second}`
    },
    handleClick(event) {
      // 去掉边角的空白区域
      const rect = event.target.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      const borderRadius = 18
      // 检查点击位置是否在圆角区域内
      if (
        x > rect.width - borderRadius &&
        (y < rect.width - x || y > rect.width - x + borderRadius)
      ) {
        return
      }
      this.handleFocus()
    },
    handleFocus() {
      setTimeout(() => {
        if (this.isActive) return
        this.isActive = true
        if (this.chooseType == 1) {
          //如果是近一个小时的话 手动更新打开时的时间
          let start = new Date()
          start.setHours(start.getHours() - 1)
          let end = new Date()
          this.timeRange = [start, end]
        }
        setTimeout(() => {
          this.$refs.dateTimePicker.focus()
          this.$emit('visible-change', true)
        }, 10)
      }, 10)
    },
    handleBlur() {
      setTimeout(() => {
        this.isActive = false
        if (this.chooseType == 1) {
          //如果是近一个小时的话 手动更新打开时的时间
          let start = new Date()
          start.setHours(start.getHours() - 1)
          let end = new Date()
          this.timeRange = [start, end]
        }
      }, 10)
      this.$emit('visible-change', false)
    },
    clear() {
      this.chooseType = 'custom'
      this.isNotClear = false
      this.timeRange = []
      this.$emit('change', [])
      setTimeout(() => {
        this.isNotClear = true
      }, 100)
    },
    closeTime() {
      // this.timeRange = formatDate(this.backToDefault)
      this.timeRange = []
      this.clear()
    },
  },
}
</script>
<style lang="scss" scoped>
.tu-date-custom {
  position: relative;
  height: 32px;
  line-height: 32px;
  max-width: 370px;
  min-width: 250px;
  background: white;
  border-radius: 18px;
  user-select: none;
  border: 1px solid #dcdfe6;
  &.search-use {
    max-width: 310px;
  }
  &.select-color {
    // color: #2C5BFA;
    // border: 1px solid #2C5BFA;
    box-sizing: border-box;
    // background: #d4e4fe;
    .label {
      // color: #2C5BFA;
      padding-right: 54px;
    }
  }
  &.tu-date-un-clearable {
    .label {
      padding-right: 34px;
    }
  }
  .label-time-placeholder {
    color: #c6c6c6;
  }
  .label {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #181818;
    padding-left: 10px;
    padding-right: 26px;
    .label-title {
      display: inline-block;
      flex-shrink: 0;
    }
    .label-time {
      padding-left: 2px;
      padding-right: 10px;
      display: inline-block;
      font-size: 12px;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .close-all {
    position: absolute;
    right: 36px;
    top: 0px;
    font-size: 16px;
    z-index: 19;
    cursor: pointer;
  }
  .date-suffix-icon {
    position: absolute;
    right: 10px;
    top: -1px;
    font-size: 16px;
    color: #6d6d6d;
    cursor: pointer;
    z-index: 19;
    transition: transform 0.25s;
    .icon-arrow {
      font-size: 14px;
      margin-bottom: -8px;
    }
    &.is-active {
      top: 0px;
      transform: rotate(-180deg);
    }
  }
  .date-time-picker {
    opacity: 0;
    flex-shrink: 0;
    position: absolute;
    left: 0;
    top: 0;
    border: 0;
    display: flex;
    border-radius: 18px;
    ::v-deep .el-date-picker {
      opacity: 0;
    }
    ::v-deep .el-input__inner {
      border: 0;
    }
    ::v-deep .el-input__prefix {
      display: none;
    }
  }
  .data-picker-modal {
    position: absolute;
    left: 0px;
    top: 0px;
    z-index: 9;
    width: 100%;
    height: 100%;
    border-radius: 18px;
    border: 1px solid transparent;
    cursor: pointer;
  }
}
.tu-date-custom-active {
  border-color: #429efd;
}
</style>
<style lang="scss">
.tu-date-custom-search {
  transition: none !important;
  .el-picker-panel__footer button:first-child {
    display: none;
  }
  .el-picker-panel__body {
    padding-right: 32px;
  }
}
</style>
