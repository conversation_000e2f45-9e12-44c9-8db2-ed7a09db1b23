<template>
  <!-- 对活动类型进行选择 -->
  <el-popover v-model="visible" :placement="placement" style="cursor: pointer">
    <template #reference>
      <slot name="referenceType">
        <i
          class="el-icon-filter icon-hover"
          :title="$t('filter')"
          style="float: right; font-size: 20px"
        />
      </slot>
      <!-- <el-button><Grid /></el-button> -->
    </template>
    <div>
      <el-checkbox-group
        v-model="activityType"
        class="checkbox-wrap"
        @change="dropDownChange"
      >
        <el-checkbox
          v-for="item in options"
          :key="item[defaultProps.value]"
          :label="item[defaultProps.value]"
          size="large"
          style="display: block"
        >
          {{ item[defaultProps.label] }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div style="text-align: right; margin: 10px 0px 0px 0px">
      <el-button size="mini" @click="handleCancel"> 取消 </el-button>
      <el-button type="primary" size="mini" @click="handleConfirm">
        确定
      </el-button>
    </div>
  </el-popover>
</template>
<script>
export default {
  name: 'PopoverChecked',
  props: {
    placement: {
      type: String,
      default: 'left',
    },
    options: {
      type: Array,
      default: () => [],
    },
    initCheck: {
      type: Array,
      default: () => [],
    },
    defaultProps: {
      type: Object,
      default: () => ({
        value: 'value',
        label: 'label',
      }),
    },
  },
  data() {
    return {
      visible: false,
      activityType: [],
    }
  },
  watch: {
    initCheck: {
      handler(val) {
        this.activityType = [...val] || []
      },
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    dropDownChange() {
      // console.log('val', val)
      // this.activityType = val
    },
    handleCancel() {
      this.visible = false
      this.activityType = [...this.initCheck] || []
    },
    handleConfirm() {
      if (this.activityType.length === 0) {
        this.$message.warning(this.$t('pleaseCheckOne'))
        return
      }
      this.visible = false
      this.$emit('handleCheck', this.activityType)
      // console.log('activityType', this.activityType)
    },
  },
}
</script>
<style lang="scss" scoped>
.el-icon-filter:hover {
  color: #409eff;
}
</style>
