<template>
  <!-- 首页-实时监控趋势数据 -->
  <div class="treMon-wrapper">
    <div class="treMon-title">
      {{ title }}
    </div>
    <div class="treMon-content-wrapper">
      <el-collapse v-model="activeNames" class="collapse-wrapper">
        <el-collapse-item
          v-for="item of trendListData"
          :key="`${item.quota}-${item.dcId}`"
          :name="`${item.quota}-${item.dcId}`"
        >
          <template slot="title">
            <div class="collpase-title-wrapper">
              <span class="collpase-title">{{
                `${item.quotaTitle || ''} (${item.dcName || ''})`
              }}</span>
              <span class="collpase-sub-title">{{
                `(${item.colonyNum || 1} pannel)`
              }}</span>
            </div>
          </template>
          <!-- 趋势图 折线图和柱状图 -->
          <monitor-chart
            v-if="item.quotaType === 'metric'"
            :search-cond="{ ...item, areas: item.areas }"
            :collapse-open="
              activeNames | getCollapse(`${item.quota}-${item.dcId}`)
            "
          />
          <!-- 实时告警列表 -->
          <monitor-alarm
            v-else
            :search-cond="{ ...item, areas: item.areas }"
            :collapse-open="
              activeNames | getCollapse(`${item.quota}-${item.dcId}`)
            "
          />
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>
<script>
import MonitorAlarm from './MonitorAlarm.vue'
import MonitorChart from './MonitorChart.vue'
import { metricsPageObj, metricsTypeObj } from './config.js'
import selfLocaLStorage from '@/utils/selfLocalStorage'

export default {
  name: 'TrendMonitor',
  components: {
    MonitorChart,
    MonitorAlarm,
  },
  filters: {
    getCollapse(list, name) {
      // console.log('avtiveNames', list, 'name', name)
      return list.includes(name)
    },
  },
  props: {
    searchCond: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      activeNames: [],
      title: this.$t('trendMonitor.title'),
      treeQuota: [],
      timeRange: [],
      treeQuotaName: [],
      dcInfo: [],
      trendListData: [],
      xAxis: [],
      seriesObj: {},
    }
  },
  computed: {},
  watch: {
    searchCond: {
      handler(val) {
        // console.log('收到父组件发来的消息', val) //这是父组件发送的消息！
        if (val) {
          const {
            treeQuota = [],
            timeRange,
            treeQuotaName = [],
            dcInfo = [],
            timeStamp = Date.now(),
          } = val
          this.treeQuota = treeQuota
          this.treeQuotaName = treeQuotaName
          this.timeRange = timeRange
          this.dcInfo = dcInfo
          // 根据指标数组和dcInfo信息构建collapse
          const trendListData = []
          const p2pMonitor = [
            'monitor.nat',
            // 'monitor.udt', // UDT要展示RDC
            'monitor.relay',
          ] // 对应NatServer RedirectServer RelayServer
          const hostMonitor = [
            'monitor.host',
            'monitor.host.disk',
            'monitor.host.process',
          ] // 主机相关的采集项
          const microserMonitor = ['monitor.microservice'] // 微服务相关的采集项
          // const domainCertMonitor = ['monitor.domain', 'monitor.cert'] // 域名和证书的采集项
          // const kafkaMongoMonitor = [
          //   'monitor.kafka.node',
          //   'monitor.kafka.cluster',
          //   'monitor.mongo',
          // ] // kafka和mongo的采集项
          // 获取dcResCodeObj
          const dcResCodeObj = selfLocaLStorage.getItem('dcResCodeObj') || {}
          // 遍历指标
          treeQuota.forEach((item, index) => {
            const [monitorType, ctSubtype, metric] = item.split('-')
            let quotaType = 'metric' // 类型为指标
            if (monitorType === 'alarm') {
              // 说明是实时告警数据
              quotaType = 'alarm'
            }
            // console.log('monitorType', monitorType)
            // 先通过metricsPageObj获取到对应的权限页面
            const metricsPage = metricsPageObj[monitorType]
            let dcList = []
            if (metricsPage) {
              // 通过权限页面拿到对应的dcList
              dcList = Object.keys(dcResCodeObj[metricsPage])
            }
            // 遍历DC
            dcInfo.forEach((item2) => {
              // P2P/短信/推送不展示RDC,对应的collapse不需要展示，直接过滤掉--这些判断通过dcResCodeObj来判断
              // 如果当前dcList中不包含dc值，则不展示
              if (monitorType !== 'alarm' && !dcList.includes(item2.dcId)) {
                return
              }
              // 如果当前dcList中包含dc值，但是指标类型不存在，则不展示
              const monitor = metricsTypeObj[monitorType]
              // 非告警数据，也不在从metricsTypeObj中获取到对应的DC的指标类数组中，对应的collapse不需要展示
              if (
                monitorType !== 'alarm' &&
                metricsPage &&
                dcResCodeObj[metricsPage] &&
                dcResCodeObj[metricsPage][item2.dcId] &&
                !dcResCodeObj[metricsPage][item2.dcId].includes(monitor)
              ) {
                // console.log(
                //   'dcResCodeObj[metricsPage][item2.dcId]',
                //   dcResCodeObj[metricsPage][item2.dcId]
                // )
                return
              }
              // P2P、主机、微服务才展示不同集群，其余指标就展示一个图
              let areas = []
              if (
                p2pMonitor.includes(monitorType) ||
                hostMonitor.includes(monitorType) ||
                microserMonitor.includes(monitorType)
              ) {
                // P2P、主机、微服务的采集项，才加区域
                const areasTmp = item2.areas
                  ? item2.areas.map((item) => ({
                      value: item.code,
                      label: item.name,
                      type: item.type,
                    }))
                  : []
                if (p2pMonitor.includes(monitorType)) {
                  // P2P的集群只展示type为2的   java集群：1 ，p2p集群：2
                  areas = areasTmp.filter((areaItem) => areaItem.type === '2')
                } else {
                  areas = areasTmp
                }
                if (areas.length) {
                  const temp = {
                    ...item2,
                    areas,
                    timeRange,
                    quota: item,
                    quotaType,
                    monitorType,
                    ctSubtype,
                    ctType: metric,
                    metrics: [metric],
                    // quotaTitle: `${label}:${treeQuotaName[index]}`,
                    quotaTitle: `${treeQuotaName[index]}`,
                    colonyNum: areas.length || 1,
                    timeStamp,
                  }
                  trendListData.push(temp)
                }
              } else {
                // 其余的正常走,不加区域
                areas.push(undefined) // 手动添加一个元素
                const temp = {
                  ...item2,
                  areas,
                  timeRange,
                  quota: item,
                  quotaType,
                  monitorType,
                  ctSubtype,
                  ctType: metric,
                  metrics: [metric],
                  // quotaTitle: `${label}:${treeQuotaName[index]}`,
                  quotaTitle: `${treeQuotaName[index]}`,
                  colonyNum: areas.length || 1,
                  timeStamp,
                }
                trendListData.push(temp)
              }
            })
          })
          this.trendListData = trendListData
          if (this.activeNames.length > 0) {
            // 先过滤当前打开的页签
            const activeNameSet = new Set(this.activeNames) // 原来展开的页签
            const newActvieNames = []
            trendListData.forEach((item) => {
              const collapseName = `${item.quota}-${item.dcId}`
              if (activeNameSet.has(collapseName)) {
                // 继续保留原来展开的页签
                newActvieNames.push(collapseName)
              }
            })
            if (newActvieNames.length > 0) {
              // 继续原来的展开
              this.activeNames = newActvieNames
              return
            }
            // console.log('newActvieNames', newActvieNames)
          }
          // 默认展开第一个
          if (trendListData.length > 0) {
            const initName = `${trendListData[0].quota}-${trendListData[0].dcId}`
            this.activeNames = [initName]
          }
        }
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  methods: {},
}
</script>
<style lang="scss" scoped>
.treMon-wrapper {
  width: 100%;
  height: 100%;
  .treMon-title {
    width: 100%;
    height: 30px;
    line-height: 30px;
    font-size: 24px;
    margin-bottom: 20px;
    text-align: center;
  }
  .treMon-content-wrapper {
    width: 100%;
    // height: calc(100% - 50px);
    height: calc(100vh - 198px);
    overflow-x: hidden;
    overflow-y: auto;
    ::v-deep .el-collapse-item__header.is-active {
      color: #429efd;
    }
  }
  .treMon-list-wrapper {
    width: 100%;
    height: 100%;
    min-height: 300px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 10px;
    .treMon-item-wrapper {
      width: calc(50% - 10px);
      min-height: 300px;
      // margin: 10px 0px;
      // border: 1px solid #c6c6c6;
    }
    .treMon-line-wrapper {
      width: calc(100%);
      // height: 300px;
      // margin: 10px 0px;
      // border: 1px solid #c6c6c6;
    }
  }
}
::v-deep .el-collapse-item__header {
  .collpase-title-wrapper {
    .collpase-title {
      font-size: 16px;
      padding-left: 20px;
    }
    .collpase-sub-title {
      display: inline-block;
      font-size: 16px;
      color: #c6c6c6;
      margin-left: 20px;
    }
  }
}
</style>
