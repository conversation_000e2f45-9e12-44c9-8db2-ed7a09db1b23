<template>
  <div>
    <tvt-dialog
      :title="title"
      :show.sync="showFlag"
      width="1300px"
      :cancel-text="$t('cancel')"
      :submit-text="$t('confirm')"
      :close-on-click-modal="false"
      @close="closeDialog"
      @Cancel="closeDialog"
      @Submit="btnSave"
    >
      <div class="execute-deployment">
        <div class="search-wrapper">
          <!-- 部署方式下拉框 - 仅在部署业务微服务和部署C++服务时显示 -->
          <tvt-select
            v-if="[1, 3].includes(deployType)"
            v-model="filterCond.deploymentMode"
            :placeholder="$t('deployPlanTab.selectDeploymentMode')"
            style="margin-right: 20px"
            @change="handleDeploymentModeChange"
          >
            <el-option
              :label="$t('deployPlanTab.packageTableDeployment')"
              value="packageTable"
            />
            <el-option
              :label="$t('deployPlanTab.patchDeployment')"
              value="patch"
            />
          </tvt-select>

          <!-- 配套表下拉 - 仅在选择"配套表部署"时显示 -->
          <tvt-select
            v-if="
              [1, 3].includes(deployType) &&
              filterCond.deploymentMode === 'packageTable'
            "
            v-model="filterCond.packageTag"
            :placeholder="$t('versionTab.packageTagInput')"
            clearable
            style="margin-right: 20px"
            @change="getOrderList()"
          >
            <el-option
              v-for="(item, index) in packageTagList"
              :key="index"
              :label="item"
              :value="item"
            />
          </tvt-select>

          <!-- 补丁包下拉树形选择 - 仅在选择"补丁部署"时显示 -->
          <tvt-select-tree
            v-if="filterCond.deploymentMode === 'patch'"
            id="osspath"
            ref="selectTree"
            v-model="filterCond.packages"
            clearable
            multiple
            collapse-tags
            :options="ossPathList"
            :default-props="defaultProps"
            :default-expand-all="false"
            :lazy="true"
            class="selectTree"
            style="width: 250px; margin-right: 20px"
            @change="handlePatchPackageChange"
          >
          </tvt-select-tree>

          <!-- 备注输入框 - 仅在选择"补丁部署"时显示 -->
          <tvt-input
            v-if="filterCond.deploymentMode === 'patch'"
            v-model="filterCond.deployRemark"
            :placeholder="$t('deployPlanTab.remarkPlaceholder')"
            clearable
            maxlength="200"
            style="width: 250px; margin-right: 20px"
          />

          <!-- 微服务名称输入框 - 仅在选择"配套表部署"时显示 -->
          <search-input
            v-if="[1, 3].includes(deployType)"
            v-model="filterCond.projectName"
            :placeholder="$t('pleaseEnter', { type: filterName })"
            clearable
            maxlength="32"
            style="width: 250px; margin-right: 20px"
            @change="getOrderList()"
          />

          <!-- 部署开源件时的搜索框 - 保持原有逻辑 -->
          <search-input
            v-if="deployType === 2"
            v-model="filterCond.projectName"
            :placeholder="$t('pleaseEnter', { type: filterName })"
            clearable
            maxlength="32"
            style="width: 250px; margin-right: 20px"
            @change="getOrderList()"
          />
        </div>
        <!-- 补丁包选择区域，仅在部署业务微服务和部署C++服务时显示 -->
        <div class="table-wrapper1">
          <tvt-table
            ref="tableSelection"
            :key="`table-${deployType}-${filterCond.deploymentMode}`"
            v-myLoading="loading"
            :data="orderList"
            :columns="columns"
            :border-bottom="true"
            :border="true"
            max-height="500"
            :pagination="false"
            @selection-change="handleSelectionChange"
          >
            <template #bodyCell="{ row, column }">
              <template v-if="column.prop === 'version'">
                <span>{{
                  row.version
                    ? row.version
                    : row.versions
                    ? row.versions[0]
                    : ''
                }}</span>
              </template>
              <template v-if="column.prop === 'startXmx'">
                <span>{{
                  row.projectName.indexOf('web') != -1 ||
                  row.projectName.indexOf('Web') != -1
                    ? '- -'
                    : row.startXmx
                }}</span>
              </template>
            </template>
          </tvt-table>
        </div>
      </div>
    </tvt-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  getPackageTagList,
  deployTaskServiceList,
  getOssPackage,
} from '@/api/installDeploy.js'
import { addObjKey, debounce } from '@/utils/common'
import { RequestManager } from '@/utils/requestManager'
export default {
  name: 'ExecuteDeployment',
  data() {
    return {
      deployType: '', // "1：部署微服务 2：部署开源件 3：部署C++服务"
      title: '',
      filterName: '',
      showFlag: false,
      packageTagList: [],
      orderList: [],
      total: 0,
      filterCond: {
        deploymentMode: 'packageTable', // 部署方式：packageTable-配套表部署，patch-补丁部署，默认为配套表部署
        packageTag: '',
        projectName: '',
        packages: [],
        deployRemark: '', // 备注
      },
      loading: false,
      multipleSelection: [],
      // 补丁包相关数据
      ossPathList: [],
      ossPathListCache: {}, // 缓存不同类型的补丁包数据
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'path',
      },
      // 请求管理器 - 管理所有可取消的请求
      requestManager: new RequestManager(),
      patchPackageNodes: [], // 补丁包节点数据
    }
  },
  computed: {
    ...mapState('params', ['environmentCode', 'dcName']),
    // 动态计算表格列配置，避免渲染跳动
    columns() {
      // 基础列配置
      const baseColumns = [
        { type: 'selection', width: 60 },
        { label: this.$t('index'), type: 'index', width: 80 },
        {
          label: this.$t('deployPlanTab.version'),
          prop: 'version',
          slotName: 'bodyCell',
          'min-width': 160,
        },
        {
          label: this.$t('deployPlanTab.hostIp'),
          prop: 'hostIp',
          width: 120,
        },
        {
          label: this.$t('deployPlanTab.projectPort'),
          prop: 'projectPort',
          width: 100,
        },
        {
          label: this.$t('deployPlanTab.startMaxMemory'),
          prop: 'startXmx',
          slotName: 'bodyCell',
          width: 150,
        },
        {
          label: this.$t('deployPlanTab.instanceId'),
          prop: 'instanceId',
          width: 100,
        },
      ]

      // 根据部署类型和部署方式动态添加列
      if ([1, 3].includes(this.deployType)) {
        // 部署业务微服务和C++服务
        if (this.filterCond.deploymentMode === 'packageTable') {
          // 配套表部署：显示配套表名称和微服务名称
          baseColumns.splice(
            2,
            0,
            {
              label: this.$t('deployPlanTab.tableName'),
              prop: 'packageTag',
              'min-width': 160,
            },
            {
              label: this.$t('deployPlanTab.projectName'),
              prop: 'projectName',
              'min-width': 160,
            }
          )
          // 在最后面添加备注列
          baseColumns.push({
            label: this.$t('deployPlanTab.remark'),
            prop: 'deployRemark',
            width: 100,
          })
        } else {
          // 补丁部署：只显示微服务名称，不显示配套表名称
          baseColumns.splice(2, 0, {
            label: this.$t('deployPlanTab.projectName'),
            prop: 'projectName',
            'min-width': 160,
          })
        }
      } else {
        // 部署开源软件
        baseColumns.splice(2, 0, {
          label: this.$t('deployPlanTab.sourceName'),
          prop: 'projectName',
          'min-width': 160,
        })
      }

      return baseColumns
    },
  },
  beforeDestroy() {
    // 组件销毁前取消所有请求
    this.requestManager.cancelAllRequests()
    // 清理缓存，避免内存泄漏
    this.ossPathListCache = {}
    this.patchPackageNodes = []
  },
  methods: {
    open(deployType) {
      // 取消所有未完成的请求
      this.requestManager.cancelAllRequests()

      this.deployType = deployType
      this.showFlag = true
      this.filterCond = {
        deploymentMode: 'packageTable', // 默认为配套表部署
        packageTag: '',
        projectName: '',
        packages: [],
        deployRemark: '',
      }
      this.packageTagList = []
      this.multipleSelection = []
      this.orderList = []
      this.ossPathList = []
      this.patchPackageNodes = []

      // 列配置现在通过计算属性动态生成，无需手动初始化
      switch (this.deployType) {
        case 1:
          this.title = this.$t('deployPlanTab.executeDeployment')
          break
        case 2:
          this.title = this.$t('deployPlanTab.executeOpenSource')
          break
        case 3:
          this.title = this.$t('deployPlanTab.executeCPlusService')
          break
        default:
          this.title = this.$t('deployPlanTab.executeDeployment')
          break
      }

      // 设置过滤器名称
      if ([1, 3].includes(this.deployType)) {
        this.filterName = this.$t('deployPlanTab.projectName')
        // 加载补丁包数据
        this.getOssPackageData()
      } else {
        this.filterName = this.$t('deployPlanTab.sourceName')
      }

      // 表格列现在通过计算属性自动更新
      const paramsObj = {
        1: ['java', 'p2pweb'], // 业务微服务对应java和p2pweb
        2: [], // 部署开源软件，对应所有
        3: ['cpp'], // 部署c++服务
      }
      this.packageTagListOption(paramsObj[this.deployType])
      this.getOrderList()
    },
    async packageTagListOption(data = []) {
      try {
        const result = await this.requestManager.executeRequest(
          'packageTagList',
          (signal) => getPackageTagList(data, signal)
        )

        if (result && result.basic && result.basic.code == 200) {
          this.packageTagList = result.data
        } else {
          this.packageTagList = []
        }
      } catch (error) {
        this.packageTagList = []
        console.error(error)
      }
    },
    // 获取补丁包数据（优化：添加缓存机制）
    async getOssPackageData() {
      const data = this.deployType === 1 ? 'java' : 'cpp'

      // 检查缓存
      if (this.ossPathListCache[data]) {
        this.ossPathList = this.ossPathListCache[data]
        return
      }

      try {
        const result = await this.requestManager.executeRequest(
          'ossPackageData',
          (signal) => getOssPackage(data, signal)
        )

        if (result && result.basic && result.basic.code == 200) {
          this.ossPathList = result.data
          // 缓存数据
          this.ossPathListCache[data] = result.data
        } else {
          this.ossPathList = []
        }
      } catch (error) {
        this.ossPathList = []
        console.error(error)
      }
    },
    // 处理部署方式变化
    handleDeploymentModeChange() {
      // 清空相关字段
      this.filterCond = {
        ...this.filterCond,
        packageTag: '',
        projectName: '',
        packages: [],
        deployRemark: '',
      }
      this.patchPackageNodes = []

      // 重新获取数据
      this.getOrderList()
    },

    // 处理补丁包选择变化
    handlePatchPackageChange: debounce(function () {
      // 优化：减少 DOM 查询，缓存节点数据
      if (
        this.$refs.selectTree &&
        this.$refs.selectTree.$refs['osspath-tree']
      ) {
        const checkedNodes =
          this.$refs.selectTree.$refs['osspath-tree'].getCheckedNodes(true)
        // 只有当选择的节点真正发生变化时才更新
        if (
          JSON.stringify(checkedNodes) !==
          JSON.stringify(this.patchPackageNodes)
        ) {
          // 执行校验规则，参照 DeployPatch.vue 中的校验逻辑
          const validationResult = this.validatePatchPackages(checkedNodes)
          if (!validationResult.isValid) {
            // 校验失败，显示错误信息并清空选择
            this.$message.error(validationResult.message)
            return
          }

          this.patchPackageNodes = checkedNodes
          // 调用查询
          this.getOrderList()
        }
      }
    }, 500),

    // 校验补丁包选择，参照 DeployPatch.vue 中的校验规则
    validatePatchPackages(selectedNodes) {
      if (!selectedNodes || selectedNodes.length === 0) {
        return { isValid: true }
      }

      // 1. 检查是否只选择了openSourceDependency的包
      const allOpenSourceDep = selectedNodes.every(
        (item) =>
          item.name && item.name.toLowerCase().includes('opensourcedependency')
      )
      if (allOpenSourceDep) {
        return {
          isValid: false,
          message: this.$t('deployPlanTab.cannotOnlySelectOpenSource'),
        }
      }

      // 2. 选择java服务时，检查是否包含OpenSourceDependency
      const packageNames = selectedNodes.map((item) => item.name).join()
      if (
        packageNames.includes('Service') &&
        !packageNames.includes('OpenSourceDependency')
      ) {
        return {
          isValid: false,
          message: this.$t('versionTab.checkPackageName'),
        }
      }

      // 3. 检查是否存在重复的包名（同一路径下的同一项目）
      const nameSet = new Set(
        selectedNodes.map(function (item) {
          return item.parentPath
            .concat(';')
            .concat(item.businessParams.projectName)
        })
      )
      if (nameSet.size < selectedNodes.length) {
        return {
          isValid: false,
          message: this.$t('versionTab.checkNoRepeatName'),
        }
      }

      // 4. 检查是否存在同个服务多个版本
      const projectSet = new Set(
        selectedNodes.map(function (item) {
          return item.businessParams.projectName
        })
      )
      if (projectSet.size < selectedNodes.length) {
        return {
          isValid: false,
          message: this.$t('versionTab.checkNoRepeatProject'),
        }
      }

      return { isValid: true }
    },

    getOrderList: debounce(async function () {
      const data = addObjKey(this.filterCond)
      // 查询过滤参数
      const { dcName } = this
      data.dcName = dcName || undefined
      data.deployType = this.deployType
      // 选择业务微服务/C++服务中补丁部署时，deployType为4
      if (data.deploymentMode === 'patch') {
        data.deployType = 4
      }

      if (this.patchPackageNodes && this.patchPackageNodes.length > 0) {
        data.packages = this.patchPackageNodes.map((item) => {
          return {
            name: item.name,
            path: item.path,
            packageName: item.businessParams && item.businessParams.packageName,
            projectName: item.businessParams && item.businessParams.projectName,
            version: item.businessParams && item.businessParams.version,
          }
        })
      }

      this.loading = true

      try {
        const result = await this.requestManager.executeRequest(
          'orderList',
          (signal) => deployTaskServiceList(data, signal)
        )

        this.loading = false

        if (result && result.basic && result.basic.code == 200) {
          if (result.data) {
            // 最大内存 和版本号 要有默认值
            this.orderList = result.data
            this.total = parseInt(result.data.total)
          } else {
            this.orderList = []
          }
        }
      } catch (error) {
        this.loading = false
        console.error(error)
      }
    }, 500),
    handleSelectionChange(rows) {
      //表格选择
      this.multipleSelection = rows
    },
    btnSave() {
      if (this.multipleSelection.length == 0) {
        this.$message.error(this.$t('versionTab.needChooseList'))
        return false
      }

      // 构建返回数据，包含补丁包信息
      const saveData = {
        deploymentMode: this.filterCond.deploymentMode, // 部署方式
        packageTag: this.filterCond.packageTag,
        multipleSelection: this.multipleSelection,
        deployType: this.deployType,
        packages:
          this.patchPackageNodes && this.patchPackageNodes.length > 0
            ? this.patchPackageNodes.map((item) => {
                return {
                  name: item.name,
                  path: item.path,
                  packageName:
                    item.businessParams && item.businessParams.packageName,
                  projectName:
                    item.businessParams && item.businessParams.projectName,
                  version: item.businessParams && item.businessParams.version,
                }
              })
            : [], // 处理补丁包数据，参照 ArchivePkg.vue 的处理方式
        deployRemark: this.filterCond.deployRemark, // 备注信息
      }

      this.$emit('btnSave', saveData)
      this.showFlag = false
    },
    closeDialog() {
      // 取消所有未完成的请求
      this.requestManager.cancelAllRequests()
      this.showFlag = false
    },
  },
}
</script>
<style lang="scss" scoped>
.execute-deployment {
  flex: 1;
  padding: 20px;
  .search-wrapper {
    width: 100%;
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}
::v-deep .el-dialog__wrapper {
  align-items: start;
  .el-dialog {
    margin-top: 15vh !important;
  }
}
::v-deep .el-select-dropdown__wrap {
  max-height: 500px;
}
</style>
<style>
.execute-deployment .search-wrapper .el-input__inner {
  height: 32px;
  line-height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 16px;
}

.execute-deployment .search-wrapper .tvt-input .tvt-field-set {
  border: none;
}
.execute-deployment .search-wrapper .tvt-select .tvt-field-set {
  border: none;
}
.execute-deployment .search-wrapper .tu-input .search.blur-search:hover {
  border: 1px solid #409eff !important;
}

.execute-deployment .search-wrapper .select-tree .el-input__inner {
  height: 34px;
  line-height: 34px;
}

.execute-deployment .search-wrapper .select-tree .el-input__icon {
  line-height: 34px;
}

.execute-deployment .search-wrapper .select-tree .el-tag__close.el-icon-close {
  top: 2px;
}

.execute-deployment .search-wrapper .select-tree .el-tag {
  border-radius: 12px;
}
</style>
