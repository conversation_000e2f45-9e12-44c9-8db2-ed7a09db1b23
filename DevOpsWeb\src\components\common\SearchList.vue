<template>
  <!-- 过滤条件组件 -->
  <div class="search-wrapper">
    <div class="search-left-wrapper">
      <div v-for="item in condList" :key="item.paramKey" class="search-item">
        <search-input
          v-if="item.type === 'SearchInput'"
          v-model="filterCond[item.paramKey]"
          :placeholder="item.placeholder"
          clearable
          maxlength="100"
          style="width: 250px; margin-right: 20px"
          @change="changeFilterCond(item, value)"
        />
        <tvt-select
          v-if="item.type === 'TvtSelect'"
          v-model="filterCond[item.paramKey]"
          :options="item.options || []"
          :placeholder="item.placeholder"
          clearable
          @change="changeFilterCond(item, value)"
        />
        <el-date-picker
          v-if="item.type === 'DatePicker'"
          v-model="filterCond[item.paramKey]"
          class="search-datepicker"
          :type="item.dateType || 'date'"
          :placeholder="item.placeholder"
        />
      </div>
      <div v-for="(item, index) in buttonList" :key="index" class="btn-item">
        <el-button
          :type="item.type || 'primary'"
          size="small"
          round
          @click="handle(item.btnFuncType, filterCond)"
        >
          {{ item.text }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SearchList',
  props: {
    searchConfig: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      collapse: false,
      condList: [], // 过滤条件配置数组
      buttonList: [], // 过滤条件右侧的按钮配置数组
      filterCond: {},
    }
  },
  watch: {
    searchConfig(newVal) {
      const { condList = [], buttonList = [] } = newVal
      this.condList = condList
      this.buttonList = buttonList
      this.filterCond = this.condList.reduce((pre, next) => {
        pre[next.paramKey] = next.initValue || null
        return pre
      }, {})
    },
  },
  methods: {
    // 更新过滤条件
    changeFilterCond(item, value) {
      const filterCond = { ...this.filterCond }
      filterCond[item.paramKey] = value
      this.filterCond = filterCond
    },
  },
}
</script>

<style lang="scss" scoped>
.search-wrapper {
  width: 100%;
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  .search-left-wrapper {
    flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }
  .search-right-wrapper {
    width: max-content;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  .search-item {
    width: 250px;
    margin-right: 20px;
  }
  .search-datepicker {
    width: 250px;
  }
  .btn-item {
    margin-left: 20px;
  }
}
</style>

<style>
.search-wrapper .search-left-wrapper .search-item .el-input__inner {
  border: 1px solid #dcdfe6;
  border-radius: 16px;
}

.search-wrapper .search-left-wrapper .search-item .tvt-input .tvt-field-set {
  border: none;
}
.search-wrapper .search-left-wrapper .search-item .tvt-select .tvt-field-set {
  border: none;
}
</style>
