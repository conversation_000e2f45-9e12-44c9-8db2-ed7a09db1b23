<template>
  <div class="viewPortMatrix">
    <tvt-dialog
      ref="addEnvRef"
      :title="$t('configTab.viewPortMatrix')"
      :show.sync="showFlag"
      width="800px"
      :foot-show="false"
      @close="closeDialog"
    >
      <tvt-table
        height="600"
        class="viewPortMatrix-table"
        :data="orderList"
        :columns="columns"
        :border="true"
        :border-bottom="true"
      />
    </tvt-dialog>
  </div>
</template>

<script>
export default {
  name: 'AddEnv',
  components: {},
  data() {
    return {
      showFlag: false,
      columns: [
        { label: this.$t('index'), prop: 'id', width: 60 },
        { label: this.$t('configTab.type'), prop: 'type', width: 100 },
        {
          label: this.$t('configTab.projectName'),
          prop: 'projectName',
          width: 200,
        },
        {
          label: this.$t('configTab.portScope'),
          prop: 'portScope',
          width: 150,
        },
        {
          label: this.$t('configTab.startXmxParam'),
          prop: 'startXmxParam',
          width: 200,
        },
      ],
      orderList: [
        {
          id: '1',
          type: '应用服务',
          projectName: 'BusinessApplyService',
          portScope: '7100-7109',
          startXmxParam: '1024M',
        },
        {
          id: '2',
          type: '应用服务',
          projectName: 'CloudPartnerApplyService',
          portScope: '7110-7119',
          startXmxParam: '1024M',
        },
        {
          id: '3',
          type: '应用服务',
          projectName: 'CloudUserApplyService',
          portScope: '7120-7129',
          startXmxParam: '1024M',
        },
        {
          id: '4',
          type: '应用服务',
          projectName: 'CloudVMSApplyService',
          portScope: '7130-7139',
          startXmxParam: '1024M',
        },
        {
          id: '5',
          type: '基础服务',
          projectName: 'EurekaBasicService',
          portScope: '7001-7009 ',
          startXmxParam: '256M',
        },
        {
          id: '6',
          type: '基础服务',
          projectName: 'GatewayBasicService',
          portScope: '7010-7019',
          startXmxParam: '512M(RDC) 1024M(DDC)',
        },
        {
          id: '7',
          type: '基础服务',
          projectName: 'AccountBasicService',
          portScope: '7020-7029',
          startXmxParam: '1024M',
        },
        {
          id: '8',
          type: '基础服务',
          projectName: 'DeviceManageApplyService',
          portScope: '7030-7039',
          startXmxParam: '1024M',
        },
        {
          id: '9',
          type: '基础服务',
          projectName: 'NoticeBasicService',
          portScope: '7040-7049',
          startXmxParam: '1024M',
        },
        {
          id: '10',
          type: '基础服务',
          projectName: 'OrderApplyService',
          portScope: '7050-7059',
          startXmxParam: '1024M',
        },
        {
          id: '11',
          type: '基础服务',
          projectName: 'HttpredirectBasicService',
          portScope: '7060-7069',
          startXmxParam: '1024M',
        },
        {
          id: '12',
          type: '基础服务',
          projectName: 'DCHttpAgentService',
          portScope: '7070-7079',
          startXmxParam: '256M',
        },
        {
          id: '13',
          type: '运维',
          projectName: 'DevOpsService',
          portScope: '7200',
          startXmxParam: '512M',
        },
        {
          id: '14',
          type: '统计',
          projectName: 'StatisticsApplyService',
          portScope: '7210-7219',
          startXmxParam: '512M(RDC) 1024M(DDC)',
        },
        {
          id: '15',
          type: '后台管理',
          projectName: 'BackgroundManageApplyService',
          portScope: '7220-7229',
          startXmxParam: '1024M',
        },
      ],
    }
  },
  methods: {
    open() {
      this.showFlag = true
    },
    closeDialog() {
      this.showFlag = false
    },
  },
}
</script>
