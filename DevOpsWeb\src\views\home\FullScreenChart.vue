<template>
  <div class="fullScreen-wrapper">
    <div class="fullScreen-search-wrapper">
      <tu-search-time
        v-if="showSearchTime"
        ref="tuSearchTimeRef"
        v-model="filterCond.timeRange"
        class="search-time"
        :default="defaultTimeRange"
        @change="handleTimeChange"
      />
      <!-- <el-button type="primary" size="small" round @click="handleSearch">{{$t('search')}}</el-button> -->
      <el-button
        v-if="showResetBtn"
        type="primary"
        size="small"
        round
        @click="handleReset"
      >
        {{ $t('reset') }}
      </el-button>
      <div class="echart-select-tree">
        <popover-checked
          v-if="topData && topData.length > 0"
          :placement="'top-end'"
          :options="topData"
          :init-check="checkedIntance"
          @handleCheck="handleCheck"
        />
      </div>
    </div>
    <div class="fullScreen-content-wrapper">
      <line-chart
        v-if="echartType === 'lineChart'"
        ref="lineChart"
        :echart-key="`${collapseKey}`"
        :title="title"
        :series-obj="{ xAxis, seriesData: seriesFilterData || [] }"
        :show-full-screen="false"
        @timeZoom="handleTimeZoom"
        @fullScreen="handleFullScreen"
      />
      <bar-chart
        v-else
        ref="lineChart"
        :echart-key="`${collapseKey}`"
        :title="title"
        :series-obj="{ xAxis, seriesData: seriesData || [], invertXY: true }"
        :show-full-screen="false"
        @timeZoom="handleTimeZoom"
        @fullScreen="handleFullScreen"
      />
    </div>
  </div>
</template>
<script>
import TuSearchTime from '@/components/common/TuSearchTime.vue'
import PopoverChecked from '@/components/common/PopoverChecked.vue'
import LineChart from '@/components/common/LineChart.vue'
import BarChart from '@/components/common/BarChart.vue'
import { stampToStrLong } from '@/utils/common'
import { getMonitorDataListApi, getMonitorBarDataApi } from '@/api/home.js'
import { barMetricsNameObj } from '@/views/home/<USER>'

export default {
  name: 'FullScreenChart',
  components: {
    TuSearchTime,
    PopoverChecked,
    LineChart,
    BarChart,
  },
  props: {
    searchCond: {
      type: Object,
      default: () => ({
        ctType: 'monitor.host',
        timeRange: [],
        dcId: null,
        area: null,
      }),
    },
    echartType: {
      type: String,
      default: 'lineChart',
    },
    initCheck: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      collapseKey: Date.now(),
      filterCond: {
        monitorType: 'monitor.host',
        timeRange: [],
        area: null,
        dcId: null,
      },
      defaultTimeRange: [],
      xAxis: [],
      seriesData: null,
      seriesFilterData: null, // 对最终生成的结果进行过滤，针对微服务这种返回多实例的，需要默认勾选TOP5展示
      checkedIntance: [], // 默认勾选展示的实例,在图上展示 默认为TOP5
      topData: [], // 排序的实例
      title: null, // 图的名称
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: this.$t('today'),
            onClick(picker) {
              picker.$emit('pick', new Date())
            },
          },
          {
            text: this.$t('yesterday'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24)
              picker.$emit('pick', date)
            },
          },
          {
            text: this.$t('weekAgo'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', date)
            },
          },
        ],
      },
    }
  },
  computed: {
    // 是否展示时间搜索
    showSearchTime() {
      // 磁盘不展示时间
      // return !['monitor.host.disk'].includes(this.filterCond.monitorType)
      return true
    },
    // 是否展示重置按钮
    showResetBtn() {
      // 磁盘不展示重置
      // return !['monitor.host.disk'].includes(this.filterCond.monitorType)
      return true
    },
  },
  watch: {
    initCheck: {
      handler(newVal, oldVal) {
        if (newVal) {
          if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
            this.checkedIntance = newVal.slice()
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {},
  mounted() {
    // 获取区域选择条件
    const { timeRange = [] } = this.searchCond
    // console.log('this.searchCond', this.searchCond)
    // this.getAlarmHistory(data)
    this.filterCond = { ...this.searchCond }
    const { area } = this.searchCond
    if (area) {
      this.title = area.label
    } else {
      this.title = null
    }
    this.defaultTimeRange = timeRange
    this.getMonitorDataList(this.filterCond)
  },
  methods: {
    // 时间控件变化
    handleTimeChange() {
      this.getMonitorDataList()
    },
    // 点击搜索按钮
    handleSearch() {
      this.getMonitorDataList()
    },
    // 点击重置按钮
    handleReset() {
      const formData = {
        ...this.filterCond,
        timeRange: [],
      }
      this.filterCond = formData
      this.getMonitorDataList(formData)
    },
    getMonitorDataList(formData) {
      this.loading = true
      if (this.$refs.lineChart && this.$refs.lineChart.chartInstance) {
        this.$refs.lineChart.chartInstance.showLoading({
          text: 'loading',
          color: 'rgba(20, 149, 247, 0.7)', //设置加载颜色
          textColor: '#000',
          maskColor: 'rgba(255, 255, 255, 0.2)',
          zlevel: 0,
        })
      }
      if (!formData) {
        formData = this.filterCond
      }
      const { monitorType, ctSubtype, metrics, timeRange, dcId, area } =
        formData
      const params = {
        monitorType,
        metrics,
        dcId,
      }
      // console.log('查询条件', timeRange)
      if (timeRange && timeRange.length) {
        params.startTime = timeRange[0]
        params.endTime = timeRange[1]
        params.startTimeTmp = window
          .moment(timeRange[0])
          .format('YYYY-MM-DD HH:mm:ss')
        params.endTimeTmp = window
          .moment(timeRange[1])
          .format('YYYY-MM-DD HH:mm:ss')
      }
      if (area) {
        params.extra = {
          clusterCode: area.value,
        }
      }
      const requestFn =
        this.echartType === 'lineChart'
          ? getMonitorDataListApi
          : getMonitorBarDataApi
      // if (['monitor.host.disk'].includes(monitorType)) {
      //   // 磁盘
      //   // 这些柱状图不需要时间参数
      //   delete params.startTime
      //   delete params.startTimeTmp
      //   delete params.endTime
      //   delete params.endTimeTmp
      // }
      // P2P2.0中选择RDC、UDT时，不传dcId
      if (
        this.echartType === 'lineChart' &&
        monitorType === 'monitor.udt' &&
        dcId === '1'
      ) {
        delete params.dcId // 不传dcId
      }
      // wsConnect 中app连接数，有点特殊，需要前端在入参的extra字段里加个内容：group：appId
      if (
        this.echartType === 'lineChart' &&
        monitorType === 'monitor.wsConnect' &&
        params.metrics[0] === 'connectNum'
      ) {
        params.extra = {
          ...(params.extra || {}),
          group: 'appId',
        }
      }
      return requestFn(params)
        .then((res) => {
          this.loading = false
          if (this.$refs.lineChart && this.$refs.lineChart.chartInstance) {
            this.$refs.lineChart.chartInstance.hideLoading()
          }
          if (res.basic && res.basic.code == 200) {
            if (res.data) {
              if (this.echartType === 'lineChart') {
                // 折线图逻辑
                // 解析返回结果，生成按照采集项分类的数据
                const {
                  fields,
                  datas,
                  topData = [],
                  topDisplayNum = 5,
                } = res.data
                // 遍历fields找到每个指标的下标
                const fieldIndexObj = {}
                fields.forEach((item, index) => {
                  fieldIndexObj[item] = index
                })
                let seriesName = Object.keys(datas).sort() // 用作series分类依据
                if (topData && topData.length > 0) {
                  // 如果有排序，则按照排序顺序来
                  seriesName = topData
                }
                // 先遍历所有数据取出ct （时间） 用作x轴 有些是用其他用作x轴
                const xAxisField = 'st'
                // 域名和证书是柱状图 -- 现在改成折线图了
                // if (ctSubtype === 'Https域名') {
                //   xAxisField = 'domain'
                // } else if (ctSubtype === '证书') {
                //   xAxisField = 'cert'
                // }
                const stIndex = fieldIndexObj[xAxisField]
                const stSet = seriesName.reduce((pre, next) => {
                  const nextData = datas[next] || []
                  nextData.forEach((item2) => {
                    const st =
                      xAxisField === 'st'
                        ? stampToStrLong(parseInt(item2[stIndex]) * 1000)
                        : item2[stIndex] // 转换成YYYY-MM-DD HH:mm:ss的形式
                    item2[stIndex] = st
                    pre.add(st)
                  })
                  return pre
                }, new Set())
                // console.log('ctSet', ctSet)
                const stSortArr = Array.from(stSet).sort() // 升序排列
                // const ctArr = ctSortArr.map((item) => item.split(' ')[1]) // 取出HH:mm:ss
                // console.log('ctSortArr', ctSortArr)
                this.xAxis = stSortArr
                // console.log('ctArr', ctArr)
                // 遍历ctSortArr确定每个时刻的index
                const stIndexObj = {}
                stSortArr.forEach((item, index) => {
                  stIndexObj[item] = index
                })
                const seriesData = [] // 整体series的对象，每个指标项对应一个key  [{ name: '主机1'， data: [...] }, { name: '主机2'， data: [...] }]
                // 遍历seriesName,生成数据
                seriesName.forEach((name) => {
                  // 找到
                  if (metrics.length > 1) {
                    // 说明是类似'loadOne', 'loadFive', 'loadFifteen'这种三个放在一块的，需要在series层面额外加一级 [{ name: '主机1-loadOne'， data: [...] }, { name: '主机1-loadFive'， data: [...] }, { name: '主机1-loadFifteen'， data: [...] }]
                    metrics.forEach((metric) => {
                      const temp = {
                        name: name + '-' + metric,
                        data: new Array(stSortArr.length).fill(undefined), // 按照所有时刻生成对应的数据数组
                      }
                      seriesData.push(temp)
                    })
                  } else {
                    const temp = {
                      name,
                      data: new Array(stSortArr.length).fill(undefined), // 按照所有时刻生成对应的数据数组
                    }
                    seriesData.push(temp)
                  }
                })
                // 遍历seriesName，找到每个原始数据，再遍历之后将数据生成图所需要的数据
                seriesName.forEach((name, index) => {
                  const seriseData = datas[name] || []
                  seriseData.forEach((item) => {
                    if (metrics.length > 1) {
                      // 说明是类似'loadOne', 'loadFive', 'loadFifteen'这种三个放在一块的
                      const len = metrics.length
                      metrics.forEach((metric, index2) => {
                        const stSeriesIndex = stIndexObj[item[stIndex]]
                        const value = metric
                        const quotaIndex = fieldIndexObj[value] // 具体指标数值所在的下标
                        seriesData[index * len + index2].data[stSeriesIndex] =
                          item[quotaIndex]
                      })
                    } else {
                      const stSeriesIndex = stIndexObj[item[stIndex]]
                      const value = metrics[0]
                      const quotaIndex = fieldIndexObj[value] // 具体指标数值所在的下标
                      // console.log('value', value, 'fieldIndexObj', fieldIndexObj, 'quotaIndex', quotaIndex, 'item[quotaIndex]', item[quotaIndex])
                      // value对应指标（CPU利用率） index对应主机的下标，ctSeriesIndex对应ct在xAxis中的下标
                      seriesData[index].data[stSeriesIndex] = item[quotaIndex]
                    }
                  })
                })
                // 如果metrics长度大于0，需要把series上的名称rdc-dev40-01-loadOne换成国际化的名称rdc-dev40-01-1分钟平均负载
                if (metrics.length > 1) {
                  const len = metrics.length
                  seriesName.forEach((name, index) => {
                    metrics.forEach((metric, index2) => {
                      seriesData[index * len + index2].name =
                        name + '-' + this.$t(metric)
                    })
                  })
                }
                // console.log('seriesData', seriesData)
                const newSeriesData = [...seriesData]
                this.seriesData = newSeriesData
                // 判断checkedIntance是否存在,存在则直接使用
                if (this.checkedIntance && this.checkedIntance.length) {
                  this.getSeriesFilterData(newSeriesData, this.checkedIntance) // 过滤展示的实例
                } else {
                  // 处理TOP5展示逻辑
                  this.topData = topData.map((item) => ({
                    label: item,
                    value: item,
                  }))
                  if (topData && topData.length > 0) {
                    // 从topData中取前5的数据
                    const newCheckedValue = topData.slice(0, topDisplayNum)
                    this.checkedIntance = newCheckedValue
                    this.getSeriesFilterData(newSeriesData, newCheckedValue) // 过滤展示的实例
                  } else {
                    this.seriesFilterData = newSeriesData
                  }
                }
              } else {
                // 柱状图逻辑
                // 解析返回结果，生成按照采集项分类的数据
                const { datas, topData = [] } = res.data
                let seriesName = Object.keys(datas).sort() // 用作series分类依据
                if (topData && topData.length > 0) {
                  // 如果有排序，则按照排序顺序来
                  seriesName = topData
                }
                // datas中key作为x轴，value作为纵轴
                const xAxis = []
                // 获取当前指标的label
                const key = `${monitorType}-${ctSubtype}-${metrics.join(',')}`
                const name = barMetricsNameObj[key] || ''
                const seriesData = [{ name, data: [] }]
                seriesName.forEach((key) => {
                  xAxis.push(key)
                  seriesData[0].data.push(datas[key][0][0])
                })
                this.xAxis = xAxis
                this.seriesData = seriesData
                this.checkedIntance = []
                this.seriesFilterData = null
                this.topData = []
              }
            } else {
              this.xAxis = []
              this.seriesData = null
              this.checkedIntance = []
              this.seriesFilterData = null
            }
          }
        })
        .catch((err) => {
          this.loading = false
          if (this.$refs.lineChart && this.$refs.lineChart.chartInstance) {
            this.$refs.lineChart.chartInstance.hideLoading()
          }
          console.log(err)
        })
    },
    // 图上区域选择
    handleTimeZoom(timeZoom) {
      const { startTime, endTime } = timeZoom
      if (startTime && endTime) {
        const {
          monitorType,
          ctSubtype,
          metrics = [],
          area = null,
          dcId,
        } = this.filterCond
        const startTimeTmp = new Date(startTime).getTime()
        const endTimeTmp = new Date(endTime).getTime()
        const formData = {
          monitorType,
          ctSubtype,
          metrics: metrics.slice(),
          timeRange: [startTimeTmp, endTimeTmp],
          dcId,
          area,
        }
        // this.xAxis = []
        // this.seriesData = null
        this.getMonitorDataList(formData)
      } else {
        // 还原
        const {
          monitorType,
          ctSubtype,
          metrics = [],
          dcId,
          area = null,
          timeRange,
        } = this.filterCond
        const formData = {
          monitorType,
          ctSubtype,
          metrics: metrics.slice(),
          timeRange,
          dcId,
          area,
        }
        this.getMonitorDataList(formData)
      }
    },
    // 图上toolbox全屏点击事件
    handleFullScreen() {
      this.$emit('fullScreen')
    },
    handleCheck(val) {
      this.getSeriesFilterData(this.seriesData, val)
    },
    // 根据勾选情况过滤seriesData
    getSeriesFilterData(seriesData = null, val) {
      const data = seriesData || this.seriesData
      // 根据勾选的实例过滤图数据
      const instanceSet = new Set(val)
      this.seriesFilterData = data.filter((item) => instanceSet.has(item.name))
    },
  },
}
</script>
<style lang="scss" scoped>
.fullScreen-wrapper {
  width: 100%;
  height: calc(100vh - 92px);
  display: flex;
  flex-direction: column;
}
.fullScreen-search-wrapper {
  width: 100%;
  height: 34px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  & > div {
    margin-right: 20px;
  }
}
.fullScreen-content-wrapper {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  // padding-top: 20px;
  overflow: hidden;
}
.echart-select-tree {
  position: absolute;
  top: 40px;
  left: 10px;
  z-index: 1;
  width: 20px;
  height: 34px;
  display: flex;
  align-items: center;
}
</style>
