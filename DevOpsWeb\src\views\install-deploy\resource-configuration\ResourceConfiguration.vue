<template>
  <!-- 部署环境资源规划 -->
  <div class="resource-configuration-wrapper">
    <div class="resource-configuration">
      <div class="search-wrapper">
        <div class="search-left-wrapper">
          <search-input
            v-model="filterCond.projectName"
            size="small"
            clearable
            :placeholder="$t('projectNameInput')"
            maxlength="100"
            class="search-item-box"
            @change="handleSearchChange"
          />
          <search-input
            v-model="filterCond.hostName"
            size="small"
            clearable
            :placeholder="$t('alarmHistoryTab.hostNameInput')"
            maxlength="100"
            class="search-item-box"
            @change="handleSearchChange"
          />
          <search-input
            v-model="filterCond.hostIp"
            size="small"
            clearable
            :placeholder="$t('microLogTab.hostIpInput')"
            maxlength="100"
            class="search-item-box"
            @change="handleSearchChange"
          />
          <!-- <el-button type="primary" round size="small" @click="getOrderList(1)">{{$t('search')}}</el-button> -->
          <el-button
            v-if="$isAuthorityExist(['devops_deploy_resourcePlan_mgr'])"
            type="primary"
            class="btn"
            size="small"
            round
            :loading="btnLoading"
            @click="importTemplate"
          >
            {{ $t('hostTab.importTemplate') }}
          </el-button>
          <el-button
            v-if="$isAuthorityExist(['devops_deploy_resourcePlan_mgr'])"
            type="primary"
            class="btn"
            size="small"
            round
            @click="hostInitialization"
          >
            {{ $t('hostTab.hostInitialization') }}
          </el-button>
          <!-- <el-button type="primary"  size="small" round @click="openLink('eureka')" v-if="$isAuthorityExist()">{{ $t('serviceStatusTab.openEureka') }}</el-button>
        <el-button type="primary"  size="small" round @click="openLink('springBootAdmin')" v-if="$isAuthorityExist()">{{ $t('serviceStatusTab.openSpringBootAdmin') }}</el-button> -->
        </div>
        <div class="search-right-wrapper">
          <!-- <el-button type="primary" class="btn" size="small" round @click="addResource" v-if="$isAuthorityExist()">{{$t('configTab.addResource')}}</el-button> -->
          <!-- <el-button type="primary" class="btn-view btn-view1" size="small" round @click="viewPortMatrix">{{$t('configTab.viewPortMatrix')}}</el-button> -->
        </div>
      </div>
      <div class="quota-wrapper">
        <div
          v-for="item in quotaData"
          :key="item.clusterName"
          class="quota-line-wrapper"
        >
          <div class="quota-label">{{ $t('configTab.clusterName') }}:</div>
          <div class="quota-value" style="min-width: 60px">
            {{ item.clusterName || '' }}
          </div>
          <div class="quota-label">
            {{ $t('configTab.cpu') }}({{ $t('configTab.cpuCore') }}):
          </div>
          <div class="quota-value">
            {{ item.cpu || '' }}
          </div>
          <div class="quota-label">{{ $t('configTab.memory') }}(G):</div>
          <div class="quota-value">
            {{ item.memory || '' }}
          </div>
          <div class="quota-label">{{ $t('configTab.disk') }}(G):</div>
          <div class="quota-value">
            {{ item.disk || '' }}
          </div>
        </div>
      </div>
      <div class="table-wrapper">
        <tvt-table
          ref="myTable"
          v-myLoading="loading"
          :data="orderList"
          :columns="columns"
          :border="true"
          :border-bottom="true"
          :pagination="{
            total,
            current: filterCond.current,
            size: filterCond.size,
            'page-sizes': $tablePageSizes,
            background: true,
          }"
          :row-class-name="tableRowClassName"
          style="width: 100%"
          @sort-change="handleSort"
          @onFetchData="getOrderList"
        >
          <template #bodyCell="{ row, column }">
            <template v-if="column.key === 'operation'">
              <!-- <span class="btn-text"  @click="deleteOne(row,$index)">{{ $t('delete') }}</span> -->
              <div class="operation-list">
                <div
                  v-if="
                    row.operateList && row.operateList.indexOf('reload') > -1
                  "
                  class="btn-box"
                  @click="serviceOperateOne(row, 'reload')"
                >
                  {{ $t('serviceStatusTab.reloadService') }}
                </div>
                <div
                  v-if="
                    row.operateList && row.operateList.indexOf('restart') > -1
                  "
                  class="btn-box"
                  @click="serviceOperateOne(row, 'restart')"
                >
                  {{ $t('serviceStatusTab.restartService') }}
                </div>
                <div
                  v-if="row.operateList && row.operateList.indexOf('stop') > -1"
                  class="btn-box"
                  @click="serviceOperateOne(row, 'stop')"
                >
                  {{ $t('serviceStatusTab.stopService') }}
                </div>
                <div
                  v-if="
                    row.operateList && row.operateList.indexOf('uninstall') > -1
                  "
                  class="btn-box"
                  @click="serviceOperateOne(row, 'uninstall')"
                >
                  {{ $t('serviceStatusTab.uninstallService') }}
                </div>
              </div>
            </template>
          </template>
        </tvt-table>
      </div>
    </div>
    <tip-dialog
      ref="tipDialogRef"
      title="configTab.info"
      :tip-msg="$t('configTab.delete')"
      @submit="handleDelete"
    />
    <env-delete-dialog
      ref="envDeleteDialog"
      title="configTab.info"
      :tip-msg="$t('configTab.deleteEnv')"
      @submit="handleEnvDelete"
    />
    <!-- 新增资源弹框 -->
    <config-edit ref="configEdit" @btnSave="resourceAddSave" />
    <!-- 查看端口矩阵 -->
    <view-port-matrix ref="viewPortMatrix" />
    <!-- 主机初始化 -->
    <host-initialization ref="hostInitialization" @btnSave="hostSave" />
  </div>
</template>

<script>
import {
  envDelete,
  resList,
  resAdd,
  resDelete,
  importTemplate,
  serviceStatusOperate,
  getQuotaSummary,
} from '@/api/installDeploy.js'
import { debounce, addObjKey } from '@/utils/common'
import TipDialog from '@/components/common/TipDialog.vue'
import EnvDeleteDialog from '@/components/common/TipDialog.vue'
import ConfigEdit from '@/views/install-deploy/resource-configuration/ConfigEdit'
import ViewPortMatrix from '@/views/install-deploy/resource-configuration/ViewPortMatrix.vue'
import HostInitialization from '@/views/install-deploy/resource-configuration/HostInitialization'
import { mapState } from 'vuex'
import { getLocale } from '@/lang'

const lang = getLocale() || 'zh-CN'

export default {
  name: 'ResourceConfiguration',
  components: {
    TipDialog,
    ConfigEdit,
    EnvDeleteDialog,
    ViewPortMatrix,
    HostInitialization,
  },
  data() {
    return {
      orderList: [],
      resourceList: [],
      loading: false,
      columns: [
        { label: this.$t('index'), type: 'index', width: 60 },
        {
          label: this.$t('configTab.projectName'),
          prop: 'projectName',
          'min-width': 250,
          sortable: 'custom',
        },
        {
          label: this.$t('configTab.clusterName'),
          prop: 'clusterName',
          width: lang === 'zh-CN' ? 200 : 180,
        },
        {
          label: this.$t('configTab.hostName'),
          prop: 'hostName',
          width: lang === 'zh-CN' ? 190 : 170,
          sortable: 'custom',
        },
        {
          label: this.$t('configTab.resourceIp'),
          prop: 'hostIp',
          width: lang === 'zh-CN' ? 190 : 140,
          sortable: 'custom',
        },
        {
          label: this.$t('configTab.projectPort'),
          prop: 'projectPort',
          width: lang === 'zh-CN' ? 130 : 110,
        },
        {
          label: this.$t('configTab.instanceId'),
          prop: 'instanceId',
          width: lang === 'zh-CN' ? 140 : 110,
        },
        // { label: this.$t('configTab.orderId'),prop:'batchOrder',width: 60},
        // { label: this.$t('configTab.startXms'),prop:'startXms',width: 150},
        {
          label: this.$t('configTab.startXmx'),
          prop: 'startXmx',
          width: lang === 'zh-CN' ? 150 : 130,
        },
        {
          label: this.$t('serviceStatusTab.serviceStatus'),
          prop: 'serviceStatus',
          width: 130,
        },
        {
          label: this.$t('configTab.operation'),
          key: 'operation',
          slotName: 'bodyCell',
          fixed: 'right',
          width: lang === 'zh-CN' ? 180 : 220,
          hide: !this.$isAuthorityExist(['devops_deploy_resourcePlan_mgr']),
        },
      ],
      filterCond: {
        current: 1,
        size: 20,
        projectName: '',
        hostName: '',
        hostIp: '',
      },
      total: 0,
      deleteId: null,
      deleteIndex: null,
      deleteLength: null,
      activeItem: 0,
      showEnvAdd: false,
      envDeleteId: '', //删除环境 id
      dcNameOptions: [],
      btnLoading: false,
      quotaData: [], // 集群统计数据
      order: null, // 排序
    }
  },
  computed: {
    ...mapState('params', ['environmentCode', 'dcName']),
  },
  mounted() {
    this.getOrderList(1)
    this.getSummary()
  },
  methods: {
    tableRowClassName({ row }) {
      if (row.serviceStatus === 'DOWN') {
        // 服务状态为DOWN时，显示红色背景
        return 'warning-row'
      }
      return ''
    },
    // 主机初始化
    hostInitialization() {
      this.$refs.hostInitialization.open()
    },
    // 导入部署模板
    async importTemplate() {
      try {
        this.btnLoading = true
        await importTemplate({})
        // console.log('res2', res2)
        this.btnLoading = false
        this.$message.success(this.$t('hostTab.importSuccess'))
        this.getOrderList(1)
      } catch (error) {
        this.btnLoading = false
        console.log(error)
      }
    },
    // 查看端口矩阵
    viewPortMatrix() {
      this.$refs.viewPortMatrix.open()
    },
    changeActiveItem(index) {
      this.activeItem = index
      this.getOrderList(1) //切换选项后要切换列表
    },
    // 1.4分页获取资源规划列表
    getOrderList: debounce(function (pageSize) {
      this.loading = true
      if (pageSize == 1) {
        this.filterCond.current = 1
      } else if (pageSize && pageSize.current) {
        this.filterCond.current = pageSize.current
        this.filterCond.size = pageSize.size
      }
      let data = addObjKey(this.filterCond)
      const { projectName, hostName, hostIp } = data
      data.projectName = projectName ? projectName.trim() : null
      data.hostName = hostName ? hostName.trim() : null
      data.hostIp = hostIp ? hostIp.trim() : null
      // 查询过滤参数
      let { environmentCode, dcName } = this
      data.environmentCode = environmentCode
      data.dcName = dcName ? dcName : undefined
      data.pageNum = this.filterCond.current
      data.pageSize = this.filterCond.size
      data.lang = lang
      // 排序
      data.order = this.order
      delete data['current']
      delete data['size']
      resList(data)
        .then((res) => {
          this.loading = false
          if (res.basic && res.basic.code == 200) {
            if (res.data) {
              this.orderList = res.data.records
              this.total = parseInt(res.data.total)
            } else {
              this.orderList = []
            }
          }
        })
        .catch((err) => {
          this.loading = false
          console.log(err)
        })
    }, 500),
    // 1.5新增资源规划
    resourceAddSave(val) {
      let data = { ...val }
      resAdd(data)
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            this.getOrderList(1)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    addResource() {
      this.$refs.configEdit.open()
    },
    deleteOne(row, index) {
      this.deleteId = row.id
      this.deleteIndex = index
      this.deleteLength = this.orderList.length
      this.$refs.tipDialogRef.open()
    },
    // 弹框出来后点保存 ==   1.6删除资源规划
    handleDelete() {
      let data = { ids: [this.deleteId] }
      resDelete(data)
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            // 刷新 环境列表 删除 判断 index 是0 并且删除页只有一条数据
            if (this.deleteIndex == 0 && this.deleteLength == 1) {
              this.getOrderList(1) //如果当前页只有一项 就把页面归到第一页
            } else {
              this.getOrderList()
            }
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    getEnvListMethodRefresh() {
      this.getOrderList(1)
    },
    // 删除环境弹框
    confirmDeleteEnv(item) {
      this.envDeleteId = item.id
      this.$refs.envDeleteDialog.open()
    },
    handleEnvDelete() {
      this.deleteEnv(this.envDeleteId)
    },
    // 1.3删除环境
    deleteEnv(id) {
      let data = { ids: [id] }
      envDelete(data)
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            // 刷新 环境列表 并且重新查表格数据
            this.getEnvListMethodRefresh()
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    closeResourceDialog() {},
    btnResourceSave() {},
    hostSave() {},
    openLink(type) {
      if (type == 'eureka') {
        //打开eureka
        var eurekaBasicService = this.orderList.find((item) => {
          return item.projectName == 'EurekaBasicService'
        })
        if (eurekaBasicService) {
          window.open(
            `http://${eurekaBasicService.resourceIp}:${eurekaBasicService.projectPort}`
          )
        } else {
          this.$message.error(this.$t('serviceStatusTab.openEurekaTip'))
        }
      }

      if (type == 'springBootAdmin') {
        //打开springBootAdmin
        var backgroundManageApplyService = this.orderList.find((item) => {
          return item.projectName == 'BackgroundManageApplyService'
        })
        if (backgroundManageApplyService) {
          window.open(
            `http://${backgroundManageApplyService.resourceIp}:${backgroundManageApplyService.projectPort}/admin`
          )
        } else {
          this.$message.error(
            this.$t('serviceStatusTab.openSpringBootAdminTip')
          )
        }
      }
    },
    serviceOperateOne(row, opType) {
      this.executeServiceOperate([row], opType)
    },
    executeServiceOperate(rows, opType) {
      if (!rows.length) {
        this.$message.error(this.$t('versionTab.needChooseList'))
        return false
      }
      let data = []
      for (var i = 0; i < rows.length; i++) {
        data.push({
          projectName: rows[i].projectName,
          hostIp: rows[i].hostIp,
          projectPort: rows[i].projectPort,
          instanceId: rows[i].instanceId,
          operate: opType,
        })
      }
      this.$TvtLoading.show({ text: this.$t('LoadingMsg') })
      serviceStatusOperate(data)
        .then((res) => {
          this.$TvtLoading.hide()

          if (res.basic && res.basic.code === 200) {
            // this.$message({
            //     dangerouslyUseHTMLString: true,
            //     message: res.data.join(' <br/> '),
            //     type: 'success'
            // });
            const operateNameObj = {
              restart: this.$t('serviceStatusTab.restartService'),
              stop: this.$t('serviceStatusTab.stopService'),
              uninstall: this.$t('serviceStatusTab.uninstallService'),
              reload: this.$t('serviceStatusTab.reloadService'),
            }
            this.$message.success(
              `${operateNameObj[opType]}${this.$t('success')}`
            )
            this.getOrderList(this.filterCond.current)
          } else {
            this.$message.error(res.basic.msg)
          }
        })
        .catch((err) => {
          this.$TvtLoading.hide()
          console.log(err)
        })
    },
    // 获取所有集群统计信息
    getSummary() {
      getQuotaSummary(getLocale())
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            if (res.data) {
              // console.log('res.data', res.data)
              this.quotaData = res.data
            }
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    handleSort(params) {
      const { prop = null, order = null } = params || {}
      const orderObj = { ascending: 'asc', descending: 'desc' }
      if (prop && order) {
        const orderTemp = `${prop}_${orderObj[order]}`
        this.order = orderTemp
        this.getOrderList(1)
      } else {
        // 说明是去掉排序
        this.order = null
        this.getOrderList(1)
      }
    },
    handleSearchChange() {
      this.order = null
      // 清除表格排序
      if (this.$refs.myTable && this.$refs.myTable.$refs.tvtTableElTableRef) {
        this.$refs.myTable.$refs.tvtTableElTableRef.clearSort()
      }
      this.getOrderList(1)
    },
  },
}
</script>
<style lang="scss" scoped>
.resource-configuration-wrapper {
  margin: 24px;
  width: calc(100% - 48px);
  position: relative;
}
.resource-configuration {
  width: 100%;
  .search-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    .search-left-wrapper {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;
      gap: 10px;
      .search-item-box {
        width: 250px;
        margin-right: 10px;
      }
    }
    .search-right-wrapper {
      width: max-content;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
  }
  .quota-wrapper {
    width: 100%;
    margin-bottom: 20px;
    font-size: 14px;
    background: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 14px;
    margin-right: 20px;
    box-sizing: border-box;
    height: 54px;
    display: flex;
    align-items: center;
    .quota-line-wrapper {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      line-height: 20px;
      .quota-label {
        margin-right: 10px;
      }
      .quota-value {
        margin-right: 20px;
      }
    }
  }
  .table-wrapper {
    width: calc(100%);
    // .btn{
    //   float:right;
    //   margin-bottom: 20px;
    // }
    // .btn-view1{
    //   margin-right: 10px;
    // }
    // .btn-view{
    //   float:right;
    // }
  }
  .operation-list {
    width: 100%;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: minmax(50px, 1fr) minmax(50px, 1fr) minmax(50px, 1fr);
    grid-row-gap: 10px;
  }
  .btn-box {
    color: #429efd !important;
    cursor: pointer;
    text-align: center;
    white-space: nowrap;
  }
}
.active-item {
  color: #429efd;
}
</style>
<style>
.resource-configuration .search-wrapper .el-input__inner {
  height: 32px;
  line-height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 16px;
}

.resource-configuration .search-wrapper .tvt-input .tvt-field-set {
  border: none;
}
.resource-configuration .search-wrapper .tvt-select .tvt-field-set {
  border: none;
}

.resource-configuration .table-wrapper .el-table .el-table__cell {
  padding: 8px 0 !important;
}

.resource-configuration .warning-row {
  background: #f56c6c;
}
</style>
