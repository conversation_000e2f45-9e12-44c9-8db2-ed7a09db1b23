<template>
  <!-- 系统监控-实时监控趋势数据 -->
  <div class="monitor-wrapper">
    <div class="echart-search-wrapper">
      <tvt-select
        v-model="filterCond.treeQuota"
        class="echart-select"
        :style="{ width: '250px' }"
        :options="echartSelectList"
        @change="changeSelect"
        @focus="handleFocus"
      >
      </tvt-select>
    </div>
    <div
      :class="[
        echartSelectList && echartSelectList.length > 0
          ? 'monitor-content-wrapper'
          : 'monitor-full-content-wrapper',
      ]"
    >
      <div v-loading="loading" class="monitor-list-wrapper">
        <div
          v-for="(item, index) of areas"
          :key="`${filterCond.search}-${
            item ? item.value : 'collapse'
          }-${index}`"
          :class="[
            areas && areas.length > 1
              ? 'monitor-item-wrapper'
              : 'monitor-line-wrapper',
          ]"
          :data-chart-index="index"
        >
          <template v-if="renderedCharts[index]">
            <chart-component
              :collapse-key="`${filterCond.dcId}-${
                item ? item.value : 'area'
              }-${filterCond.treeQuota}`"
              :search-cond="{ ...collapseCond, area: item }"
              :data-obj="getChartData(item, index)"
              :collapse-open="true"
            />
          </template>
          <template v-else>
            <div class="chart-placeholder">
              <div class="loading-indicator">{{ $t('LoadingMsg') }}</div>
            </div>
          </template>
          <!-- <chart-component
            :collapse-key="`${filterCond.dcId}-${item ? item.value : 'area'}-${
              filterCond.treeQuota
            }`"
            :search-cond="{ ...collapseCond, area: item }"
            :data-obj="getChartData(item, index)"
            :collapse-open="true"
          /> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import ChartComponent from '@/views/home/<USER>'
import { getAllMonitorDataApi } from '@/api/home.js'
import { dealLineChartData } from '@/utils/common'
import { metricsOptionsObj } from '../config.js'
import { mapState } from 'vuex'
import { ObserveVisibility } from 'vue-observe-visibility'

export default {
  name: 'MonitorChart',
  components: {
    ChartComponent,
  },
  directives: {
    ObserveVisibility,
  },
  props: {
    searchCond: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      metricsOptionsObj,
      echartSelectList: [],
      filterCond: {
        searchType: null,
        dcId: null,
        timeRange: [],
        areas: [],
        treeQuota: null,
      },
      collapseCond: {
        // 传给MonitorCollapse组件的参数
        monitorType: 'monitor.nat',
        ctSubtype: 'NatServer',
        metrics: ['nat_dev_online'],
        timeRange: [],
      },
      dataObj: {}, // 统一请求的折线图数据,
      areas: [], // 对应dc、指标的集群选项，默认从查询条件中拿，全量接口返回了则用全量接口的
      loading: false, // 全量请求时loading
      currentRenderIndex: 0, // 当前渲染到的索引
      renderedCharts: {}, // 用于跟踪已渲染的图表
      observer: null, // 添加 observer 属性
    }
  },
  computed: {
    ...mapState('monitor', ['dcInfoList', 'clusterCodeNameObj']),
    // 添加新的计算属性
    shouldRenderImmediately() {
      return !this.areas || this.areas.length <= 2
    },
  },
  watch: {
    // 表格数据需要与图的数据异步请求处理，所以在监听searchCond中不请求数据而是父组件直接调用refreshRequest函数请求数据
    searchCond: {
      handler(newVal) {
        if (newVal) {
          // console.log('查询参数不一样变化this.collapseOpen', this.collapseOpen)
          const {
            searchType,
            dcId = null,
            timeRange = [],
            dayTime,
            areas,
          } = newVal
          // console.log('newVal', newVal, 'areas', areas)
          if (searchType) {
            this.echartSelectList = this.metricsOptionsObj[searchType]
            const treeQuota = this.metricsOptionsObj[searchType][0].value
            const [monitorType, ctSubtype, metric] = treeQuota.split('-')
            const metrics = metric.split(',')
            const formData = {
              monitorType,
              ctSubtype,
              metrics,
              timeRange,
              dayTime,
              dcId,
            }
            this.filterCond = { ...formData, treeQuota, areas }
            // this.areas = areas // 查询条件变化则重置area选项 注意：此处不需要，在图表请求返回时重置，避免两者不一致时图表切换变化
            this.areas = [undefined] // 默认放一个区域
            this.collapseCond = formData
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {},
  mounted() {
    this.setupIntersectionObserver()
  },
  beforeDestroy() {
    // 清理 observer
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
  },
  methods: {
    setupIntersectionObserver() {
      const options = {
        root: null,
        rootMargin: '0px',
        threshold: 0.3,
      }

      this.observer = new IntersectionObserver((entries) => {
        const visibleIndexes = []
        entries.forEach((entry) => {
          const index = parseInt(entry.target.getAttribute('data-chart-index'))
          if (entry.isIntersecting && !this.renderedCharts[index]) {
            visibleIndexes.push(index)
          }
        })

        if (visibleIndexes.length > 0) {
          this.renderChartsProgressively(visibleIndexes)
        }
      }, options)

      this.$nextTick(() => {
        const chartElements = document.querySelectorAll('[data-chart-index]')
        chartElements.forEach((el) => {
          this.observer.observe(el)
        })
      })
    },
    // 搜索条件变化刷新请求
    async refreshRequest(callback, refreshTime = null) {
      if (this.observer) {
        this.observer.disconnect()
      }

      // 如果不是刷新操作（没有refreshTime），重置所有渲染状态
      if (!refreshTime) {
        this.renderedCharts = {}
      }

      // 获取数据
      await this.getAllMonitorDataList(this.collapseCond, refreshTime)

      // 数据加载完成后重新设置观察者
      this.$nextTick(() => {
        this.setupIntersectionObserver()
        if (callback) callback()
      })
    },
    // select切换echart数据
    changeSelect(val) {
      const { timeRange, dcId, areas } = this.filterCond
      const [monitorType, ctSubtype, metric] = val.split('-')
      const metrics = metric.split(',')
      const formData = {
        monitorType,
        ctSubtype,
        metrics,
        timeRange,
        dcId,
      }
      this.filterCond = { ...formData, treeQuota: val, areas }
      this.collapseCond = formData
      this.getAllMonitorDataList(formData)
    },
    handleFocus() {
      this.$emit('focus')
    },
    // 获取全量折线图数据
    async getAllMonitorDataList(formData, refreshTime = null) {
      if (!formData) {
        formData = this.filterCond
      }
      const { monitorType, metrics, timeRange, dcId } = formData

      const data = {
        monitorType,
        metrics,
        dcId,
      }

      if (timeRange && timeRange.length) {
        data.startTime = timeRange[0]
        data.endTime = timeRange[1]
        data.startTimeTmp = window
          .moment(timeRange[0])
          .format('YYYY-MM-DD HH:mm:ss')
        data.endTimeTmp = window
          .moment(timeRange[1])
          .format('YYYY-MM-DD HH:mm:ss')
      }
      this.searchTimeRange = timeRange

      if (monitorType === 'monitor.udt' && dcId === '1') {
        delete data.dcId
      }
      // wsConnect 中app连接数，有点特殊，需要前端在入参的extra字段里加个内容：group：appId
      if (
        monitorType === 'monitor.wsConnect' &&
        data.metrics[0] === 'connectNum'
      ) {
        data.extra = {
          group: 'appId',
        }
      }
      this.loading = true
      try {
        const res = await getAllMonitorDataApi(data)
        this.loading = false
        const resultObj = dealLineChartData(
          data,
          res,
          this.clusterCodeNameObj,
          refreshTime
        )
        if (resultObj) {
          const { dataObj, areas } = resultObj
          this.dataObj = dataObj
          this.areas = areas

          // 只在刷新数据时（有refreshTime）处理新增区域的渲染状态
          if (refreshTime) {
            areas.forEach((_, index) => {
              if (!Object.hasOwn(this.renderedCharts, index)) {
                this.$set(this.renderedCharts, index, false)
              }
            })
          }
        }
      } catch (err) {
        this.loading = false
        console.error(err)
      }
    },
    renderChartsProgressively(indexes = []) {
      const BATCH_SIZE = 2 // 每批渲染的图表数量
      const RENDER_DELAY = 50 // 每批之间的延迟时间（毫秒）
      let currentIndex = 0

      const renderNextBatch = () => {
        if (currentIndex >= indexes.length) return

        // 渲染当前批次的图表
        const endIndex = Math.min(currentIndex + BATCH_SIZE, indexes.length)
        for (let i = currentIndex; i < endIndex; i++) {
          const indexToRender = indexes[i]
          this.$set(this.renderedCharts, indexToRender, true)
        }

        currentIndex = endIndex

        // 如果还有未渲染的图表，安排下一批
        if (currentIndex < indexes.length) {
          setTimeout(() => renderNextBatch(), RENDER_DELAY)
        }
      }

      // 开始渲染第一批
      renderNextBatch()
    },
    isChartRendered(index) {
      return index < this.currentRenderIndex
    },
    // 新增方法：根据条件返回图表数据
    getChartData(item, index) {
      if (!this.renderedCharts[index]) {
        // 返回空数据结构，保持图表基本框架显示
        return {
          xAxis: [],
          seriesData: [{ name: '', data: [] }],
          topData: [],
          refreshTime: null,
        }
      }
      return this.dataObj ? this.dataObj[item ? item.value : null] : null
    },
    handleVisibilityChange(isVisible, index, entry) {
      // 添加日志来检查可见性比例
      if (entry) {
        // console.log(`Chart ${index} visibility ratio:`, entry.intersectionRatio)
        // 只有当可见比例超过阈值时才渲染
        if (isVisible && entry.intersectionRatio >= 0.5) {
          this.$set(this.renderedCharts, index, true)
        }
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.monitor-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: content-box;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  .echart-search-wrapper {
    width: 100%;
    background: white;
    padding: 10px 0px 0px 0px;
  }
  .monitor-content-wrapper {
    width: 100%;
    height: calc(100% - 55px);
    box-sizing: border-box;
    overflow-y: auto;
  }
  .monitor-full-content-wrapper {
    width: 100%;
    height: 100%;
    box-sizing: content-box;
    overflow-y: auto;
  }
  .monitor-list-wrapper {
    width: 100%;
    height: 100%;
    min-height: 300px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 0px 10px 0px 10px;
    .monitor-item-wrapper {
      width: calc(50% - 10px);
      min-height: 300px;
    }
    .monitor-line-wrapper {
      width: calc(100%);
      height: 100%;
    }
  }
}
.chart-placeholder {
  height: 322px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding: 10px 0px;
}

.loading-indicator {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
  font-size: 14px;
}
</style>
<style lang="scss">
.monitor-wrapper .echart-search-wrapper .tvt-select .tvt-field-set {
  border: none;
}
</style>
