<template>
  <!-- 设备日志 -->
  <div class="page-wrapper">
    <div class="search-wrapper">
      <tvt-select
        v-model="filterCond.p2pServer"
        :options="domainOptions"
        filterable
        allow-create
        default-first-option
        :mockplaceholder="$t('deviceSearchTab.p2pServerSelect')"
        :clearable="false"
        style="width: 250px; margin-right: 20px"
        @change="getDeviceInfoList"
      />
      <multi-input
        v-model="filterCond.snList"
        :mockplaceholder="$t('deviceSearchTab.snListInput')"
        :input-type="2"
        class="device-mutli-input"
        clearable
        :has-limit="true"
        style="width: 320px; margin-right: 20px"
        tag-max-length="32"
        @change="handleChange"
      />
      <div class="search-btn-box">
        <el-button type="primary" size="small" round @click="handleReset">{{
          $t('reset')
        }}</el-button>
        <el-button type="primary" size="small" round @click="handleRefresh">{{
          $t('refresh')
        }}</el-button>
      </div>
      <div class="search-left-wrapper"></div>
    </div>
    <!-- 设备信息表格 -->
    <div class="table-wrapper">
      <tvt-table
        ref="myTable"
        v-myLoading="loading"
        :data="deviceInfoList"
        :columns="deviceInfoColumn"
        :border="true"
        :border-bottom="true"
        :pagination="false"
        @onFetchData="getDeviceInfoList"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.prop === 'online'">
            <span>{{
              row[column.prop]
                ? $t('deviceSearchTab.onLine')
                : $t('deviceSearchTab.offLine')
            }}</span>
          </template>
          <template v-if="column.prop === 'bind'">
            <span>{{
              row[column.prop]
                ? $t('alarmHistoryTab.yes')
                : $t('alarmHistoryTab.no')
            }}</span>
          </template>
          <template v-if="column.prop === 'bindUserInfo'">
            <span>{{ row['bindUserEmail'] || row['bindUserPhone'] }}</span>
          </template>
        </template>
      </tvt-table>
    </div>
  </div>
</template>
<script>
import { debounce } from '@/utils/common'
import { getDomainList, getP2PTest } from '@/api/basicTool'
import MultiInput from '@/components/common/MultiInput.vue'
import { getLocale } from '@/lang'
export default {
  name: 'DeviceSearch',
  components: {
    MultiInput,
  },
  data() {
    return {
      defaultTimeRange: [],
      filterCond: {
        p2pServer: '',
        snList: [],
      },
      loading: false,
      domainOptions: [],
      deviceInfoList: [],
      deviceInfoColumn: [
        {
          label: 'SN',
          prop: 'sn',
          width: 300,
        },
        {
          label: this.$t('deviceSearchTab.onLineStatus'),
          prop: 'online',
          slotName: 'bodyCell',
          width: 120,
        },
        {
          label: this.$t('versionTab.version'),
          prop: 'devVersion',
          minWidth: 100,
        },
        {
          label: this.$t('deviceSearchTab.p2pVersion'),
          prop: 'p2pVersion',
          width: 120,
        },
        {
          label: this.$t('deviceSearchTab.bindStatus'),
          prop: 'bind',
          slotName: 'bodyCell',
          width: 110,
        },
        {
          label: this.$t('deviceSearchTab.bindUserInfo'),
          prop: 'bindUserInfo',
          slotName: 'bodyCell',
          minWidth: 150,
        },
        {
          label: 'DcCode',
          prop: 'dcCode',
          width: 100,
        },
        {
          label: this.$t('deviceSearchTab.devType'),
          prop: 'devType',
          width: 120,
        },
        {
          label: this.$t('deviceSearchTab.publicIp'),
          prop: 'publicIp',
          minWidth: 100,
        },
        {
          label: this.$t('deviceSearchTab.publicPort'),
          prop: 'publicPort',
          width: 100,
        },
        {
          label: this.$t('deviceSearchTab.localIp'),
          prop: 'localIp',
          minWidth: 150,
        },
        {
          label: this.$t('deviceSearchTab.localPort'),
          prop: 'localPort',
          width: 100,
        },
      ],
    }
  },
  async mounted() {
    await this.getDomainList()
    // this.getDeviceInfoList()
  },
  methods: {
    // 获取domian列表
    async getDomainList() {
      try {
        const { data = [] } = await getDomainList(getLocale())
        const domainOptions = data
          .filter((item) => item.domainType === 'ddc-udt-redirect')
          .map((item2) => ({
            label: item2.domain,
            value: item2.domain,
          }))
        this.domainOptions = domainOptions
        // 微服务列表默认选中第一个
        this.filterCond.p2pServer = domainOptions[0].value
      } catch (error) {
        console.error(error)
      }
    },
    getDeviceInfoList: debounce(async function () {
      try {
        const { p2pServer, snList } = this.filterCond
        if (!p2pServer || !snList || !snList.length) {
          // 没有p2pServer和sn不让查询
          return
        }
        this.loading = true
        const params = {}
        if (p2pServer) {
          params.p2pServer = p2pServer
        }
        if (snList && snList.length) {
          params.snList = snList
        }
        const { data = [] } = await getP2PTest(params)
        this.deviceInfoList = data
        this.loading = false
      } catch (error) {
        console.error(error)
        this.loading = false
      }
    }, 200),
    handleChange(list) {
      this.filterCond.snList = list
      this.$nextTick(() => {
        this.getDeviceInfoList()
      })
    },
    handleReset() {
      // 条件重置
      this.filterCond = {
        ...this.filterCond,
        p2pServer: this.domainOptions[0].value,
        snList: [],
      }
      this.$nextTick(() => {
        this.getDeviceInfoList()
      })
    },
    handleRefresh() {
      this.getDeviceInfoList()
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .tooltip-ellipsis-box {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
::v-deep .search-wrapper .device-mutli-input .el-input__inner {
  width: 320px;
}

::v-deep .search-wrapper .device-mutli-input.isFocus .label {
  border: 1px solid #dcdfe6;
  border-radius: 18px;
  width: 320px;
  box-sizing: border-box;
}

::v-deep .search-wrapper .device-mutli-input.isFocus .el-input__inner {
  border: 1px solid #429efd;
  box-sizing: border-box;
}
</style>
