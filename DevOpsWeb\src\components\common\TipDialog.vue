<template>
  <tvt-dialog
    :title="$t(title)"
    :show.sync="visible"
    :width="width"
    :modal-append-to-body="false"
    :append-to-body="true"
    :cancel-text="$t('cancel')"
    :submit-text="$t('confirm')"
    @close="onClose"
    @Cancel="onCancel"
    @Submit="submit"
  >
    <div class="tip-box">
      {{ tipMsg }}
    </div>
  </tvt-dialog>
</template>

<script>
export default {
  name: 'TipDialog',
  props: {
    tipMsg: {
      type: String,
      default: '',
    },
    title: {
      // 标题对应的多语言key；默认是tips
      type: String,
      default: 'tips',
    },
    width: {
      type: String,
      default: '320px',
    },
  },
  data() {
    return {
      visible: false,
    }
  },
  methods: {
    open() {
      this.visible = true
    },
    submit() {
      this.visible = false
      this.$emit('submit')
    },
    onClose() {
      this.visible = false
      this.$emit('onClose')
    },
    onCancel() {
      this.visible = false
      this.$emit('onClose')
    },
  },
}
</script>

<style lang="scss" scoped>
.tip-box {
  min-height: 120px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 16px;
  padding-bottom: 20px;
  color: rgba(0, 0, 0, 0.6);
}
</style>
