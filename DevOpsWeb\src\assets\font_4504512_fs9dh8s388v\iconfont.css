@font-face {
  font-family: "element-icons"; /* Project id 4504512 */
  src: url('iconfont.woff2?t=1712884855578') format('woff2'),
       url('iconfont.woff?t=1712884855578') format('woff'),
       url('iconfont.ttf?t=1712884855578') format('truetype');
}

.element-icons {
  font-family: "element-icons" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.el-icon-filter:before {
  content: "\e717e";
}

