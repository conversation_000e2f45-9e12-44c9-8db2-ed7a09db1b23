<template>
  <div class="config-edit">
    <tvt-dialog
      ref="configEditRef"
      :title="$t('configTab.addResource')"
      :show.sync="showFlag"
      width="600px"
      :close-on-click-modal="false"
      :cancel-text="$t('cancel')"
      :submit-text="$t('confirm')"
      @close="closeDialog"
      @Cancel="closeDialog"
      @Submit="btnSave"
    >
      <el-form ref="form" :model="form" :rules="rules">
        <el-row :gutter="30">
          <el-col :span="12">
            <el-form-item prop="projectName">
              <tvt-select
                v-model="form.projectName"
                :label="$t('configTab.projectName')"
                clearable
                required
                :options="projectNameList"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="resourceIp">
              <tvt-input
                v-model="form.resourceIp"
                :label="$t('configTab.resourceIp')"
                clearable
                required
                maxlength="32"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="12">
            <el-form-item prop="instanceId">
              <tvt-input
                v-model="form.instanceId"
                :label="$t('configTab.instanceId')"
                clearable
                required
                type="number"
                maxlength="32"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="orderId">
              <tvt-input
                v-model="form.orderId"
                :label="$t('configTab.orderId')"
                clearable
                type="number"
                maxlength="32"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="12">
            <el-form-item prop="startXms">
              <tvt-input
                v-model="form.startXms"
                :label="$t('configTab.startXms')"
                clearable
                type="number"
                :min="0"
                :max="4096"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="startXmx">
              <tvt-input
                v-model="form.startXmx"
                :label="$t('configTab.startXmx')"
                clearable
                type="number"
                :min="0"
                :max="4096"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </tvt-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { IP_REG } from '@/utils/validate.js'
import { resList } from '@/api/installDeploy.js'
import { getLocale } from '@/lang'
export default {
  name: 'ConfigEdit',
  components: {},
  data() {
    // ip格式校验
    const validateIp = (rule, value, callback) => {
      if (value && !IP_REG.test(value)) {
        callback(new Error(this.$t('ipUncorrect')))
      } else {
        callback()
      }
    }
    // 内存大小校验
    const startLimit = (rule, value, callback) => {
      if ((value && value < 0) || value > 1024 * 4) {
        callback(new Error(this.$t('configTab.startMinMax')))
      }
      if (value && this.form.startXms > this.form.startXmx) {
        callback(new Error(this.$t('configTab.startLimit')))
      } else {
        callback()
      }
    }
    return {
      showFlag: false,
      form: {
        environmentCode: '',
        dcName: '',
        projectName: '',
        resourceIp: '',
        instanceId: '',
        orderId: '',
        startXms: '',
        startXmx: '',
      },
      rules: {
        projectName: [
          {
            required: true,
            message: this.$t('configTab.projectNameRequired'),
            trigger: 'blur',
          },
        ],
        resourceIp: [
          {
            required: true,
            message: this.$t('configTab.ipResourceRequired'),
            trigger: 'blur',
          },
          { validator: validateIp, trigger: 'blur' },
        ],
        instanceId: [
          {
            required: true,
            message: this.$t('noEmpty', {
              name: this.$t('configTab.instanceId'),
            }),
          },
        ],
        startXms: [{ validator: startLimit, trigger: 'blur' }],
        startXmx: [{ validator: startLimit, trigger: 'blur' }],
      },
      projectNameList: [],
    }
  },
  computed: {
    ...mapState('params', ['environmentCode', 'dcName']),
  },
  methods: {
    // 获取微服务数组
    getProjectNameList() {
      let { environmentCode, dcName } = this
      resList({
        dcName: dcName ? dcName : undefined,
        environmentCode: environmentCode,
        pageNum: 1,
        pageSize: 999,
        lang: getLocale(),
      })
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            if (res.data) {
              let projectNameList = res.data.records.map(
                (item) => item.projectName
              )
              projectNameList = [...new Set(projectNameList)]
              this.projectNameList = projectNameList.map((item) => {
                return {
                  label: item,
                  value: item,
                }
              })
            } else {
              this.projectNameList = []
            }
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 弹框打开时 清除第一次的自动校验回调
    open() {
      this.showFlag = true
      this.$nextTick(() => {
        this.$refs['form'].resetFields()
        this.getProjectNameList()
        this.form.environmentCode = this.environmentCode
        this.form.dcName = this.dcName
      })
    },
    btnSave() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.$emit('btnSave', this.form)
          this.showFlag = false
        } else {
          return false
        }
      })
    },
    // 关闭弹框并清理数据
    closeDialog() {
      this.showFlag = false
    },
  },
}
</script>
