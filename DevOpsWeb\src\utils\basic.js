import MD5 from 'js-md5'
import SHA512 from './secret/sha512' // sha512加密
import selfLocaLStorage from '@/utils/selfLocalStorage'

/**
 * 生成32位随机正整数 0~2147483647 (2^32)
 */
export const getNonce = () => Math.floor(Math.random() * 2147483646 + 1)

/**
 * 生成basic数据
 * @param ver     {string} 必填    -- 版本号
 * @param time    {number} 必填    -- 时间戳
 * @param id      {string} 必填    -- 0
 * @param nonce   {string} 必填    -- 32位随机数
 * @param token   {string} 非必填  -- 登陆成功后必填，从后台获取，在localStorage中存取
 * @param sign    {string} 非必填  -- 签名，从后台获取，在localStorage中存取
 */
export const getBasic = ({
  ver = '1.0',
  time = new Date().getTime(),
  id = 1,
  nonce = getNonce(),
  token = selfLocaLStorage.getItem('opsToken'),
  sign,
} = {}) => {
  return { ver, time, id, nonce, token, sign }
}

/**
 * 生成sign签名
 * @param {Object} obj: 必须有pwd属性
 * @param {array} argsArray 拼接项数组
 */
export const creatSignForDpikey = (obj, argsArray) => {
  let str = ''
  let arr = argsArray
  if (!arr) {
    arr = Object.keys(obj).filter((v) => v !== 'pwd')
  }
  arr.sort()
  for (let key of arr) {
    str += obj[key]
    str += '#'
  }
  str += MD5(obj['pwd'])
  let Mstr = SHA512.encrypt(str)
  return Mstr
}
