import request from '@/api/request'
const baseUrl = process.env.NODE_ENV === 'development' ? '/dev-ops' : '/dev-ops'

/*
告警配置
*/

// 获取告警配置列表
export const getMonitorConfList = (data) =>
  request.post(`${baseUrl}/monitor/config-list`, data)

// 编辑告警配置
export const editMonitorConf = (data) =>
  request.post(`${baseUrl}/monitor/config-edit`, data)

// 获取告警历史数据
export const getMonitorHisList = (data) =>
  request.post(`${baseUrl}/monitor/alert-list`, data)

// 获取实时告警数据
export const getMonitorList = (data) =>
  request.post(`${baseUrl}/monitor/current-alert-list`, data)

// 修改告警配置的状态
export const setMonitorStatus = (data) =>
  request.post(`${baseUrl}/monitor/config-change-status`, data)

// 获取实时告警数据
export const getAlarmDataList = (data) =>
  request.post(`${baseUrl}/monitor/current-alert-list`, data)

// 获取DC主机列表
export const getDcHostList = (data) =>
  request.post(`${baseUrl}/monitor/config-dc-host-list`, data)
