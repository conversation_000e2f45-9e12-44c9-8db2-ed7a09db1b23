import Vue from 'vue'
import axios from 'axios'
import router from '../router' // 获取路由
import { Message } from 'element-ui' // message提示信息组件
import { getBasic } from '@/utils/basic.js'
import { getLocale } from '@/lang'
import errorCode from '@/api/errorCode.js'
import specialCode from '@/api/specialCode.js'
import { RenewToken } from '@/api/user'
import AES from '@/utils/secret/aes.js' // AES加密
import { debounce } from '@/utils/common'
import selfLocaLStorage from '@/utils/selfLocalStorage'

const service = axios.create({
  baseURL: '',
  timeout: 60000,
})

// 格式化请求参数
const formatParams = (params) => {
  const basic = getBasic()
  return {
    basic,
    data: params,
  }
}

// 退出登录

let opsTokenTip = true
const logout = () => {
  // 将envHomeQuota额外保存下载，其余的清除--特殊要求
  const treeQuota = selfLocaLStorage.getItem('envHomeQuota') || null
  selfLocaLStorage.clear()
  sessionStorage.clear()
  // 将treeQuotq暂存下来
  if (treeQuota) {
    selfLocaLStorage.setItem('envHomeQuota', treeQuota)
  }
  router.push('/login') // 跳转登录页
  setTimeout(function () {
    opsTokenTip = true
  }, 5000)
}

let showOtherMsg = true

let isRenewalFlag = false // 标识正在续签
const tipsCommon = debounce(function (code) {
  if (opsTokenTip) {
    opsTokenTip = false
    Vue.prototype.$message.error(errorCode[getLocale()][code])
    isRenewalFlag = false
    logout()
  }
})

let renewPromise = null
const renewPromiseFn = (response) => {
  renewPromise = new Promise((resolve, reject) => {
    RenewToken({})
      .then((res) => {
        const oldSid = selfLocaLStorage.getItem('sid')
        let { token, sid } = res.data
        let deSid = ''
        if (sid) deSid = AES.decrypt(sid, oldSid) // 解密sid 秘钥为上一个解密后的sid
        selfLocaLStorage.setItem('opsToken', token)

        selfLocaLStorage.setItem('sid', deSid)
        let newData = JSON.parse(response.config.data)
        newData.basic.token = token
        response.config.data = newData
        setTimeout(() => {
          isRenewalFlag = false
          renewPromise = null
        }, 60000)
        resolve(token)
      })
      .catch((err) => {
        reject(err)
      })
  })
  return renewPromise
}

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    config.data = config.data || ''
    const { data, type, basic } = config.data
    if (data && basic) return config //如果传参时有传 basic 则不需要再加一次basic
    if (config.url !== '/login') {
      // 判断请求是否是登录接口
      config.headers.token = selfLocaLStorage.getItem('opsToken') // 如果不是登录接口，就给请求头里面设置token
    }
    config.data = type === 'upload' ? data : formatParams(config.data)
    return config // 返回这个配置对象，如果没有返回，这个请求就不会发送出去
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const errorObj = errorCode[getLocale()]
    const status = response.status
    // 响应成功时
    if (status === 200 && response.data && response.data.basic) {
      const { code, msg } = response.data.basic
      return new Promise((resolve, reject) => {
        if ([7003, 7009, 7088, 7090, 10002, 9104].indexOf(code) > -1) {
          reject(tipsCommon(code))
        } else if (
          code === 7004 &&
          response.config.url.indexOf('user/token-renewal') > -1
        ) {
          reject(tipsCommon(code))
        } else if (code === 7004) {
          if (!isRenewalFlag) {
            isRenewalFlag = true
            renewPromiseFn(response, code)
              .then(async () => {
                const result = await service.request(response.config)
                resolve(result || null)
              })
              .catch(() => {
                reject(tipsCommon(code))
              })
          } else {
            renewPromise.then(async (token) => {
              const localToken = selfLocaLStorage.getItem('opsToken')
              const reqToken = token
              if (localToken !== reqToken) {
                selfLocaLStorage.setItem('opsToken', reqToken)
                const newData = JSON.parse(response.config.data)
                newData.basic.token = token
                response.config.data = newData
                const result = await service.request(response.config)
                resolve(result || null)
              } else {
                const error = {
                  code,
                  msg: errorObj[code] || msg,
                  resData: response.data ? response.data.data : response.data,
                }
                reject(error)
              }
            })
          }
        } else if (code === 200) {
          resolve(response.data)
        } else {
          const error = {
            code,
            msg: '',
            resData: response.data ? response.data.data : response.data,
          }
          if (!specialCode.includes(code)) {
            const codeMsg = errorObj[code]
            if (codeMsg) {
              if (showOtherMsg) {
                error.msg = codeMsg
                Message({
                  message: codeMsg,
                  type: 'error',
                  duration: 2 * 1000,
                })
                showOtherMsg = false
                setTimeout(() => {
                  showOtherMsg = true
                }, 2000)
              }
            } else {
              //处理通用得 error code
              if (showOtherMsg) {
                error.msg = msg
                Message({
                  message: msg,
                  type: 'error',
                  duration: 2 * 1000,
                })
                showOtherMsg = false
                setTimeout(() => {
                  showOtherMsg = true
                }, 2000)
              }
            }
          }
          reject(error)
        }
      })
    } else if (response.config.responseType === 'blob') {
      return new Promise((resolve, reject) => {
        let reader = new FileReader()
        reader.readAsText(response.data)
        reader.onload = (e) => {
          try {
            let boldData = JSON.parse(e.target.result)
            if (boldData.basic.code === 7004) {
              reject(tipsCommon(boldData.basic.code))
            } else {
              resolve(response.data)
            }
          } catch (error) {
            resolve(response.data)
          }
        }
      })
    } else {
      const error = { code: '', msg: errorObj['9500'] }
      return Promise.reject(error)
    }
  },
  (error) => {
    const errorObj = errorCode[getLocale()]
    if (error.message.includes('timeout')) {
      if (showOtherMsg) {
        Message({
          message: errorObj['12345'], //网络连接超时
          type: 'error',
          duration: 2 * 1000,
        })
        showOtherMsg = false
        setTimeout(() => {
          showOtherMsg = true
        }, 2000)
      }
    } else if (error && error.response) {
      // 处理失败的状态码  状态码404 500 503之类的
      let statusCode = error.response.status
      if (showOtherMsg) {
        Message({
          message: errorObj[statusCode],
          duration: 2000,
          type: 'error',
        })
        showOtherMsg = false
        setTimeout(() => {
          showOtherMsg = true
        }, 2000)
      }
    } else if (error.message == 'Network Error') {
      if (showOtherMsg) {
        Message({
          message: errorObj['12344'], //网络连接失败
          type: 'error',
          duration: 2 * 1000,
        })
        showOtherMsg = false
        setTimeout(() => {
          showOtherMsg = true
        }, 2000)
      }
    }
    return Promise.reject(error)
  }
)

export default service
