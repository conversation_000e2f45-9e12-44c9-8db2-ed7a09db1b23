export default {
  enterImgCode: 'Please enter the graphic verification code',
  emptyImgCode: 'Graphic verification code cannot be empty!',
  errorImgcode: 'Graphic verification code error!',
  hello: 'Hello！',
  cancel: 'cancel',
  confirm: 'confirm',
  info: 'info',
  edit: 'edit',
  editSuccess: 'edit successfully!',
  on: 'on',
  off: 'off',
  filter: 'filter',
  delete: 'delete',
  deleteSuccess: 'delete successfully!',
  addContinue: 'add and continue',
  index: 'index',
  operation: 'operation',
  search: 'search',
  searchText: 'Search',
  add: 'add',
  input: 'Please enter',
  choose: 'Please choose',
  refresh: 'Refresh',
  import: 'import',
  export: 'Export',
  exportFail: 'Export failed！',
  ipUncorrect: 'ip uncorrect',
  downloadTemplate: 'download template',
  importSuccess: 'import successfully!',
  importFail: 'import failed！',
  chooseImportExcel: 'please choose import file！',
  uploadSuccess: 'upload successfully',
  detail: 'detail',
  close: 'close',
  noData: 'noData',
  LoadingMsg: 'Loading...',
  LoadingUpload: 'Uploading...',
  LoadingDownload: 'Downloading...',
  packageTagLoading: 'packageTagLoading',
  haveChoose: 'Added data',
  addThen: 'Please add data first！',
  chooseEnvAndDcName: 'Please select the environment and DC name',
  modifyPass: 'Modify Password',
  logout: 'logout',
  download: 'Download',
  noEmpty: '{name} cannot be empty',
  all: 'All',
  projectNameInput: 'Please enter micorservice name',
  pleaseEnter: 'Please enter {type}',
  pleaseSelect: 'Please select {type}',
  pleaseCheckOne: 'Please check at least one option',
  columnSetting: 'Column setting',
  activityTypeTRequire: 'Field name cannot be empty',
  compareTypeRequire: 'Comparison method cannot be empty',
  inputValRequire: 'Input cannot be empty',
  microservicesPhd: 'Please select microservices',
  serviceName: 'Collection microservice name',
  logCreateTime: 'Log generation time',
  status: 'Collection task status',
  logCollectTime: 'Log collection time',
  logCloudUrl: 'Log cloud storage URL address',
  selectTime: 'Please select a date',
  serviceList: 'List of microservices:',
  logCreateTimeLab: 'Log generation time:',
  collecting: 'Collecting',
  collectSucc: 'Collection successful',
  collectFail: 'Collection failed',
  notEmptySerName: 'Please select microservices before proceeding!',
  maxSupportSerName: 'Up to 5 microservices can be collected, please reselect!',
  quotaSelect: 'Please select indicators',
  instanceSelect: 'Please select an instance',
  areaSelect: 'Please select a region',
  startDate: 'Start date',
  endDate: 'End date',
  today: 'Today',
  yesterday: 'Yesterday',
  weekAgo: 'Week ago',
  lastHour: 'Last hour',
  last3Hour: 'Last 3 hour',
  last6Hour: 'Last 6 hour',
  last12Hour: 'Last 12 hour',
  last24Hours: 'Last 24 hours',
  last7d: 'Last 7 days',
  last30d: 'Last 30 days',
  last3m: 'Last 3 months',
  last6m: 'Last 6 months',
  last1y: 'Last year',
  last2y: 'Last 2 years',
  timePlaceholder: 'Please select a time',
  fullScreen: 'Full screen',
  fullScreenView: 'Full screen view',
  save: 'Save',
  restoration: 'Restoration',
  zoom: 'Region zoom',
  allClose: 'Close all',
  allOpen: 'Open all',
  open: 'Open',
  alarmStatus: 'Status',
  alarmEmailStatus: 'Email Alarm Status',
  alarmSmsStatus: 'Sms Alarm Status',
  allEmailClose: 'Close all email alarm',
  allEmailOpen: 'Open all email alarm',
  allSmsClose: 'Close all sms alarm',
  allSmsOpen: 'Open all sms alarm',
  hostMonitor: 'Host monitor',
  cpuRate: 'CPU utilization rate',
  loadOne: '1-minute average load',
  loadFive: '5-minute average load',
  loadFifteen: '15-minute average load',
  hostAvailMemory: 'Available memory',
  hostMemoryRate: 'Memory usage rate',
  hostAvailDisk: 'Remain space',
  hostDiskRate: 'Space utilization rate',
  hostRecvmb: 'Incoming traffic',
  hostSentmb: 'Traffic',
  hostIoRead: 'IO read',
  hostIoWrite: 'IO write',
  hostBandwidthRate: 'Bandwidth utilization rate',
  microService: 'MicroService',
  javaMicroService: 'Java MicroService',
  cplusMicroService: 'C++ MicroService',
  webMicroService: 'Web MicroService',
  hostTraffic: 'Host traffic',
  microMemoryRate: 'MicroService memory rate',
  microcpuRate: 'CPU usage rate',
  mysqlDbSlows: 'mysql slow query count',
  redisMemory: 'Redis used memory',
  redisMemoryRate: 'Redis memory usage rate',
  kafkaMemoryRate: 'Kafka memory usage rate',
  kafkaCpuRate: 'Kafka cpu utilization rate',
  mongoSpace: 'Mongo database occupies space',
  mongoConnections: 'Mongo used connections',
  mongoAvailConnections: 'Mongo available database connections',
  mongoMemory: 'Mongo used memory',
  mongoSlows: 'Mongo slow query count',
  nginxConnections: 'Nginx used connections',
  nginxAvailConnections: 'Nginx available database connections',
  nginxIncrRequests: 'Nginx requests',
  storages: 'Bucket used storage capacity',
  httpsDomain: 'Https domain',
  domainRemainDays: 'Domain effective remaining time',
  certificate: 'Certificate',
  certRemainDays: 'Certificate effective remaining time',
  dpikeyErrors: 'DPikey abnormal log count',
  email: 'Email',
  emailErrors: 'Failure count',
  emailLimits: 'Exceeds frequency',
  emailNums: 'Successfully count',
  emailTps: 'Peak tps sent by email',
  textMessage: 'SMS',
  smsErrors: 'Failure count',
  smsLimits: 'Exceeds frequency',
  smsNums: 'Successfully count',
  smsTps: 'Peak tps sending messages per minute',
  account: 'Account',
  push2: 'Push2.0',
  kafkaNode: 'Kafka Node',
  kafkaCluster: 'Kafka Cluster',
  pushOfflineErrors: 'Offline push failures per minute',
  pushOfflineLimits: 'Offilne exceeds frequency',
  pushOfflinePushs: 'Offline count',
  pushOfflineTps: 'Peak tps pushed offline per minute',
  pushOnlineErrors: 'Online push failures per minute',
  pushOnlinePushs: 'Online count',
  pushOnlineTps: 'Peak tps pushed online per minute',
  pushPushs: 'Total count',
  pushTps: 'Peak tps pushed per minute',
  cpuMemoryHost: 'Cpu/Memory/Host traffic',
  processMemory: 'Process uses physical memory',
  processCpuRate: 'Single process CPU usage',
  processMemoryRate: 'Single process memory usage rate',
  usedMemory: 'Used memory',
  memory: 'Memory',
  disk: 'Disk',
  process: 'Process',
  domain: 'Domain',
  cert: 'Certificate',
  remainDays: 'Effective remaining time',
  videoFlow: 'Video service traffic monitoring',
  mediaTrafficIn: 'Incoming traffic per second',
  mediaTrafficOut: 'Out traffic per second',
  day: ' day ',
  days: ' days ',
  hour: ' hour ',
  hours: ' hours ',
  minute: ' minute ',
  minutes: ' minutes ',
  second: ' second ',
  seconds: ' seconds ',
  mysqlInstance: 'Mysql instance',
  mysqlDb: 'Mysql database',
  fileSizeLimits: 'The file size cannot exceed {size}{unit}!',
  noPermissionPage: 'No permission to access any page',
  nav: {
    home: 'Home',
    installDeploy: 'Install Deploy',
    // resourceConfiguration:'部署环境资源规划',
    resourceConfiguration: 'Resource Configuration',
    relationMaintenance: 'Relation Maintenance',
    // versionMatch:'部署包版本配套',
    versionMatch: 'Version Match',
    configurationParams: 'Configuration Params',
    // deploymentPlan:'部署规划',
    deploymentPlan: 'Deployment Plan',
    // serviceStatus:'服务状态(启停卸载)',
    serviceStatus: 'Automation Testing',
    monitor: 'Monitor',
    resourceMonitor: 'Resource Monitor',
    applicationMonitor: 'Application Monitor',
    businessMonitor: 'Business Monitor',
    envTesting: 'Environmental Testing',
    domainConfiguration: 'Domain Configuration',
    // log:'日志',
    log: 'Download Center',
    logFile: 'Host Log Download',
    // downloadLog:'日志下载',
    downloadLog: 'Log Download Task',
    envHealthCheck: 'Environmental Health Check',
    systemMonitor: 'System Monitor',
    resLayerMonitor: 'Resource Layer Monitor',
    appLayerMonitor: 'Application Layer Monitor',
    businessLayerMonitor: 'Business Layer Monitor',
    domainCert: 'Domain/Certificate Status',
    dpikey: 'Dpikey',
    email: 'Email',
    textMessage: 'Text Message',
    push: 'Push2.0',
    account: 'Account',
    p2p: 'P2P2.0',
    p2p1: 'P2P1.0',
    systemAlarm: 'System Alarm',
    alarmConfig: 'Alarm Configuration',
    alarmHistory: 'Alarm History',
    alarmData: 'Alarm Data',
    systemSLA: 'System SLA',
    systemAnalysis: 'Data Analysis',
    businessData: 'Business Data Statistics',
    costData: 'Cost Data Statistics',
    serviceQuality: 'Service Quality Statistics',
    devBasicTool: 'Devops Basic Tool',
    microServiceLog: 'Microservice  Log',
    restfulLog: 'Restful Interface Log',
    appLog: 'APP Log',
    kafkaSearch: 'Kafka Search',
    ossSearch: 'OSS/S3 Search',
    redisSearch: 'Redis Search',
    mysqlSearch: 'Mysql Search',
    deviceSearch: 'Device Info Search',
    configureParam: 'Configure Param Management',
    deviceLog: 'Device Log',
    operateLog: 'Operate Log',
    scheduleTaskLog: 'Scheduled Task Log',
    devopsStatistics: 'Devops Statistics',
    characteVisitVolume: 'Characteristic Visit Volume',
    lowFrequencyAccess: 'Low-frequency Access',
    featureAlarmStatics: 'Feature Alarm Statistics',
  },
  login: {
    userNamePHD: 'Please enter user name',
    passwordPHD: 'Please enter password',
    login: 'login',
    userNameRequired: 'The username cannot be empty',
    passwordRequired: 'The password cannot be empty',
    confirmLogout: 'Confirm exit?',
    modifyPass: 'Modify Password',
    oldPassword: 'Original Password',
    newPassword: 'New Password',
    confirmPassword: 'Confirm Password',
    noOldPwd: 'The original password cannot be empty',
    noNewpwd: 'The new password cannot be empty',
    noConfirmPwd: 'Confirm password cannot be empty',
    pwdFormUncorrect: 'Incorrect password format',
    pwdDifferent: 'The passwords entered twice are inconsistent',
  },
  configTab: {
    envRes: 'Deploy environmental resources',
    environmentName: 'Environmental Name',
    projectName: 'Microservice name',
    resourceIp: 'IP Resource',
    projectPort: 'Port',
    startXms: 'Minimum startup heap memory(M)',
    startXmx: 'Xmx(M)',
    resourceMemory: 'Host memory',
    resourceCpu: 'Host cpu',
    dcName: 'DC name',
    operation: 'Operation',
    info: 'Tip',
    delete: 'Are you sure to delete this data?',
    deleteEnv: 'Are you sure to delete this environment?',
    environmentNameRequired: 'Environment name cannot be empty',
    projectNameRequired: 'The microservice name cannot be empty',
    projectPortRequired: 'The port cannot be empty',
    ipResourceRequired: 'The IP resource cannot be empty',
    memoryRequired: 'The memory cannot be empty',
    CPURequired: 'The cpu cannot be empty',
    DCnameRequired: 'The dc name cannot be empty',
    ipUncorrect: 'Incorrect IP format',
    addEnv: 'Add Environment',
    addResource: 'Add Resource',
    viewPortMatrix: 'View Port Matrix',
    portNumber: 'Port Number',
    portScope: 'Planning Ports',
    projectType: 'Microservice Type',
    dcType: 'DC Type',
    dcNamePhd: 'Please select the DC name',
    hostName: 'Host name',
    instanceId: 'Instance id',
    orderId: 'Order',
    startMinMax: 'Input Range 0~4096',
    startLimit:
      'The minimum enabled heap memory should be smaller than the maximum enabled heap memory',
    type: 'Classify',
    startXmxParam: 'Enable maximum memory parameter',
    clusterName: 'Cluster name',
    cpu: 'Cpu',
    memory: 'Memory',
    disk: 'Disk',
    cpuCore: 'Cpu Core',
  },
  maintenceTab: {
    projectName: 'Microservices',
    artifactId: 'Artifact ID',
    packageName: 'Package Name',
    architectureType: 'Element type',
    needDeployProjectNames: 'Dependent Deployment',
    dcType: 'DC Type',
    buildOrder: 'Build Order',
    buildDependency: 'Deployment dependencies',
    buildParamNames: 'Build Parameters',
    info: 'Tip',
    delete: 'Are you sure to delete this data?',
    nameRequired: 'The microservice name cannot be empty',
    artifactIdRequired: 'The artifactId cannot be empty',
    packageNameRequired: 'The package name cannot be empty',
    packageNameError:
      'The package name must contain $(version)，以.jar或.zip结尾',
    elementTypeRequired: 'Element type cannot be empty',
    dependencyDeploymentRequired: 'Dependency deployment cannot be empty',
    dcTypeRequired: 'DC type cannot be empty',
    nameInput: 'Please enter the microservice name',
    packageNameInput: 'Please enter the DC type',
  },
  versionTab: {
    downloadPackageTag: 'Download deployment package',
    downloadPackageTagSuccess: 'Successfully downloaded deployment package!',
    downloadPackageTagTip:
      'Download the corresponding deployment package according to the selected deployment package label',
    deleteTag: 'Are you sure to delete the deployment package label?',
    deletePackageTagSuccess: 'Successfully deleted deployment package label!',
    packageTagInput: 'Please select the name of the supporting table',
    packageNameInput: 'Please enter the microservice name',
    manualUploadPKG: 'Manually uploading packages',
    getLatestPKG: 'Get the latest package',
    applyLatestPKG: 'Apply the latest package',
    archivePKG: 'Create version matching table',
    packageTag: 'Supporting table',
    serviceType: 'Service type',
    businessService: 'Business microservices',
    cppService: 'c++ service',
    p2pwebService: 'p2pweb',
    projectName: 'Microservice name',
    artifactId: 'Artifact ID',
    packageName: 'Package file name',
    architectureType: 'Element type',
    version: 'Version',
    archiveTime: 'Archive time',
    uploadPkgUncorrect: 'The manual upload package format is incorrect',
    environmentNameSelect: 'Please select the environment',
    packageTagRequired: 'The name of the supporting table cannot be empty',
    serviceTypeRequired: 'Service type cannot be empty',
    needChooseList: 'Please select at least one piece of data',
    projectNameInput: 'Please enter the microservice name',
    latestApplySuccess: 'Successfully updated deployment package version',
    archiveSuccess: 'Archived successfully',
    uploadLimit:
      'Manual package upload can only upload zip/jar files, and does not exceed 150MB',
    needChooseversion:
      'The version number of the selected data cannot be empty. Please select it before proceeding',
    versionSave: 'Local version matching save',
    versionSaveConfirm: 'Confirm to save local version matching?',
    ossPath: 'Directory path on OSS',
    ossPathSelect: 'Deployment matching selection',
    createTime: 'Creation time',
    checkPackageName: 'Please select the OpenSource package',
    checkNoRepeatName:
      'Only one package can be selected under the same version of microservices',
    checkNoRepeatProject:
      'Only one package can be selected for the same microservice',
  },
  deployPlanTab: {
    joinDeployment: 'Join deployment',
    executeDeployment: 'Deploy microservices',
    environmentName: 'Environment',
    packageTag: 'Deployment Package Label',
    id: 'id',
    instanceId: 'Instance id',
    projectName: 'Microservice name',
    hostIp: 'IP',
    projectPort: 'Port',
    startMaxMemory: 'Start memory(M)',
    version: 'Version',
    dcName: 'Deploy DC',
    deployStatus: 'Deploy status',
    sqlUpStatus: 'nysql upgrade status',
    deployLog: 'Deployment logs',
    envInput: 'Please select the environment',
    microserviceName: 'Please enter the microservice name',
    addDeploy: 'Add Deployment',
    environmentNameRequired: 'Environment cannot be empty',
    projectNameRequired: 'The microservice name cannot be empty',
    resourceIpRequired: 'IP cannot be empty',
    projectPortRequired: 'Port cannot be empty',
    startMaxMemoryRequired: 'The maximum startup memory cannot be empty',
    versionRequired: 'Version number cannot be empty',
    dcNameRequired: 'Deploying DC cannot be empty',
    ipUncorrect: 'The IP format is incorrect',
    status0: 'To be deployed',
    status1: 'Deploying',
    status2: 'Successfully deployed',
    status3: 'Deployment failed',
    startMaxMemoryEmpty:
      'There is a situation where the maximum startup memory is empty in the selected deployment data, which needs to be filled in',
    versionEmpty:
      'There is a situation in the selected deployment data where the version number is empty and needs to be filled in',
    existDeploying:
      'There is data in the selected data that is still in deployment. If deployment still needs to be executed, please remove the data from deployment or wait for deployment to be completed before proceeding with deployment',
    deployEndTime: 'Deployment end time',
    executeDeploymentDalete: 'Batch deletion',
    joinDeployError: 'Abnormal connectivity detection',
    sqlUpCost: 'MySQL upgrade time (seconds)',
    deployCost: 'Deployment time (seconds)',
    totalCost: 'Total time (seconds)',
    tableName: 'Supporting table',
    executeDeployError: 'Execution deployment exception',
    executeOpenSource: 'Deploy open-source software',
    sourceName: 'Open source software name',
    executeCPlusService: 'Deploy C++ Services',
    executeP2pWeb: 'Deploy P2P Web',
    deployType: 'Deploy type',
    deployTypeRequired: 'Deploy type cannot be empty',
    fileType: 'File type',
    fileTypeRequired: 'File type cannot be empty',
    local: 'Local',
    remote: 'Remote',
    fileUrl: 'File url',
    fileUrlRequired: 'File url cannot be empty',
    checkFileUrl: 'Please check the file url',
    deployService: 'Deploy service',
    deployPackageName: 'Deploy pachage name',
    packageSource: 'Software source',
    inputFileUrl: 'Please input file url',
    urlDownload: 'Url download',
    uploadFile: 'Upload file',
    clickUpload: 'Click upload',
    archiveVersion: 'Archive version',
    chooseDeployService: 'Please select deploy service',
    choosePackageSource: 'Please select deploy source',
    pleaseUploadFile: 'Please upload file',
    deployPatch: 'Deploy patch',
    patchPackageSelect: 'Please select patch package',
    remark: 'Remark',
    selectDeploymentMode: 'Please select deployment mode',
    packageTableDeployment: 'Package table deployment',
    patchDeployment: 'Patch deployment',
    remarkPlaceholder: 'Please enter remark',
    cannotOnlySelectOpenSource: 'Cannot just choose openSource packages',
    onlyTagOrService:
      'The supporting table and service type cannot be selected at the same time',
  },
  serviceStatusTab: {
    serviceStatus: 'Service status',
    startService: 'Start',
    restartService: 'Restart',
    reloadService: 'Reload',
    stopService: 'Stop',
    uninstallService: 'Uninstall',
    serviceInstanceId: 'Instance id',
    openEureka: 'Open Eureka',
    openSpringBootAdmin: 'Open SpringBootAdmin',
    openEurekaTip: 'Unable to find Eureka service address',
    openSpringBootAdminTip: 'Unable to find springBootAdmin service address',
  },
  resourceMonitor: {
    envInput: 'Please select an environment name',
    envName: 'Environment name',
    ipResource: 'IP Resource',
    memory: 'Memory',
    CPU: 'CPU',
    diskSpace: 'Disk Space',
    DCname: 'DC name',
    hostCpuUseRate: 'Host cpu utilization rate',
    averageLoad: 'Average load',
    cpuUseRate: 'CPU utilization rate',
    memoryUseRate: 'Memory utilization rate',
    diskRemain: 'Disk remaining',
  },
  applicationMonitor: {
    envInput: 'Please select an environment name',
    envName: 'Environment name',
    ipResource: 'IP Resource',
    name: 'Name',
    process: 'Process',
    info: 'Info',
    abnormalInfo: 'Abnormal information',
  },
  envTesting: {
    env: 'environment',
    type: 'Classification',
    testingItem: 'Detection items',
    testingRes: 'Detection result',
    testingDesc: 'Description of detection content',
    typePhd: 'Please select a category',
    testingItemPhd: 'Please select the detection item',
    testingResPhd: 'Please select the detection result',
    typePhd1: 'Please enter a category',
    testingItemPhd1: 'Please enter detection items',
    testingResPhd1: 'Please enter the detection results',
    testing: 'Detection',
    normal: 'Normal',
    abnormal: 'Abnormal',
    paramConfig: 'Configuration parameter detection',
    microServiceStatus: 'Microservice status',
    dbTableAndField: 'Field detection',
    testingTime: 'Detection time',
    dcNamePhd: 'Please select the DC name',
    envPhd: 'Please select an environment name',
    dcNameEmpty: 'DC name cannot be empty',
    dataBase: 'Database',
  },
  hostTab: {
    importTemplate: 'Resource planning detection update',
    hostInitialization: 'Host initialization',
    searchName: 'Host name/ip',
    hostName: 'Host name',
    hostIp: 'Host ip',
    hostInit: 'Host initialization status',
    nfsInit: 'NFS installation status',
    installStatus: 'Software installation status',
    agentVersion: 'Agent Version',
    updateTime: 'Update time',
    status0: 'To be initialized',
    status1: 'Initializing',
    status2: 'Success',
    status3: 'Fail',
    statusY: 'Initialization not completed',
    statusN: 'Initialization completed',
    nfsStatus0: 'To be installed',
    nfsStatus1: 'Installation in progress',
    nfsStatus2: 'Success',
    nfsStatus3: 'Fail',
    nfsStatusY: 'Installation not completed',
    nfsStatusN: 'Installation completed',
    importSuccess: 'Resource planning detection update successfully ',
  },
  warnTab: {
    RealTimeAlarm: 'Real-time alarm',
    title: 'Real time alarm data',
    ip: 'IP',
    host: 'Node',
    ct: 'Time',
    warnInfo: 'Alarm Information',
  },
  trendMonitor: {
    title: 'Real time monitoring trend data',
    monitorTitle: 'Trend data',
    onlineDevices: 'NAT: online devices',
    deviceRegist: 'NAT: Device registration indicators',
  },
  resourceTab: {
    monitorTableTitle: 'Indicator Data Table',
    ip: 'IP',
    node: 'Node',
    host: 'Node',
    time: 'Collection time',
    ct: 'Collection time',
    cpuRate: 'CPU utilization rate(%)',
    loadOne: '1-minute average load',
    loadFive: '5-minute average load',
    loadFifteen: '15-minute average load',
    availMemory: 'Available memory(M)',
    memoryRate: 'Memory usage rate(%)',
    fs: 'Filesystem of disk',
    disk: 'Total space of filesystem(M)',
    availDisk: 'Remain space(M)',
    diskRate: 'Space utilization rate(%)',
    recvmb: 'Incoming traffic(Mbits/s)',
    sentmb: 'Traffic(Mbits/s)',
    bandwidthRate: 'Bandwidth utilization rate(%)',
    memory: 'Memory(M)',
    processCpuRate: 'CPU rate(%)',
    processMemoryRate: 'Memory rate(%)',
    nfsAvailable: 'NFS available',
    pid: 'Process ID',
    parentPid: 'Parent ID',
    name: 'Process name',
    user: 'User',
    state: 'Process status',
    threads: 'Threads',
    openFiles: 'Open files',
    sqlId: 'SQL description',
    cost: 'Execution time(ms)',
    st: 'Start execution time',
    errorCode: 'Error code',
    errorType: 'Error type',
    errorMsg: 'Error message',
    errorTime: 'Error time',
    businessMsg: 'Business message',
    ioRead: 'IO read(kb/s)',
    ioWrite: 'IO write(kb/s)',
    hi: 'CPU hardware interrupts time(%)',
    si: 'CPU software interrupts time(%)',
    steal: 'CPU steal time(%)',
  },
  microServeTab: {
    projectName: 'Project name',
    projectPort: 'Microservice port',
    startXmx: 'Xmx(M)',
    pid: 'Process ID',
    instanceId: 'Instance id',
    available: 'Services available',
    cpuRate: 'CPU usage rate(%)',
    memory: 'Used memory(M)',
    memoryRate: 'Memory usage rate(%)',
    restarts: 'Restarts',
    dmpfiles: 'Memory overflow files',
    type: 'Type',
  },
  mySqlTab: {
    available: 'Available',
    dbName: 'Database name',
    // dbSpaces: 'mysql占用总空间(M)',
    dbTables: 'Tables',
    maxConnections: 'Max connections ',
    dbConnections: 'Used connections',
    dbAvailConnections: 'Available connections',
    dbDeadlocks: 'Deadlocks',
    dbSpaces: 'Occupies space(M)',
    dbSlows: 'Slow querys',
    spaceRate: 'Space rate(%)',
    availSpace: 'Available space(M)',
    mysqlType: 'Mysql type',
    totalSpace: 'Total space(M)',
  },
  redisTab: {
    available: 'Available',
    memory: 'Used memory(M)',
    memoryRate: 'Used memory rate(%)',
    connections: 'Client connections',
    blockedClients: 'Blocked connections',
    rejectedConnections: 'Rejected connections',
    clusterDowns: 'Cluster node downs',
    redisType: 'Redis type',
  },
  kafkaTab: {
    available: 'Kafka available',
    memory: 'Used memory(M)',
    memoryRate: 'Used memory rate(%)',
    cpuRate: 'CPU utilization rate(%)',
    lags: 'Stacked messages',
    clusterDowns: 'Cluster node downs',
  },
  mongoTab: {
    available: 'Available',
    spaces: 'Occupies space(M)',
    connections: 'Connections used',
    availConnections: 'Remain connections',
    memory: 'Used Memory(M)',
    requests: 'Requests per minute',
    slows: 'Slow querys',
  },
  nginxTab: {
    available: 'Available',
    connections: 'Connections used',
    availConnections: 'Remain available connections',
    totalRequests: 'Total requests',
    incrRequests: 'Requests per minute',
  },
  ossTab: {
    bucketName: 'Bucket name',
    bucketType: 'Bucket type',
    businessType: 'Business type',
    available: 'Bucket available',
    storages: 'Bucket used storage capacity(M)',
    objects: 'The storage objects used by the bucket',
  },
  queueTab: {
    queueName: 'Queue name',
    queueType: 'Queue type',
    maxSize: 'Maxsize',
    usedSize: 'Used size',
    usedRate: 'Used rate(%)',
  },
  apacheTab: {
    busyWorkers: 'Busy workers',
    idleWorkers: 'Idle workers',
    incrRequests: 'Requests per minute',
    available: 'Apache available',
  },
  wsConnectTab: {
    connectNum: 'Instance app connections',
    totalConnectNum: 'Instance total connections',
  },
  domainCertTab: {
    domain: 'Domain',
    customerName: 'Customer name',
    envType: 'Environment type',
    available: 'Domain available',
    remainDays: 'Effective remaining time (days)',
    cert: 'Certificate name',
    appId: 'Associated AppId',
    ptp: 'Push service type',
  },
  dpikeyTab: {
    dpikeySend: 'DPikey issued',
    dpikeyErr: 'DPikey exception log',
    errors: 'abnormal log(number/minute)',
  },
  websocketTab: {
    heartbeat: 'The heartbeat of P2P and device services is normal',
    disconnects: 'Disconnections',
    instanceId: 'Device service instance',
    serverInstance: 'Server instance',
    clientInstance: 'Client instance',
    p2pInstanceId: 'P2P instance',
    websocketType: 'Websocket type',
    agentHost: 'Agent host',
  },
  emailTab: {
    emailErr: 'Email sending failure with abnormal alarm',
    emailLimit: 'Email sending exceeding frequency alarm',
    errors: 'Failure count(number/minute)',
    limits: 'Exceeds frequency(number/minute)',
    nums: 'Successfully count(number/minute)',
    tps: 'Peak tps sent by email (number/second)',
    summary: 'Total(number/minute)',
  },
  textMessageTab: {
    smsErr: 'Sms sending failure with abnormal alarm',
    smsLimit: 'Sms sending exceeding frequency control alarm',
    errors: 'Failure count(number/minute)',
    limits: 'Exceeds frequency(number/minute)',
    nums: 'Successfully count(number/minute)',
    tps: 'Peak tps sending messages per minute (number/second)',
  },
  pushTab: {
    pushErr: 'Offline push sending failure with abnormal alarm',
    pushLimit: 'Offline push exceeding frequency control alarm',
    offlineErrors: 'Offline failures',
    offlineLimits: 'Offilne exceeds frequency',
    offlineTps: 'Peak tps pushed offline',
    offlinePushs: 'Offline success',
    onlineErrors: 'Online failures',
    onlinePushs: 'Online success',
    onlineTps: 'Peak tps pushed online',
    errors: 'Total failures',
    pushs: 'Total success',
    tps: 'Peak tps pushed',
    apnsPushs: 'IOS push success',
    apnsErrors: 'IOS push failure',
    fcmPushs: 'Google push success',
    fcmErrors: 'Google push failure',
    hmsPushs: 'Huawei push success',
    hmsErrors: 'Huawei push failure',
    linePushs: 'Line push success',
    lineErrors: 'Line push failure',
  },
  accountTab: {
    accountType: 'Account type',
    registerErr: 'Abnormal alarm during account registration process',
    loginErr: 'Abnormal alarm during account login process',
    registerErrors: 'Register exceptions(per minute)',
    loginErrors: 'Login exceptions(per minute)',
    registers: 'Registered accounts',
    logins: 'Login times',
  },
  natTab: {
    instanceId: 'Instance id',
    serviceInstanceId: 'Service instance id',
    natInstanceId: 'NAT instance id',
    nodeName: 'Region Name',
    clusterName: 'Cluster name',
    clusterCode: 'Cluster code',
    nat_cleint_p2p_req_sn_online:
      'The connections requested by clients through SN',
    nat_cleint_p2p_req_token_online:
      'The connections requested by the client through datoken',
    nat_client_offline: 'Client disconnections',
    nat_client_online: 'Client launches',
    nat_client_reg: 'Client registrations',
    nat_dev_big_heat: 'Large heartbeats',
    nat_dev_offline: 'Disconnections',
    nat_dev_online: 'Devices online',
    nat_dev_reg: 'Device registrations',
    nat_dev_small_heat: 'Small heartbeats',
    nat_dev_update_ip: 'Device side heartbeat negotiations',
    nat_dev_big_heat_v6: 'Double port device with large heartbeat count',
    nat_dev_small_heat_v6: 'Small heartbeats on dual port devices',
    nat_dev_update_ip_v6: 'Heartbeat negotiations on dual port devices',
    nat_dev_online_dec: 'Online reduced',
    syncErrors: 'RDC sync errors',
    sendErrors: 'Send errors',
    cplusGetErrors: 'P2P errors',
    javaGetErrors: 'Service errors',
    maxDevNum: 'Max devices',
    devNum: 'Online devices',
    connectionNum: 'Request connections',
    clientNum: 'Online clients',
    p2pReqNum: 'P2P requests',
    relayReqNum: 'P2P forwarding requests',
    restartNum: 'Total restarts',
  },
  relayTab: {
    relay_client_req: 'Client requests',
    relay_dev_req: 'Device requests',
    relay_session: 'Connections',
    relay_session_end: 'Ended connections',
    relay_speed_limit_count: 'Speed limited connections',
    currentSession: 'Current forwarding connections',
    natServerAddr: 'NAT address',
    maxSession: 'Max forwarding connections',
  },
  redirectTab: {
    redirect_client: 'Client redirects',
    redirect_client_full_list:
      'The total server lists for client redirection requests',
    redirect_dev: 'Device side redirects',
    redirect_dev_inc: 'Device side redirects added',
  },
  push1Tab: {
    customer: 'Customer',
    tpnsMsgRecv: 'TPNS Received Alarms(number/second)',
    tpnsDispMsg: 'TPNS Distributed Alarms(number/second)',
    tpnsDevTotal: 'TPNS Push Device Total',
    tpnsEnableDev: 'TPNS Enabled Devices',
    tpnsPendingJobCount: 'TPNS Queue Backlog Count',
    amdsPushReq: 'AMDS Push Requests(number/second)',
    amdsPushMsg: 'AMDS Push Messages',
    amdsExpCertCount: 'AMDS Expired Certificates',
    gmdsPushReq: 'GMDS Push Requests(number/second)',
    gmdsDispMsg: 'GMDS Message Distribution',
    gpnsPushReq: 'GPNS Push Requests(number/second)',
    gpnsPushMsg: 'GPNS Push Messages',
    gpnsClientNum: 'GPNS Online Phones',
    gpnsClientRegNum: 'GPNS Registration Requests',
    sdnsDevSrvReq: 'Device Redirects(number/second)',
    sdnsAppSrvReq: 'App Redirects',
  },
  alarmConfigTab: {
    ctTypeSelect: 'Please select a collection item',
    ctType: 'Collection items',
    ctPeriod: 'Collection cycle',
    ctPeriodInput: 'Please enter the collection cycle',
    ctSubtype: 'Collection item subclass',
    alarmIndexDesc: 'Alarm indicator name',
    threshold: 'Normal threshold',
    thresholdInput: 'Please enter a normal threshold',
    thresholdSelect: 'Please select a normal threshold',
    alarmThreshold: 'Send alarm threshold',
    alarmThresholdInput: 'Please enter the alarm threshold',
    alarmThresholdSelect: 'Please select the alarm threshold',
    algorithm: 'Indicator algorithm',
    alarmReceiver: 'Alarm receiver',
    alarmEmail: 'Email receiver',
    modifyAlarmReceiver: 'Modify alarm receiver',
    alarmEmailInput: 'Please enter the email receiver',
    alarmReceiverInput: 'Please enter the alarm receiver',
    alarmSms: 'Sms Receiver',
    alarmSmsInput: 'Please enter the Sms receiver',
    alarmLang: 'Languge',
    alarmLangSelect: 'Please select language',
    alarmReceiverLimit: 'A maximum of 10 alarm receivers are allowed',
    emailLangEmpty:
      'Please ensure that the recipient and language of the email/sms are fully filled out',
    collectTime: 'Collect Time(s)',
    hourLimits: 'Hourly alarm sending limit',
    dayLimits: 'Daily alarm sending limit',
    alarmType: 'Alarm category',
    alarmTypeSelect: 'Please select a alarm category',
    ctIndexFieldSelect: 'Please select the alarm indicator name',
  },
  alarmHistoryTab: {
    ctTypeSelect: 'Please select a collection item',
    hostNameInput: 'Please enter the host name',
    dcName: 'DC name',
    dcNameInput: 'Please enter the DC name',
    dcSelect: 'Please select DC',
    rangeSeparator: 'To',
    monitorType: 'Collection items',
    host: 'Host',
    os: 'System',
    ip: 'IP',
    threshold: 'Alarm threshold',
    actual: 'Actual value',
    ct: 'Time',
    msg: 'Information',
    sendNotice: 'Has send alarm',
    sendEmai: 'Has send email',
    sendSms: 'Has send sms',
    yes: 'Yes',
    no: 'No',
  },
  alarmDataTab: {
    systemMonitortType: 'System monitor category',
    warnStartTime: 'Warn start time',
    warnDuration: 'Warn duration',
    alarmObject: 'Alarm object',
  },
  microLogTab: {
    microSelect: 'Please select the microservice list',
    hostIpSelect: 'Please select hostIp',
    dayTimeSelect: 'Please select date',
    fileName: 'File name',
    fileUpdateTime: 'File update time',
    fileSize: 'Size',
    hostIpInput: 'Please input host IP',
    downloadFreq: 'Download too frequently, please try again in 10 seconds',
  },
  appLogTab: {
    logIDInput: 'Please enter logID',
    phoneTypeInput: 'Please enter phone model',
    softVersionInput: 'Please enter phone version',
    errorCodeInput: 'Please enter errors',
    mccCodeInput: 'Please enter mcc code',
    deviceSnInput: 'Please enter SN',
    logid: 'logID',
    hwmodel: 'Phone model',
    logver: 'Protocol version',
    firmver: 'Phone version',
    mobileVer: 'Phone system version',
    errors: 'Errors',
    mcc: 'Mcc',
    sn: 'SN',
    time: 'Create time',
    downloadLog: 'Download log',
    timezone: 'Time zone',
    memory: 'Memory',
    cpucore: 'CPU core',
    appver: 'Application version',
    logintime: 'Login time',
    nettype: 'Connection status',
    conntype: 'Connection type',
    isreduce: 'Is it compressed',
    reduceformat: 'Compressed format',
    isencrypt: 'Is it encrypted',
    encryformat: 'Encryption method',
  },
  restfullLogTab: {
    userIdInput: 'Please enter userID',
    hostnameSelect: 'Please select hostname',
    requestPathInput: 'Please enter interface address',
    requestPath: 'URL address',
    requsetId: 'RequstID',
    username: 'User name',
    userId: 'UserID',
    startTime: 'Start time',
    endTime: 'End time',
    requestCost: 'Interface time',
    callClient: 'Client',
    responseCode: 'Response code',
    reqParams: 'Request params',
    resParams: 'Response params',
    platform: 'Platform',
    coId: 'Company ID',
    clientIp: 'Client IP',
    partner: 'Partner',
    installer: 'Installer',
    reqStatusSelect: 'Please select request status',
    reqSuccess: 'Request success',
    reqFail: 'Request fail',
    errorMsg: 'Error Message',
    tidInput: 'Please enter tid',
  },
  kafkaSearchTab: {
    topicSelect: 'Please select topic',
    hourSelect: 'Please select hour',
    urlInput: 'Please enter url',
    snInput: 'Please enter sn',
    keyInput: 'Please enter key',
    keywordInput: 'Please enter keyword',
    keyOrUrlInput: 'Please enter url or keyword',
    partition: 'Partition',
    offset: 'Offset',
    content: 'Content',
    createTime: 'Creation time',
    prefixFilter: 'Filter by name prefix',
    fieldSearchKeyInput: 'Please enter fieldSearchKey',
    copySuccess: 'Copy successfully!',
    customSearch: 'Custom search',
    customInput: 'Please input custom search',
    abnormalData: 'Abnormal data format',
    pasteExample: 'Paste the following example:',
    paramsDesc: 'Parameter description',
    hour: 'Hour',
    protocolName: 'Protocol name',
    forExample: 'For example:',
    ciphertextSN: 'Ciphertext SN',
    searchKeywords: 'Search keywords',
  },
  ossSearchTab: {
    bucketSelect: 'Please select bucket',
    name: 'Name',
    typeSize: 'Type/Size',
    modifyTime: 'Last modified time',
    folder: 'folder',
  },
  redisSearchTab: {
    instanceSelect: 'Please select instance list',
  },
  mysqlSearchTab: {
    customMysql: 'Custom mysql',
    customMysqlInput: 'Please input sql',
    inputDesc:
      'Query restrictions: Only select query statements can be executed, and the number of returned results is limited to 100',
    searchStatus: 'Search status',
    errorMessage: 'Error Message',
    costTime: 'Cost Time',
  },
  deviceSearchTab: {
    p2pServerSelect: 'Please select P2P Server',
    snListInput: 'Please enter SN',
    onLine: 'onLine',
    offLine: 'offLine',
    onLineStatus: 'onLine Status',
    p2pVersion: 'P2P Version',
    bindStatus: 'Bind Status',
    bindUserInfo: 'Bind User Info',
    devType: 'Device Type',
    publicIp: 'Public IP',
    publicPort: 'Public Port',
    localIp: 'Local IP',
    localPort: 'Local Port',
  },
  configParamTab: {
    sourceTypePhd: 'Please select data Source type',
    dataSourceType: 'Data source type',
    dataSourceIdenti: 'Data source identification',
    configuration: 'Configuration',
    configRelease: 'Config release',
    releaseTip: 'Are you sure to release the configuration?',
    configReleaseSucccess: 'Config release success successfully!',
    pendingEffective: 'Pending effective',
    effective: 'Effective',
  },
  operateLogTab: {
    userName: 'Usermame',
    eventModule: 'Event module',
    eventType: 'Event type',
    eventModuleSelect: 'Please select event module',
    eventTypeSelect: 'Please select event type',
  },
  scheduleTaskLogTab: {
    excuteClassPHD: 'Please enter excute class',
    scheduleTaskSelect: 'Please select schedule task',
    scheduleTask: 'Schedule task',
    businessId: 'Buiness ID',
    executeClass: 'Excute class',
    executeMethod: 'Excute method',
    executeStatus: 'Excute status',
    zone: 'Zone',
    scheduleInstanceId: 'Schedule instance id',
    excuteInstanceId: 'Excute instance id',
    createTime: 'Create time',
    modifyTime: 'Modify time',
    excuteCost: 'Excute cost',
    code: 'Code',
    msg: 'Message',
    excutePeroid: 'Excute peroid',
    excuteStatusSelect: 'Please select excute status',
    other: 'Other',
    success: 'Success',
    fail: 'Failed',
    running: 'Running',
    timeout: 'Timeout',
  },
  devopsStatisticsTab: {
    clientType: 'Client type',
    clientTypeSelect: 'Please select client type',
    function: 'Function',
    functionSelect: 'Please select function',
    product: 'Product',
    module: 'Module',
    requestCount: 'Request count',
    successCount: 'Success count',
    failedCount: 'Failed count',
    requestPath: 'Request path',
    accountCount: 'Account count',
    maxCost: 'Max cost(ms)',
    avgCost: 'Avg cost(ms)',
    monitorType: 'Monitor type',
    ctIndexField: 'Monitor Index',
    groupBySelect: 'Please select group by',
    count: 'Count',
    hostAlarm: 'Host alarm',
    microServiceAlarm: 'Micro service alarm',
    openSourceAlarm: 'Open source alarm',
    domainCert: 'Domain and certificate',
    businessMonitor: 'Business monitor',
    host: 'Host',
    alarmCount: 'Alarm count',
    serverSide: 'Server side',
    clientSide: 'Client side',
  },
  authConfig: {
    module: 'Module',
    func: 'Functions',
    // VMS
    vms: 'VMS product',
    vms_account: 'VMS account',
    vms_account_member_mgr: 'VMS member account management',
    vms_account_person_mgr: 'VMS personal account management',
    vms_account_qry: 'VMS account query',
    vms_account_invite_query: 'VMS account invitation query',
    vms_account_mgr: 'VMS account management',
    vms_cloudstorage: 'Cloud storage',
    vms_cloudstorage_mgr: 'Cloud storage management',
    vms_cloudstorage_qry: 'Cloud storage query',
    vms_stgorder_qry: 'Cloud storage order query',
    vms_archives: 'Cloud archive',
    vms_archives_mgr: 'Cloud archive management',
    vms_archives_qry: 'Cloud archive query',
    vms_site: 'Site',
    vms_site_mgr: 'Site management',
    vms_site_qry: 'Site query',
    vms_device: 'Device',
    vms_device_mgr: 'Device management',
    vms_device_qry: 'Device query',
    vms_device_bind: 'Device binding management',
    vms_device_connect: 'Redirect to device web page',
    vms_device_opr: 'Device operations',
    vms_devicetrusteeships: 'Device trusteeship',
    vms_devicetrusteeships_mgr: 'Device trusteeship management',
    vms_devicetrusteeships_qry: 'Device trusteeship query',
    vms_event: 'Device event',
    vms_event_mgr: 'Device event management',
    vms_event_qry: 'Device event query',
    vms_eventrule: 'Event rule',
    vms_eventrule_mgr: 'Event rule management',
    vms_eventrule_qry: 'Event rule query',
    vms_schedule: 'Schedule',
    vms_schedule_mgr: 'Schedule management',
    vms_schedule_qry: 'Schedule query',
    vms_vehicleplate: 'License plate',
    vms_vehicleplate_mgr: 'License plate management',
    vms_vehicleplate_qry: 'License plate query',
    vms_vehicleplate_exp: 'License plate export',
    vms_license: 'License',
    vms_license_mgr: 'License management',
    vms_license_qry: 'License query',
    vms_licorder_qry: 'License purchase query',
    vms_customercustomize: 'Dealer customization',
    vms_customercustomize_qry: 'Dealer customization query',
    vms_collectionchl: 'Favorite channel',
    vms_collectionchl_mgr: 'Favorite channel management',
    vms_collectionchl_qry: 'Favorite channel query',
    vms_config: 'User configuration',
    vms_config_qry: 'User configuration query',
    vms_config_mgr: 'User configuration management',
    vms_archauth_qry: 'Archive permission query',
    vms_archauth_mgr: 'Archive permission management',
    vms_remoteaccessreq: 'Remote access',
    vms_remoteaccessreq_mgr: 'Remote access management',
    vms_remoteaccessreq_qry: 'Remote access query',
    vms_system: 'System management',
    vms_system_dict_qry: 'Data dictionary query',
    vms_system_msg_qry: 'System message query',
    vms_system_paytype_qry: 'Payment type query',
    vms_system_oplog_qry: 'Operation log query',
    vms_system_version_qry: 'System version',
    vms_system_other: 'Other',
    vms_account_role: 'Role configuration',
    vms_account_role_mgr: 'Role management',
    vms_account_role_qry: 'Role query',
    vms_arming: 'Arming/disarming',
    vms_arming_mgr: 'Arming/disarming management',
    vms_arming_qry: 'Arming/disarming query',
    monitor_dev_notify: 'Alarm notification',
    monitor_dev_notify_qry: 'Alarm notification query',
    monitor_dev_notify_mgr: 'Alarm notification management',
    mfa_config: 'MFA configuration',
    mfa_config_qry: 'MFA configuration query',
    mfa_config_mgr: 'MFA configuration management',
    mfa_config_switch_mgr: 'MFA configuration switch management',
    vms_org: 'User organization',
    vms_org_qry: 'User organization query',
    vms_org_mgr: 'User organization management',
    // partner
    partner: 'Cloud services-Partner',
    partner_user: 'Installer account',
    partner_user_member_mgr: 'Installer member management',
    partner_user_person_mgr: 'Installer personal account management',
    partner_coinfo: 'Installer company',
    partner_coinfo_mgr: 'Installer company management',
    partner_coinfo_qry: 'Installer company query',
    partner_customter: 'Customer',
    partner_customter_mgr: 'Customer management',
    partner_customter_qry: 'Customer query',
    partner_tob_user_mgr: 'VMS customer management',
    partner_tob_user_qry: 'VMS customer query',
    partner_site: 'Site',
    partner_site_mgr: 'Site information management',
    partner_site_qry: 'Site information query',
    partner_device: 'Device',
    partner_device_mgr: 'Device management',
    partner_device_qry: 'Device query',
    partner_cloudres: 'Cloud provider',
    partner_cloudres_qry: 'Cloud provider query',
    partner_vms: 'CloudVMS',
    partner_vms_mgr: 'VMS configuration',
    partner_vms_qry: 'VMS query',
    partner_push: 'Push',
    partner_push_mgr: 'Push configuration/management',
    partner_push_qry: 'Push configuration/query',
    partner_ra: 'Remote access',
    partner_ra_mgr: 'Remote access operations',
    partner_ra_qry: 'Remote access query',
    partner_sysinfo: 'System information',
    partner_sysinfo_qry: 'System information query',
    partner_cldstorage: 'Cloud storage',
    partner_cldstorage_qry: 'Cloud storage query',
    partner_feedback: 'Feedback',
    partner_feedback_mgr: 'Feedback management',
    partner_feedback_qry: 'Feedback query',
    partner_account_role: 'Role configuration',
    partner_account_role_mgr: 'Role management',
    partner_account_role_qry: 'Role query',
    partner_other: 'Other',
    partner_other_log_qry: 'Operation logs',
    partner_other_res_qry: 'Feature resources',
    partner_other_remote_access: 'Remote access',
    partner_points_mall: 'Points mall',
    partner_points_mall_mgr: 'Points mall management',
    partner_points_mall_qry: 'Points mall query',
    // user
    user: 'SuperLivePlus/Max',
    user_account: 'Account',
    user_account_qry: 'Account query',
    user_account_mgr: 'Account management',
    user_device: 'Device',
    user_device_qry: 'Device query',
    user_device_mgr: 'Device management',
    user_subscribe: 'Subscription',
    user_subscribe_qry: 'Subscription query',
    user_subscribe_mgr: 'Subscription management',
    user_alarm: 'Alarm',
    user_alarm_qry: 'Alarm query',
    user_alarm_mgr: 'Alarm management',
    user_cloudstorage: 'Cloud storage',
    user_cloudstorage_qry: 'Cloud storage query',
    user_cloudstorage_mgr: 'Cloud storage management',
    user_message: 'Message',
    user_message_qry: 'Message query',
    user_message_mgr: 'Message management',
    user_feedback: 'Feedback',
    user_feedback_qry: 'Feedback query',
    user_feedback_mgr: 'Feedback management',
    user_config: 'User configuration',
    user_config_qry: 'User configuration query',
    user_config_mgr: 'User configuration management',
    user_site: 'Site',
    user_site_qry: 'Site query',
    user_site_mgr: 'Site management',
    user_devicetrusteeships: 'Device trusteeship',
    user_devicetrusteeships_qry: 'Device trusteeship query',
    user_devicetrusteeships_mgr: 'Device trusteeship management',
    user_devicetransfer: 'Device transfer',
    user_devicetransfer_qry: 'Device transfer query',
    user_devicetransfer_mgr: 'Device transfer management',
    user_building: 'Access control building',
    user_building_qry: 'Access control building query',
    user_building_mgr: 'Access control building management',
    // Distributor
    distributor: 'Cloud services-Distributor',
    distributor_account: 'Distributor Account',
    distributor_account_mgr: 'Distributor Enterprise Account Management',
    distributor_account_person_mgr: 'Distributor Personal Account Management',
    distributor_account_qry: 'Distributor Account Query',
    distributor_installerinvite: 'Invite Installer',
    distributor_installerinvite_mgr: 'Installer Invitation Management',
    distributor_subcustomerinvite: 'Invite Sub-distributor',
    distributor_subcustomerinvite_mgr: 'Sub-distributor Invitation Management',
    distributor_storage: 'Cloud Storage Order',
    distributor_storage_qry: 'Cloud Storage Order Query',
    distributor_account_role: 'Role Configuration',
    distributor_account_role_mgr: 'Role Management',
    distributor_account_role_qry: 'Role Query',
    distributor_other: 'Others',
    distributor_other_log_qry: 'Operation Logs',
    distributor_other_message_qry: 'Messages',
  },
}
