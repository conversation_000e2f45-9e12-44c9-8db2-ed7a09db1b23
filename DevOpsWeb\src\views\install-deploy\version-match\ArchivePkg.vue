<template>
  <div class="archivePkg">
    <tvt-dialog
      ref="archivePkgRef"
      :title="$t('versionTab.archivePKG')"
      :show.sync="showFlag"
      width="1000px"
      :cancel-text="$t('cancel')"
      :submit-text="$t('confirm')"
      :close-on-click-modal="false"
      @close="closeDialog"
      @Cancel="closeDialog"
      @Submit="btnSave"
    >
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item prop="packageTag">
          <tvt-input
            v-model="form.packageTag"
            clearable
            required
            :label="$t('versionTab.packageTag')"
          />
        </el-form-item>
        <el-form-item prop="deployRemark">
          <tvt-input
            v-model="form.deployRemark"
            type="textarea"
            :rows="1"
            clearable
            :label="$t('deployPlanTab.remark')"
          />
        </el-form-item>
        <el-form-item prop="serviceType">
          <tvt-select
            v-model="form.serviceType"
            clearable
            required
            :label="$t('versionTab.serviceType')"
            :options="serviceTypeOptions"
            @change="handleServiceTypeChange"
          />
        </el-form-item>
        <el-form-item prop="packageName">
          <!-- <tvt-select
            ref="selectTree"
            v-model="form.packageName"
            multiple
            clearable
            required
            :label="$t('versionTab.ossPathSelect')"
            class="selectTree"
            @change="handleSelectChange"
          >
            <el-option :value="form.packageName" style="height: auto">
              <el-tree
                ref="treeList"
                :data="ossPathList"
                :props="defaultProps"
                :expand-on-click-node="false"
                show-checkbox
                node-key="name"
                @check-change="handleCheckChange"
              />
            </el-option>
          </tvt-select> -->
          <tvt-select-tree
            id="osspath"
            ref="selectTree"
            v-model="form.packageName"
            :options="ossPathList"
            :default-props="defaultProps"
            multiple
            clearable
            required
            :label="$t('versionTab.ossPathSelect')"
            class="selectTree"
            @change="handleCheckChange"
          >
          </tvt-select-tree>
        </el-form-item>
      </el-form>
    </tvt-dialog>
  </div>
</template>

<script>
import { serviceVersionPlace, getOssPackage } from '@/api/installDeploy.js'
export default {
  name: 'DeployAddDialog',
  data() {
    return {
      showFlag: false,
      ossPathList: [],
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'path',
      },
      form: {
        packageTag: '',
        deployRemark: '',
        serviceType: '',
        packageNameNodes: [],
        packageName: [],
      },
      rules: {
        packageTag: [
          {
            required: true,
            message: this.$t('versionTab.packageTagRequired'),
            trigger: 'blur',
          },
        ],
        serviceType: [
          {
            required: true,
            message: this.$t('versionTab.serviceTypeRequired'),
            trigger: 'blur',
          },
        ],
        packageName: [
          {
            required: true,
            message: this.$t('noEmpty', {
              name: this.$t('versionTab.ossPathSelect'),
            }),
            trigger: 'change',
          },
          { validator: this.checkOpenSourceName, trigger: 'change' },
          { validator: this.checkNoRepeatName, trigger: 'change' },
          { validator: this.checkNoRepeatProject, trigger: 'change' },
        ],
      },
      serviceTypeOptions: [
        { label: this.$t('versionTab.businessService'), value: 'java' },
        { label: this.$t('versionTab.cppService'), value: 'cpp' },
        { label: this.$t('versionTab.p2pwebService'), value: 'p2pweb' },
      ],
    }
  },
  methods: {
    checkOpenSourceName(rule, value, callback) {
      let packageNames = this.form.packageName.join()
      if (
        packageNames.includes('Service') &&
        !packageNames.includes('OpenSourceDependency')
      ) {
        callback(new Error(this.$t('versionTab.checkPackageName')))
      } else {
        callback()
      }
    },
    checkNoRepeatName(rule, value, callback) {
      var set = new Set(
        this.form.packageNameNodes.map(function (item) {
          return item.parentPath
            .concat(';')
            .concat(item.businessParams.projectName)
        })
      )
      if (set.size < this.form.packageNameNodes.length) {
        callback(new Error(this.$t('versionTab.checkNoRepeatName')))
      } else {
        callback()
      }
    },
    // 检查是否存在同个服务多个版本
    checkNoRepeatProject(rule, value, callback) {
      var set = new Set(
        this.form.packageNameNodes.map(function (item) {
          return item.businessParams.projectName
        })
      )
      if (set.size < this.form.packageNameNodes.length) {
        callback(new Error(this.$t('versionTab.checkNoRepeatProject')))
      } else {
        callback()
      }
    },
    // 弹框打开时 清除第一次的自动校验回调
    open() {
      this.showFlag = true
      this.$nextTick(() => {
        this.ossPathList = []
        this.$refs['form'].resetFields()
        this.form.packageName = []
        this.form.serviceType = ''
        this.form.packageNameNodes = []
        this.form.deployRemark = ''
        // this.getOssPackageData()
      })
    },
    // 获取部署配套选择项
    getOssPackageData(params) {
      getOssPackage(params)
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            this.ossPathList = res.data
          } else {
            this.ossPathList = []
          }
        })
        .catch((err) => {
          this.ossPathList = []
          console.log(err)
        })
    },
    btnSave() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.$TvtLoading.show({ text: this.$t('LoadingMsg') })
          let packages = this.form.packageNameNodes.map((item) => {
            return {
              name: item.name,
              path: item.path,
              packageName:
                item.businessParams && item.businessParams.packageName,
              projectName:
                item.businessParams && item.businessParams.projectName,
              version: item.businessParams && item.businessParams.version,
            }
          })
          serviceVersionPlace({
            packageTag: this.form.packageTag,
            deployRemark: this.form.deployRemark,
            packages: packages,
            serviceType: this.form.serviceType,
          })
            .then((res) => {
              this.$TvtLoading.hide()
              this.$refs['form'].resetFields()
              if (res.basic && res.basic.code == 200) {
                this.$message.success(this.$t('versionTab.archiveSuccess'))
                this.$emit('btnSave')
              }
            })
            .catch((err) => {
              this.$TvtLoading.hide()
              console.log(err)
            })
          this.showFlag = false
        } else {
          return false
        }
      })
    },
    // 关闭弹框
    closeDialog() {
      this.showFlag = false
    },
    // handleCheckChange() {
    //   this.form.packageName = this.$refs.treeList.getCheckedKeys(true)
    //   this.form.packageNameNodes = this.$refs.treeList.getCheckedNodes(true)
    // },
    // handleSelectChange() {
    //   this.$refs.treeList.setCheckedKeys(checkedIds)
    // },
    handleCheckChange() {
      // this.form.packageName = this.$refs.selectTree.$refs['osspath-tree'].getCheckedKeys(true)
      this.form.packageNameNodes =
        this.$refs.selectTree.$refs['osspath-tree'].getCheckedNodes(true)
    },
    // 获取不同服务类型的配套包并重置配套包的选择
    handleServiceTypeChange(val) {
      const form = {
        ...this.form,
        packageNameNodes: [],
        packageName: [],
        deployRemark: this.form.deployRemark, // 保留备注内容
      }
      this.form = form
      if (val) {
        this.getOssPackageData(val)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-select-dropdown__wrap {
  max-height: 500px;
}
::v-deep .el-dialog__wrapper {
  align-items: start;
  .el-dialog {
    margin-top: 15vh !important;
  }
}
</style>
