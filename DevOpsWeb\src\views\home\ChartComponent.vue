<template>
  <!-- 系统监控-实时监控趋势数据 -->
  <!-- 会单独请求所有集群的趋势图数据,后续考虑与首页的实时监控组件合并 -->
  <div class="treMon-echart-wrapper">
    <div class="echart-line-wrapper">
      <div class="echart-select-tree">
        <popover-checked
          v-if="topData && topData.length > 0"
          :placement="'right'"
          :options="topData"
          :init-check="checkedIntance"
          @handleCheck="handleCheck"
        />
      </div>
      <line-chart
        v-if="initFlag && echartType === 'lineChart'"
        ref="lineChart"
        :echart-key="`${collapseKey}`"
        :title="title"
        :series-obj="{ xAxis, seriesData: seriesFilterData || [] }"
        @timeZoom="handleTimeZoom"
        @fullScreen="handleFullScreen"
      />
      <bar-chart
        v-if="initFlag && echartType !== 'lineChart'"
        ref="lineChart"
        :echart-key="`${collapseKey}`"
        :title="title"
        :series-obj="{ xAxis, seriesData: seriesData || [], invertXY: true }"
        @timeZoom="handleTimeZoom"
        @fullScreen="handleFullScreen"
      />
    </div>
    <tvt-dialog
      v-if="fullScreen"
      :title="fullScreenDialogTitle"
      :show.sync="fullScreen"
      custom-class="fullScreen-dialog"
      width="100vw"
      :cancel-show="false"
      :submit-text="$t('close')"
      :modal-append-to-body="false"
      :foot-show="false"
      style="height: 100vh"
      @close="handleClose"
    >
      <full-screen-chart
        :echart-type="echartType"
        :init-check="checkedIntance"
        :search-cond="{ ...filterCond, timeRange: searchTimeRange }"
        @fullScreen="handleFullScreen"
      />
    </tvt-dialog>
  </div>
</template>
<script>
import FullScreenChart from '@/views/home/<USER>'
import PopoverChecked from '@/components/common/PopoverChecked.vue'
import LineChart from '@/components/common/LineChart.vue'
import BarChart from '@/components/common/BarChart.vue'
import {
  getMonitorDataListApi,
  // getMonitorBarDataApi
} from '@/api/home.js'
import { stampToStrLong } from '@/utils/common'
import { barMetricsNameObj, homeMetricsOptions } from '@/views/home/<USER>'
import { metricsOptionsObj } from '@/views/system-monitor/config'

const REQ_TIME_INTER = 2 * 60 * 1000 // 2分钟时间间隔请求

export default {
  name: 'ChartComponent',
  components: {
    PopoverChecked,
    LineChart,
    BarChart,
    FullScreenChart,
  },
  props: {
    collapseKey: {
      type: String,
      default: 'line-chart',
    },
    collapseOpen: {
      type: Boolean,
      default: false,
    },
    searchCond: {
      type: Object,
      default: () => ({
        monitorType: 'monitor.nat',
        ctSubtype: 'NatServer',
        metrics: ['nat_dev_online'],
      }),
    },
    dataObj: {
      type: Object,
      default: () => ({
        seriesData: [],
        topData: [],
      }),
    },
  },
  data() {
    return {
      ctType: 'cpuRate',
      filterCond: {
        monitorType: 'monitor.nat',
        ctSubtype: 'NatServer',
        metrics: ['nat_dev_online'],
      },
      needInitRequest: false, // 是否需要初始化请求数据，当查询参数变化但是又不展开时，需要将此参数置为true，等待展开时再去请求数据
      xAxis: [],
      seriesData: null,
      initFlag: false, // 是否已经初始化请求数据过,
      echartType: 'lineChart',
      fullScreen: false, // 是否全屏
      searchTimeRange: [], // 记录当前查询时间
      seriesFilterData: null, // 对最终生成的结果进行过滤，针对微服务这种返回多实例的，需要默认勾选TOP5展示
      checkedIntance: [], // 默认勾选展示的实例,在图上展示 默认为TOP5
      topData: [], // 排序的实例
      title: null, // 图的名称
      requestTimer: null, // 定时器
      // 添加缓存的映射对象
      quotaNameObj: null, // 指标名称映射缓存
    }
  },
  computed: {
    // 计算全屏对话框的标题
    fullScreenDialogTitle() {
      let title = ''

      // 添加DC信息 - 显示DC名称而不是集群名称
      if (this.filterCond && this.filterCond.dcId) {
        // 从Vuex store中获取DC信息
        const dcInfo = this.$store.state.monitor.dcInfoList
        if (dcInfo && dcInfo.length > 0) {
          const currentDc = dcInfo.find(
            (dc) => dc.dcId === this.filterCond.dcId
          )
          if (currentDc && currentDc.dcName) {
            title += `${currentDc.dcName}`
          }
        }
      }

      // 添加指标分类名称
      if (this.filterCond && this.filterCond.ctSubtype) {
        title += title
          ? ` - ${this.$t(this.filterCond.ctSubtype)}`
          : this.$t(this.filterCond.ctSubtype)
      }

      // 添加指标信息
      if (
        this.filterCond &&
        this.filterCond.metrics &&
        this.filterCond.metrics.length > 0
      ) {
        // 确保映射对象已初始化
        if (!this.quotaNameObj) {
          this.initQuotaNameMapping()
        }

        // 构建完整指标key用于查找国际化名称
        const { monitorType, ctSubtype } = this.filterCond
        const metricsNames = this.filterCond.metrics
          .map((metric) => {
            const fullKey = `${monitorType}-${ctSubtype}-${metric}`
            // 尝试从缓存的配置中获取国际化名称
            const i18nName = this.quotaNameObj[fullKey]
            if (i18nName) {
              return i18nName
            }
            // 如果配置中没有，尝试直接使用i18n翻译
            return this.$t(metric) || metric
          })
          .join(', ')

        title += ` - ${metricsNames}`
      }

      return title
    },
  },
  watch: {
    searchCond: {
      handler(newVal, oldVal) {
        if (newVal) {
          if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
            // 查询参数不一样
            const {
              monitorType,
              ctSubtype,
              metrics = [],
              area = null,
              timeRange,
              dcId,
            } = newVal
            let newMetrics = metrics.slice().reduce((pre, next) => {
              //针对主机CPU的负载loadOne,loadFive,loadFifteen 这种需要切分开来
              const temp = pre.concat(next.split(','))
              return temp
            }, [])
            const formData = {
              monitorType,
              ctSubtype,
              metrics: newMetrics,
              area,
              timeRange,
              dcId,
            }
            this.filterCond = formData
            let echartType = 'lineChart'
            // 判断图形类型
            // if (ctSubtype === 'Https域名' || ctSubtype === '证书') {
            //   echartType = 'barChart'
            // }
            this.echartType = echartType
            // 恢复checkedIntance
            this.checkedIntance = []
            // 判断图形类型
            if (echartType === 'barChart') {
              // 柱状图就走正常请求逻辑
              if (this.collapseOpen) {
                // 查询参数变化且是打开状态，则请求数据
                this.xAxis = []
                this.seriesData = null
                this.needInitRequest = false
                this.initFlag = true
                this.getMonitorDataList(formData)
                // 定时刷新功能 -- 2分钟刷新请求一次
                if (this.requestTimer) {
                  clearInterval(this.requestTimer) // 清除定时器
                }
                // 没有选择时间范围则开启定时器
                if (!(timeRange && timeRange.length)) {
                  this.requestTimer = setInterval(() => {
                    this.getMonitorDataList()
                  }, REQ_TIME_INTER)
                }
              } else {
                this.needInitRequest = true
                this.xAxis = []
                this.seriesData = null
                this.initFlag = false
              }
            } else {
              // 折线图则直接用父级传来的数据
              // 更新图的查询时间--全屏时带入时间查询
              this.searchTimeRange = timeRange
            }
            if (area) {
              this.title = area.label
            } else {
              this.title = null
            }
          }
        }
      },
      deep: true,
      immediate: true,
    },
    collapseOpen: {
      handler(val) {
        if (val) {
          // 展开时
          if (this.echartType === 'barChart') {
            // 柱状图就走正常请求逻辑
            if (this.needInitRequest) {
              // 需要请求数据
              this.xAxis = []
              this.seriesData = null
              this.needInitRequest = false
              this.initFlag = true
              this.getMonitorDataList()
              // 定时刷新功能 -- 2分钟刷新请求一次
              if (this.requestTimer) {
                clearInterval(this.requestTimer) // 清除定时器
              }
              const { timeRange = [] } = this.filterCond || {}
              // 没有选择时间范围则开启定时器
              if (!(timeRange && timeRange.length)) {
                this.requestTimer = setInterval(() => {
                  this.getMonitorDataList()
                }, REQ_TIME_INTER)
              }
            }
          } else {
            // 折线图则直接用父级传来的数据
            // 更新图的查询时间--全屏时带入时间查询
            if (this.filterCond && this.filterCond.timeRange) {
              this.searchTimeRange = this.filterCond.timeRange
            }
          }
        }
      },
      deep: true,
      immediate: true,
    },
    dataObj: {
      handler(newVal, oldVal) {
        if (newVal) {
          if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
            // 传递的数据不一样
            this.initFlag = true
            const {
              xAxis,
              topData,
              seriesData,
              refreshTime = null,
              topDisplayNum = 5, // 默认取前5个
            } = newVal
            this.xAxis = xAxis
            this.seriesData = seriesData

            // 处理TOP5展示逻辑
            this.topData = topData.map((item) => ({
              label: item,
              value: item,
            }))
            if (refreshTime) {
              // 有更新时间，则保留checkIntance
              this.getSeriesFilterData(
                topData.length ? this.checkedIntance : null,
                seriesData
              ) // 过滤展示的实例
            } else if (topData && topData.length > 0) {
              // 从topData中取前5的数据
              const newCheckedValue = topData.slice(0, topDisplayNum)
              this.checkedIntance = newCheckedValue
              this.getSeriesFilterData(newCheckedValue, seriesData) // 过滤展示的实例
            } else {
              this.seriesFilterData = seriesData
              this.checkedIntance = []
            }
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    // 在组件创建时初始化映射对象
    this.initQuotaNameMapping()
  },
  mounted() {},
  beforeDestroy() {
    if (this.requestTimer) {
      clearInterval(this.requestTimer) // 清除定时器
    }
  },
  methods: {
    getMonitorDataList(formData) {
      this.loading = true
      if (this.$refs.lineChart && this.$refs.lineChart.chartInstance) {
        this.$refs.lineChart.chartInstance.showLoading({
          text: 'loading',
          color: 'rgba(20, 149, 247, 0.7)', //设置加载颜色
          textColor: '#000',
          maskColor: 'rgba(255, 255, 255, 0.2)',
          zlevel: 0,
        })
      }
      if (!formData) {
        formData = this.filterCond
      }
      const { monitorType, ctSubtype, metrics, timeRange, dcId, area } =
        formData
      //   const data = { pageNum:  current, pageSize: size}
      const data = {
        monitorType,
        metrics,
        dcId,
      }
      // console.log('查询条件', timeRange)
      if (timeRange && timeRange.length) {
        data.startTime = timeRange[0]
        data.endTime = timeRange[1]
        data.startTimeTmp = window
          .moment(timeRange[0])
          .format('YYYY-MM-DD HH:mm:ss')
        data.endTimeTmp = window
          .moment(timeRange[1])
          .format('YYYY-MM-DD HH:mm:ss')
      }
      if (area) {
        data.extra = {
          clusterCode: area.value,
        }
      }
      this.searchTimeRange = timeRange
      let echartType = 'lineChart'
      let requestFn = getMonitorDataListApi
      // 判断图形类型
      // if (ctSubtype === 'Https域名' || ctSubtype === '证书') {
      //   requestFn = getMonitorBarDataApi
      //   echartType = 'barChart'
      // }
      // 磁盘均不需要时间参数
      // if (['磁盘'].includes(ctSubtype)) {
      //   // 这些柱状图不需要时间指标
      //   delete data.startTime
      //   delete data.startTimeTmp
      //   delete data.endTime
      //   delete data.endTimeTmp
      // }
      this.echartType = echartType
      // P2P2.0中选择RDC、UDT时，不传dcId
      if (
        this.echartType === 'lineChart' &&
        monitorType === 'monitor.udt' &&
        dcId === '1'
      ) {
        delete data.dcId // 不传dcId
      }
      // wsConnect 中app连接数，有点特殊，需要前端在入参的extra字段里加个内容：group：appId
      if (
        this.echartType === 'lineChart' &&
        monitorType === 'monitor.wsConnect' &&
        data.metrics[0] === 'connectNum'
      ) {
        data.extra = {
          ...(data.extra || {}),
          group: 'appId',
        }
      }
      return requestFn(data)
        .then((res) => {
          this.loading = false
          if (this.$refs.lineChart && this.$refs.lineChart.chartInstance) {
            this.$refs.lineChart.chartInstance.hideLoading()
          }
          if (res.basic && res.basic.code == 200) {
            if (res.data) {
              if (echartType === 'lineChart') {
                // 折线图逻辑
                // 解析返回结果，生成按照采集项分类的数据
                const {
                  fields,
                  datas,
                  topData = [],
                  topDisplayNum = 5,
                } = res.data
                // 遍历fields找到每个指标的下标
                const fieldIndexObj = {}
                fields.forEach((item, index) => {
                  fieldIndexObj[item] = index
                })
                let seriesName = Object.keys(datas).sort() // 用作series分类依据
                if (topData && topData.length > 0) {
                  // 如果有排序，则按照排序顺序来
                  seriesName = topData
                }
                // 先遍历所有数据取出ct （时间） 用作x轴 有些是用其他用作x轴
                const xAxisField = 'st'
                const stIndex = fieldIndexObj[xAxisField]
                const stSet = seriesName.reduce((pre, next) => {
                  // pre.add(next[stIndex])
                  datas[next].forEach((item2) => {
                    // const ct = stampToStrLong(parseInt(item2[stIndex])) // 转换成YYYY-MM-DD HH:mm:ss的形式
                    const st =
                      xAxisField === 'st'
                        ? stampToStrLong(parseInt(item2[stIndex]) * 1000)
                        : item2[stIndex] // 转换成YYYY-MM-DD HH:mm:ss的形式
                    item2[stIndex] = st
                    pre.add(st)
                  })
                  return pre
                }, new Set())
                // console.log('ctSet', ctSet)
                const stSortArr = Array.from(stSet).sort() // 升序排列
                // const ctArr = ctSortArr.map((item) => item.split(' ')[1]) // 取出HH:mm:ss
                // console.log('ctSortArr', ctSortArr)
                this.xAxis = stSortArr
                // console.log('ctArr', ctArr)
                // 遍历ctSortArr确定每个时刻的index
                const stIndexObj = {}
                stSortArr.forEach((item, index) => {
                  stIndexObj[item] = index
                })
                const seriesData = [] // 整体series的对象，每个指标项对应一个key  [{ name: '主机1'， data: [...] }, { name: '主机2'， data: [...] }]
                // 遍历seriesName,生成数据
                seriesName.forEach((name) => {
                  // 找到
                  if (metrics.length > 1) {
                    // 说明是类似'loadOne', 'loadFive', 'loadFifteen'这种三个放在一块的，需要在series层面额外加一级 [{ name: '主机1-loadOne'， data: [...] }, { name: '主机1-loadFive'， data: [...] }, { name: '主机1-loadFifteen'， data: [...] }]
                    metrics.forEach((metric) => {
                      const temp = {
                        name: name + '-' + metric,
                        data: new Array(stSortArr.length).fill(undefined), // 按照所有时刻生成对应的数据数组
                      }
                      seriesData.push(temp)
                    })
                  } else {
                    const temp = {
                      name,
                      data: new Array(stSortArr.length).fill(undefined), // 按照所有时刻生成对应的数据数组
                    }
                    seriesData.push(temp)
                  }
                })
                // 遍历seriesName，找到每个原始数据，再遍历之后将数据生成图所需要的数据
                seriesName.forEach((name, index) => {
                  datas[name].forEach((item) => {
                    if (metrics.length > 1) {
                      // 说明是类似'loadOne', 'loadFive', 'loadFifteen'这种三个放在一块的
                      const len = metrics.length
                      metrics.forEach((metric, index2) => {
                        const stSeriesIndex = stIndexObj[item[stIndex]]
                        const value = metric
                        const quotaIndex = fieldIndexObj[value] // 具体指标数值所在的下标
                        seriesData[index * len + index2].data[stSeriesIndex] =
                          item[quotaIndex]
                      })
                    } else {
                      const stSeriesIndex = stIndexObj[item[stIndex]]
                      const value = metrics[0]
                      const quotaIndex = fieldIndexObj[value] // 具体指标数值所在的下标
                      // console.log('value', value, 'fieldIndexObj', fieldIndexObj, 'quotaIndex', quotaIndex, 'item[quotaIndex]', item[quotaIndex])
                      // value对应指标（CPU利用率） index对应主机的下标，ctSeriesIndex对应ct在xAxis中的下标
                      seriesData[index].data[stSeriesIndex] = item[quotaIndex]
                    }
                  })
                })
                // 如果metrics长度大于0，需要把series上的名称rdc-dev40-01-loadOne换成国际化的名称rdc-dev40-01-1分钟平均负载
                if (metrics.length > 1) {
                  const len = metrics.length
                  seriesName.forEach((name, index) => {
                    metrics.forEach((metric, index2) => {
                      seriesData[index * len + index2].name =
                        name + '-' + this.$t(metric)
                    })
                  })
                }
                // console.log('seriesData', seriesData)
                const newSeriesData = [...seriesData]
                this.seriesData = newSeriesData
                // 判断checkedIntance是否存在,存在则直接使用
                if (this.checkedIntance && this.checkedIntance.length) {
                  this.getSeriesFilterData(this.checkedIntance, newSeriesData) // 过滤展示的实例
                } else {
                  // 处理TOP5展示逻辑
                  this.topData = topData.map((item) => ({
                    label: item,
                    value: item,
                  }))
                  if (topData && topData.length > 0) {
                    // 从topData中取前5的数据
                    const newCheckedValue = topData.slice(0, topDisplayNum)
                    this.checkedIntance = newCheckedValue
                    this.getSeriesFilterData(newCheckedValue, newSeriesData) // 过滤展示的实例
                  } else {
                    this.seriesFilterData = newSeriesData
                  }
                }
              } else {
                // 柱状图逻辑
                // 解析返回结果，生成按照采集项分类的数据
                const { datas, topData = [] } = res.data
                let seriesName = Object.keys(datas).sort() // 用作series分类依据
                if (topData && topData.length > 0) {
                  // 如果有排序，则按照排序顺序来
                  seriesName = topData
                }
                // datas中key作为x轴，value作为纵轴
                const xAxis = []
                // 获取当前指标的label
                const key = `${monitorType}-${ctSubtype}-${metrics.join(',')}`
                const name = barMetricsNameObj[key] || ''
                const seriesData = [{ name, data: [] }]
                seriesName.forEach((key) => {
                  xAxis.push(key)
                  seriesData[0].data.push(datas[key][0][0])
                })
                this.xAxis = xAxis
                this.seriesData = seriesData
                this.checkedIntance = []
                this.seriesFilterData = null
                this.topData = []
              }
            } else {
              this.xAxis = []
              this.seriesData = null
              this.checkedIntance = []
              this.seriesFilterData = null
            }
          }
        })
        .catch((err) => {
          this.loading = false
          if (this.$refs.lineChart && this.$refs.lineChart.chartInstance) {
            this.$refs.lineChart.chartInstance.hideLoading()
          }
          console.log(err)
        })
    },
    // 图上区域选择
    handleTimeZoom(timeZoom) {
      const { startTime, endTime } = timeZoom
      if (startTime && endTime) {
        const {
          monitorType,
          ctSubtype,
          metrics = [],
          dcId,
          area = null,
        } = this.filterCond
        const startTimeTmp = new Date(startTime).getTime()
        const endTimeTmp = new Date(endTime).getTime()
        const formData = {
          monitorType,
          ctSubtype,
          metrics: metrics.slice(),
          dcId,
          area,
          timeRange: [startTimeTmp, endTimeTmp],
        }
        // this.xAxis = []
        // this.seriesData = null
        this.getMonitorDataList(formData)
        // 选择具体时间则去掉定时刷新请求
        if (this.requestTimer) {
          clearInterval(this.requestTimer) // 清除定时器
        }
      } else {
        // 还原
        const {
          monitorType,
          ctSubtype,
          metrics = [],
          dcId,
          area = null,
          timeRange,
        } = this.filterCond
        // console.log('timeRange', timeRange)
        const formData = {
          monitorType,
          ctSubtype,
          metrics: metrics.slice(),
          dcId,
          area,
          timeRange,
        }
        this.getMonitorDataList(formData)
        // 还原则恢复定时刷新请求
        if (this.requestTimer) {
          clearInterval(this.requestTimer) // 清除定时器
        }
        // 没有选择时间范围则开启定时器
        if (!(timeRange && timeRange.length)) {
          this.requestTimer = setInterval(() => {
            this.getMonitorDataList()
          }, REQ_TIME_INTER)
        }
      }
    },
    // 图上toolbox全屏点击事件
    handleFullScreen() {
      const flag = !this.fullScreen
      // console.log('是否全屏', flag)
      this.fullScreen = flag
    },
    handleClose() {
      this.fullScreen = false
    },
    handleCheck(val) {
      this.checkedIntance = val.slice()
      this.getSeriesFilterData(val, this.seriesData)
    },
    // 根据勾选情况过滤seriesData
    getSeriesFilterData(val, seriesData = null) {
      const data = seriesData || this.seriesData
      if (val) {
        // 根据勾选的实例过滤图数据
        const instanceSet = new Set(val)
        this.seriesFilterData = data.filter((item) =>
          instanceSet.has(item.name)
        )
      } else {
        this.seriesFilterData = data
      }
    },
    // 初始化指标名称映射（只调用一次）
    initQuotaNameMapping() {
      if (this.quotaNameObj) {
        return // 已经初始化过了
      }

      this.quotaNameObj = {}

      // 判断当前是否在首页，如果是首页则使用 homeMetricsOptions，否则使用 metricsOptionsObj
      const isHomePage =
        this.$route &&
        (this.$route.name === 'home' || this.$route.path === '/home')

      if (isHomePage) {
        // 首页使用 homeMetricsOptions 的递归遍历配置树方式
        const flattenOptions = (options) => {
          options.forEach((item) => {
            if (item.value && item.label) {
              this.quotaNameObj[item.value] = item.label
            }
            if (item.children && item.children.length > 0) {
              flattenOptions(item.children)
            }
          })
        }
        flattenOptions(homeMetricsOptions)
      } else {
        // 其他页面使用 metricsOptionsObj 的扁平化遍历方式
        Object.keys(metricsOptionsObj).forEach((key) => {
          const options = metricsOptionsObj[key]
          if (Array.isArray(options)) {
            options.forEach((item) => {
              if (item.value && item.label) {
                this.quotaNameObj[item.value] = item.label
              }
            })
          }
        })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.treMon-echart-wrapper {
  width: calc(100%);
  height: 100%;
  min-height: 300px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-direction: row;
  flex-wrap: wrap;
  // padding: 10px;
  .echart-line-wrapper {
    width: calc(100%);
    height: 300px;
    margin: 10px 0px;
    border: 1px solid #c6c6c6;
    position: relative;
    .echart-select-tree {
      position: absolute;
      top: 0px;
      left: 10px;
      z-index: 1;
      width: 20px;
      height: 34px;
      display: flex;
      align-items: center;
    }
  }
}
::v-deep .fullScreen-dialog {
  height: 100vh !important;
}
</style>
