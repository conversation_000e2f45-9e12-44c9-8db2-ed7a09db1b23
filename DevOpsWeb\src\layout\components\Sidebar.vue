<template>
  <div class="sidebar">
    <el-menu
      class="el-menu-vertical-demo"
      :collapse="isCollapse"
      background-color="#36393f"
      text-color="#fff"
      active-text-color="#429efd"
      :default-active="$route.path"
      @open="handleOpen"
      @close="handleClose"
    >
      <div class="menu-logo">
        <img :src="logoSrc" class="logo-img" width="" />
        <div class="logo-title">Cloud DevOps</div>
      </div>
      <template v-for="item in childrenList">
        <el-submenu
          v-if="isShowInner(item)"
          :key="item.name"
          :index="item.path"
        >
          <template slot="title">
            <i :class="item.meta.icon" style="font-size: 20px" />
            <span class="nav_title title-font">{{
              $t(`nav.${item.name}`)
            }}</span>
          </template>
          <el-menu-item-group v-for="item2 in item.children" :key="item2.name">
            <!-- 只渲染到二级 -->
            <!-- 判断展示的环境 -->
            <template
              v-if="
                !item2.showDcType ||
                (envDcInfo && envDcInfo.dcType === item2.showDcType)
              "
            >
              <el-menu-item :index="item2.path" @click="goPath(item2)">
                {{ $t(`nav.${item2.name}`) }}
              </el-menu-item>
            </template>
          </el-menu-item-group>
        </el-submenu>
        <template v-else>
          <el-menu-item
            :key="item.name"
            :index="item.path"
            @click="goPath(item)"
          >
            <i class="el-icon-location" style="font-size: 20px" />
            <span slot="title" class="nav_title title-font">{{
              $t(`nav.${item.name}`)
            }}</span>
          </el-menu-item>
        </template>
      </template>
    </el-menu>
  </div>
</template>

<script>
import EventBus from '@/utils/eventBus.js'
import { mapState } from 'vuex'
export default {
  name: 'SideBar',
  components: {},
  data() {
    return {
      logoSrc: require('@img/logo.svg'),
      childrenList: [],
      viewbox: '0 0 1024 1024',
      activePath: '',
      isCollapse: false,
    }
  },
  computed: {
    ...mapState('params', ['envDcInfo']),
  },
  watch: {
    isCollapse: function () {
      this.$emit('change')
    },
    $route: {
      handler(newVal) {
        if (newVal) {
          this.activePath = newVal.path
        }
      },
      deep: true,
      immediate: true,
    },
    envDcInfo: {
      handler(newVal) {
        if (newVal) {
          // 重新过滤路由
          this.filterRouteList()
        }
      },
    },
  },
  mounted() {
    this.filterRouteList()

    EventBus.$on('changeSiderBar', (data) => {
      this.isCollapse = data.value
    })
  },
  methods: {
    filterRouteList() {
      const routeList = (
        this.$router.options.routes.find((route) => route.name === 'main') || {}
      ).children

      // 先对每个路由的 children 进行过滤
      const filterRouteList = routeList
        .map((item) => {
          // 如果有 children，先过滤 children
          if (item.children && item.children.length) {
            const filteredChildren = item.children
              .filter((child) =>
                this.$isAuthorityExist(child.meta.pageAuthList)
              )
              .filter((child) => {
                return (
                  !child.showDcType ||
                  (this.envDcInfo && this.envDcInfo.dcType === child.showDcType)
                )
              })
            return { ...item, children: filteredChildren }
          }
          return item
        })
        // 然后过滤一级路由
        .filter((item) => {
          // 过滤掉 children 为空的路由
          if (item.children && item.children.length === 0) {
            return false
          }
          // 对一级路由进行权限和环境类型判断
          return (
            this.$isAuthorityExist(item.meta.pageAuthList) &&
            (!item.showDcType ||
              (this.envDcInfo && this.envDcInfo.dcType === item.showDcType))
          )
        })
      // console.log('childrenList', routeList, 'filterRouteList', filterRouteList)
      this.childrenList = filterRouteList
    },
    goPath(item) {
      this.$router.push(item.path)
    },
    isShowInner(item) {
      return item.children && item.children.length
    },
    // handleOpen(key, keyPath) {
    //   console.log(key, keyPath)
    // },
    // handleClose(key, keyPath) {
    //   console.log(key, keyPath)
    // },
    handleOpen() {},
    handleClose() {},
  },
}
</script>
<style lang="scss" scoped>
.title-font {
  font-size: 16px;
}
.menu-logo {
  width: 100%;
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
  .logo-img {
    width: auto;
    height: 30px;
    margin: 0px 17px;
  }
  .logo-title {
    color: white;
    font-size: 20px;
    white-space: nowrap;
    overflow: hidden;
    word-break: break-all;
  }
}
</style>
<style>
.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 240px;
  min-height: 400px;
}
.el-menu-item-group__title {
  padding: 0 !important;
  line-height: normal;
  font-size: 12px;
  color: #909399;
}
.sidebar .el-menu-item {
  text-align: left !important;
}

.sidebar .el-submenu__title {
  height: 48px;
  line-height: 48px;
  text-align: left !important;
}
.sidebar .el-submenu .el-menu-item {
  height: 40px;
  line-height: 40px;
  padding: 0 40px;
  min-width: 200px;
}
.el-menu-item.is-active {
  background-color: #2b2e32 !important;
}
.sidebar .el-submenu__icon-arrow {
  font-size: 16px;
}
</style>
