<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    :viewBox="viewBox"
    :width="width"
    :height="height"
    :aria-labelledby="iconName"
    role="presentation"
    pointer-events="all"
  >
    <g :fill="iconColor" :stroke="strokeColor">
      <slot />
    </g>
  </svg>
</template>

<script>
export default {
  name: 'SvgIcon',
  props: {
    viewBox: {
      type: String,
      default: '0 0 24 24',
    },
    iconName: {
      type: String,
      default: '',
    },
    width: {
      type: [Number, String],
      default: 20,
    },
    height: {
      type: [Number, String],
      default: 20,
    },
    iconColor: {
      type: String,
      default: 'currentColor',
    },
    strokeColor: {
      type: String,
      default: 'currentColor',
    },
  },
}
</script>

<style scoped>
svg {
  display: inline-block;
  vertical-align: baseline;
  margin-bottom: -4px;
  margin-left: 2px;
}
</style>
