<template>
  <!-- 对活动类型进行选择 -->
  <el-popover
    v-model="visible"
    :placement="placement"
    popper-class="popover-search"
    style="cursor: pointer"
  >
    <template #reference>
      <slot name="referenceType">
        <i
          class="el-icon-filter icon-hover"
          :title="$t('filter')"
          style="float: right; font-size: 20px"
        />
      </slot>
    </template>
    <div>
      <el-form ref="conditionForm" :model="formData">
        <div class="form-list-wrapper">
          <div
            v-for="(formItem, index) of formData.conditionList"
            :key="index"
            class="form-line-item"
          >
            <div class="form-operate-wrapper">
              <el-form-item
                :prop="`${index}_activityType`"
                :rules="validatorActivityType(formItem, index)"
              >
                <tvt-select
                  :id="`${index}_activityType`"
                  v-model="formData.conditionList[index].activityType"
                  :popper-append-to-body="true"
                  style="width: 200px"
                  size="mini"
                  @change="(val) => hanldeActivityType(val, index)"
                >
                  <el-option
                    v-for="item in filterOptions"
                    :key="`${index}_${item.value}`"
                    :label="item.label"
                    :value="item.value"
                  />
                </tvt-select>
              </el-form-item>
              <el-form-item
                v-if="showConditionList[index].showCompareType"
                :prop="`${index}_compareType`"
                :rules="validatorCompareType(formItem, index)"
              >
                <tvt-select
                  :id="`${index}_compareType`"
                  v-model="formData.conditionList[index].compareType"
                  :popper-append-to-body="true"
                  style="width: 100px"
                  size="mini"
                >
                  <el-option
                    v-for="item in compareOption"
                    :key="`${index}_${item}`"
                    :label="item"
                    :value="item"
                  />
                </tvt-select>
              </el-form-item>
              <el-form-item
                :prop="`${index}_inputVal`"
                :rules="validatorInputVal(formItem, index)"
              >
                <!-- 集群名称需要特殊处理，使用下拉选项 选项来自dcinfo接口 -->
                <tvt-select
                  v-if="showConditionList[index].showClusterName"
                  :id="`${index}_cluster_inputVal`"
                  v-model="formData.conditionList[index].inputVal"
                  :popper-append-to-body="true"
                  style="width: 200px"
                  size="mini"
                  @change="(val) => hanldeInputVal(val, index)"
                >
                  <el-option
                    v-for="item in clusterOptions"
                    :key="`${index}_${item.value}`"
                    :label="item.label"
                    :value="item.value"
                  />
                </tvt-select>
                <!-- 否则走正常的判断，根据type等确定组件 -->
                <el-input-number
                  v-else-if="showConditionList[index].showInputNumber"
                  v-model.number="formData.conditionList[index].inputVal"
                  :precision="2"
                  :controls="false"
                  :placeholder="$t('pleaseEnter', { type: '' })"
                  clearable
                  style="width: 200px"
                  size="mini"
                />
                <tvt-select
                  v-else-if="showConditionList[index].showSelect"
                  :id="`${index}_inputVal`"
                  v-model="formData.conditionList[index].inputVal"
                  :popper-append-to-body="true"
                  style="width: 200px"
                  size="mini"
                  @change="(val) => hanldeInputVal(val, index)"
                >
                  <el-option
                    v-for="item in booleanOptionList[index]"
                    :key="
                      item && item.value
                        ? `${index}_${item.value}`
                        : `${index}_${item}`
                    "
                    :label="item && item.label ? item.label : item"
                    :value="item && item.value ? item.value : item"
                  />
                </tvt-select>
                <tvt-input
                  v-else
                  v-model="formData.conditionList[index].inputVal"
                  :placeholder="$t('pleaseEnter', { type: '' })"
                  clearable
                  style="width: 200px"
                  size="mini"
                />
              </el-form-item>
            </div>
            <el-form-item
              v-if="formData.conditionList && formData.conditionList.length > 1"
            >
              <i
                class="el-icon-delete"
                style="margin-left: 20px; cursor: pointer"
                @click="(e) => handleDelete(e, index)"
              ></i>
            </el-form-item>
          </div>
        </div>
        <div
          v-if="formData.conditionList.length < filterOptions.length"
          class="form-add-icon"
        >
          <i
            class="el-icon-circle-plus-outline"
            style="font-size: 24px; cursor: pointer"
            @click="handleAdd()"
          ></i>
        </div>
      </el-form>
    </div>
    <div style="text-align: right; margin: 10px 0px 0px 0px">
      <el-button size="mini" @click="handleCancel"> 取消 </el-button>
      <el-button size="mini" @click="handleReset"> 重置 </el-button>
      <el-button type="primary" size="mini" @click="handleConfirm">
        确定
      </el-button>
    </div>
  </el-popover>
</template>
<script>
export default {
  name: 'PopoverSearch',
  components: {},
  props: {
    placement: {
      type: String,
      default: 'left',
    },
    options: {
      type: Array,
      default: () => [],
    },
    initSelect: {
      type: Array,
      default: () => [],
    },
    clusterOptions: {
      type: Array,
      default: () => [],
    },
    defaultProps: {
      type: Object,
      default: () => ({
        value: 'prop',
        label: 'label',
      }),
    },
  },
  data() {
    return {
      visible: false,
      compareOption: ['>', '<', '=', '>=', '<='],
      quotaMonitorObj: {}, // 监控指标的配置对象
      // 传输数据
      formData: {
        conditionList: [
          {
            activityType: '',
            compareType: '',
            inputVal: undefined,
          },
        ],
      },
      showConditionList: [
        {
          showCompareType: false,
          showClusterName: false,
          showSelect: false,
          showInputNumber: false,
        },
      ], // 每一行查询条件的显示效果
      booleanOptionList: [null], // 每一行的选择下拉框 包含success fail等
      // 规则
      rules: {
        activityType: [
          {
            required: true,
            message: this.$t('activityTypeTRequire'),
            trigger: 'blur',
          },
        ],
        compareType: [
          {
            required: true,
            message: this.$t('compareTypeRequire'),
            trigger: 'blur',
          },
        ],
        inputVal: [
          {
            required: true,
            message: this.$t('inputValRequire'),
            trigger: 'blur',
          },
        ],
      },
    }
  },
  computed: {
    filterOptions() {
      return this.options.filter((item) => item.fieldName !== 'ct')
    },
  },
  watch: {
    visible: {
      handler(val) {
        if (!val) {
          this.$refs.conditionForm && this.$refs.conditionForm.resetFields()
          // 关闭则恢复默认值
          this.formData = {
            conditionList: [
              {
                activityType: '',
                compareType: '',
                inputVal: '',
              },
            ],
          }
          this.showConditionList = [
            {
              showCompareType: false,
              showClusterName: false,
              showSelect: false,
              showInputNumber: false,
            },
          ]
          this.booleanOptionList = [null]
        } else {
          this.$refs.conditionForm && this.$refs.conditionForm.resetFields()
          this.initDate()
        }
      },
      immediate: true,
    },
    // 监听options，生成每个指标项的对应指标配置
    options: {
      handler(val) {
        if (val) {
          const quotaMonitorObj = {}
          // 遍历options，以字段为key,保存所有字段的类型等信息
          val.forEach((item) => {
            quotaMonitorObj[item.fieldName] = { ...item }
          })
          this.quotaMonitorObj = quotaMonitorObj
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {},
  methods: {
    initDate() {
      this.$nextTick(() => {
        const conditionList = JSON.parse(
          JSON.stringify(this.initSelect.slice())
        )
        this.formData = {
          conditionList,
        }
        const showConditionList = []
        const booleanOptionList = []
        // console.log('conditionList', conditionList)
        conditionList.forEach((item) => {
          const { activityType } = item
          const temp = {
            showCompareType: this.showCompareType(activityType) || false,
            showClusterName: this.showClusterName(activityType) || false,
            showSelect: this.showSelect(activityType) || false,
            showInputNumber: this.showInputNumber(activityType) || false,
          }
          showConditionList.push(temp)
          const selectObj = this.quotaMonitorObj[activityType] || {}
          if (selectObj.type === 'select' && selectObj.values) {
            // booleanOptionList.push(selectObj.values.split(','))
            if (selectObj.labels) {
              const labels = selectObj.labels.split(',')
              const values = selectObj.values.split(',')
              const boolTemp = labels.map((item, index) => {
                return {
                  label: item,
                  value: values[index],
                }
              })
              booleanOptionList.push(boolTemp)
            } else {
              booleanOptionList.push(selectObj.values.split(','))
            }
          } else {
            booleanOptionList.push(null)
          }
        })
        this.showConditionList = showConditionList
        this.booleanOptionList = booleanOptionList
      })
    },
    // 检验activityType
    validatorActivityType(formItem) {
      // 返回的是rules
      return [
        {
          required: true,
          validator: (rule, value, cb) => {
            const { activityType } = formItem
            if (!activityType) {
              cb(new Error(this.$t('activityTypeTRequire')))
            } else {
              cb()
            }
          },
          trigger: ['blur'],
        },
      ]
    },
    // 检验compareType
    validatorCompareType(formItem) {
      // 返回的是rules
      return [
        {
          required: true,
          validator: (rule, value, cb) => {
            const { compareType } = formItem
            // console.log('formItem', formItem, 'index', index, value)
            if (!compareType) {
              cb(new Error(this.$t('compareTypeRequire')))
            } else {
              cb()
            }
          },
          trigger: ['change'],
        },
      ]
    },
    // 校验inputVal
    validatorInputVal(formItem) {
      // 返回的是rules
      return [
        {
          required: true,
          validator: (rule, value, cb) => {
            const { inputVal } = formItem
            // console.log('formItem', formItem, 'index', index, value)
            if (
              inputVal === '' ||
              inputVal === undefined ||
              inputVal === null
            ) {
              cb(new Error(this.$t('inputValRequire')))
            } else {
              cb()
            }
          },
          trigger: ['blur'],
        },
      ]
    },
    showCompareType(activityType) {
      const selectObj = this.quotaMonitorObj[activityType] || {}
      // console.log('activityType', activityType, 'selectObj', selectObj)
      return selectObj.type && selectObj.type === 'number'
    },
    showClusterName(activityType) {
      const selectObj = this.quotaMonitorObj[activityType] || {}
      return selectObj.prop === 'clusterName'
    },
    showSelect(activityType) {
      const selectObj = this.quotaMonitorObj[activityType] || {}
      return selectObj.type && selectObj.type === 'select'
    },
    showInputNumber(activityType) {
      const selectObj = this.quotaMonitorObj[activityType] || {}
      return selectObj.type && selectObj.type === 'number'
    },
    // activityType变化
    hanldeActivityType(val, index) {
      // 对应条目的校验
      this.$refs.conditionForm &&
        this.$refs.conditionForm.validateField(`${index}_activityType`)
      const showConditionList = this.showConditionList.slice()
      const temp = {
        showCompareType: this.showCompareType(val) || false,
        showClusterName: this.showClusterName(val) || false,
        showSelect: this.showSelect(val) || false,
        showInputNumber: this.showInputNumber(val) || false,
      }
      showConditionList.splice(index, 1, temp)
      this.showConditionList = showConditionList
      const booleanOptionList = this.booleanOptionList.slice()
      const selectObj = this.quotaMonitorObj[val] || {}
      let boolTemp = null
      if (selectObj.type && selectObj.type === 'select') {
        // boolTemp = selectObj.values.split(',')
        if (selectObj.labels) {
          const labels = selectObj.labels.split(',')
          const values = selectObj.values.split(',')
          boolTemp = labels.map((item, index) => {
            return {
              label: item,
              value: values[index],
            }
          })
        } else {
          boolTemp = selectObj.values.split(',')
        }
      }
      booleanOptionList.splice(index, 1, boolTemp)
      this.booleanOptionList = booleanOptionList
      const conditionList = this.formData.conditionList.slice()
      const conTmep = {
        activityType: val,
        compareType: '',
        inputVal: temp.showInputNumber ? undefined : '',
      }
      conditionList.splice(index, 1, conTmep)
      this.formData.conditionList = conditionList
    },
    // InputVal选择变化
    hanldeInputVal(val, index) {
      // 对应条目的校验
      this.$refs.conditionForm &&
        this.$refs.conditionForm.validateField(`${index}_inputVal`)
    },
    handleAdd() {
      const conditionList = this.formData.conditionList.slice()
      conditionList.push({
        activityType: '',
        compareType: '',
        inputVal: '',
      })
      this.formData = {
        conditionList,
      }
      const showConditionList = this.showConditionList.slice()
      showConditionList.push({
        showCompareType: false,
        showClusterName: false,
        showSelect: false,
        showInputNumber: false,
      })
      this.showConditionList = showConditionList
      const booleanOptionList = this.booleanOptionList.slice()
      booleanOptionList.push(null)
      this.booleanOptionList = booleanOptionList
    },
    handleDelete(e, index) {
      e.stopPropagation()
      // 去掉对应条目的校验
      this.$refs.conditionForm &&
        this.$refs.conditionForm.clearValidate([
          `${index}_activityType`,
          `${index}_compareType`,
          `${index}_inputVal`,
        ])
      // this.$refs.conditionForm && this.$refs.conditionForm.resetFields()
      this.deleteIndex = index
      this.$nextTick(() => {
        const conditionList = this.formData.conditionList.slice()
        conditionList.splice(index, 1)
        this.formData = {
          conditionList,
        }
        const showConditionList = this.showConditionList.slice()
        showConditionList.splice(index, 1)
        this.showConditionList = showConditionList
        const booleanOptionList = this.booleanOptionList.slice()
        booleanOptionList.splice(index, 1)
        this.booleanOptionList = booleanOptionList
        // this.formData.conditionList.splice(index, 1)
        // this.showConditionList.splice(index, 1)
      })
    },
    handleCancel() {
      this.visible = false
    },
    handleReset() {
      this.visible = false
      const conditionList = [
        {
          activityType: '',
          compareType: '',
          inputVal: '',
        },
      ]
      this.formData = {
        conditionList,
      }
      this.showConditionList = [
        {
          showCompareType: false,
          showClusterName: false,
          showSelect: false,
          showInputNumber: false,
        },
      ]
      this.booleanOptionList = [null]
      this.$emit('handleSelect', conditionList)
    },
    handleConfirm() {
      this.$refs.conditionForm.validate((valid) => {
        if (valid) {
          const { conditionList } = this.formData
          this.$emit('handleSelect', conditionList.slice())
          this.visible = false
        } else {
          return false
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.el-icon-filter:hover {
  color: #409eff;
}
.form-list-wrapper {
  width: 100%;
  max-height: 400px;
  overflow-y: auto;
  .form-line-item {
    width: 540px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .form-operate-wrapper {
      width: 510px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
.form-add-icon {
  width: 510px;
  display: flex;
  justify-content: center;
}
</style>
<style lang="scss">
.popover-search {
  .el-input {
    display: block;
    // width: 150px !important;
    border-radius: 16px !important;
  }
  .el-select {
    display: block;
    border-radius: 16px !important;
  }
  .el-form-item__content {
    line-height: normal !important;
  }
}
</style>
