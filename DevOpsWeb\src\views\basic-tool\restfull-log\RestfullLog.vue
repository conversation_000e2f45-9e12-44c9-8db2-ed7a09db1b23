<template>
  <!-- Restfull日志 -->
  <div class="page-wrapper">
    <div class="search-wrapper">
      <tvt-select
        v-model="filterCond.projectName"
        :options="microTypeOptions"
        :mockplaceholder="$t('microLogTab.microSelect')"
        :clearable="false"
        style="width: 250px; margin-right: 20px"
        @change="getApiLogList(1)"
      />
      <tvt-date-picker
        v-model="filterCond.dayTime"
        align="right"
        type="date"
        :placeholder="$t('microLogTab.dayTimeSelect')"
        :clearable="false"
        style="width: 250px; margin-right: 20px"
        :picker-options="pickerOptions"
        @change="handleDate"
      ></tvt-date-picker>
      <el-time-picker
        v-model="filterCond.timeRange"
        is-range
        :clearable="false"
        :arrow-control="arrowControl"
        :range-separator="$t('alarmHistoryTab.rangeSeparator')"
        :start-placeholder="$t('restfullLogTab.startTime')"
        :end-placeholder="$t('restfullLogTab.endTime')"
        placeholder="选择时间范围"
        style="width: 250px; margin-right: 20px"
        @change="handleTime"
      ></el-time-picker>
      <tvt-select
        v-model="filterCond.hostname"
        :options="hostnameOptions"
        :mockplaceholder="$t('restfullLogTab.hostnameSelect')"
        clearable
        style="width: 250px; margin-right: 20px"
        @change="getApiLogList(1)"
      />
      <tvt-select
        v-model="filterCond.success"
        :options="statusOptions"
        :mockplaceholder="$t('restfullLogTab.reqStatusSelect')"
        clearable
        style="width: 250px; margin-right: 20px"
        @change="getApiLogList(1)"
      />
      <search-input
        v-model.trim="filterCond.username"
        :placeholder="$t('login.userNamePHD')"
        clearable
        size="small"
        maxlength="32"
        style="width: 250px; margin-right: 20px"
        @change="getApiLogList(1)"
      />
      <search-input
        v-model.trim="filterCond.requestPath"
        :placeholder="$t('restfullLogTab.requestPathInput')"
        clearable
        size="small"
        maxlength="32"
        style="width: 250px; margin-right: 20px"
        @change="getApiLogList(1)"
      />
      <search-input
        v-model.trim="filterCond.tid"
        :placeholder="$t('restfullLogTab.tidInput')"
        clearable
        size="small"
        maxlength="32"
        style="width: 250px; margin-right: 20px"
        @change="getApiLogList(1)"
      />
      <div class="search-btn-box">
        <el-button type="primary" size="small" round @click="handleReset">{{
          $t('reset')
        }}</el-button>
        <el-button type="primary" size="small" round @click="handleRefresh">{{
          $t('refresh')
        }}</el-button>
      </div>
      <div class="search-left-wrapper"></div>
    </div>
    <!-- Restfull日志表格 -->
    <div class="table-wrapper">
      <tvt-table
        ref="myTable"
        v-myLoading="loading"
        :data="restfullLogList"
        :columns="restfullLogColumn"
        :border="true"
        :border-bottom="true"
        :pagination="{
          total,
          current: filterCond.current,
          size: filterCond.size,
          'page-sizes': $tablePageSizes,
          background: true,
        }"
        @onFetchData="getApiLogList"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="['startTime', 'endTime'].includes(column.prop)">
            <span>{{ stampToStrLongMethod(row[column.prop]) }}</span>
          </template>
          <template v-if="['requestBody', 'response'].includes(column.prop)">
            <el-tooltip
              class="item"
              effect="dark"
              :content="row[column.prop]"
              placement="left-start"
            >
              <span class="tooltip-ellipsis-box">{{ row[column.prop] }}</span>
            </el-tooltip>
          </template>
          <template v-if="column.prop === 'platform'">
            <span>{{ platformNameObj[row[column.prop]] }}</span>
          </template>
        </template>
      </tvt-table>
    </div>
  </div>
</template>
<script>
import { debounce, stampToStrLong } from '@/utils/common'
import {
  getServiceList,
  getApiLogList,
  getHostNameList,
} from '@/api/basicTool.js'
export default {
  name: 'RestfullLog',
  components: {},
  data() {
    return {
      total: 0,
      filterCond: {
        current: 1,
        size: 20,
        projectName: '',
        username: '',
        hostname: '',
        requestPath: '',
        dayTime: new Date(),
        timeRange: [
          // 最近一小时的时间范围
          new Date(new Date().getTime() - 3600 * 1000),
          new Date(),
        ],
        success: '',
        tid: '',
      },
      loading: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: this.$t('today'),
            onClick(picker) {
              picker.$emit('pick', new Date())
            },
          },
          {
            text: this.$t('yesterday'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24)
              picker.$emit('pick', date)
            },
          },
          {
            text: this.$t('weekAgo'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', date)
            },
          },
        ],
      },
      arrowControl: false,
      microTypeOptions: [],
      hostnameOptions: [],
      restfullLogList: [],
      restfullLogColumn: [
        {
          label: this.$t('microServeTab.projectName'),
          prop: 'projectName',
          width: 240,
        },
        {
          label: this.$t('microServeTab.instanceId'),
          prop: 'instanceId',
          width: 100,
        },
        {
          label: this.$t('restfullLogTab.requestPath'),
          prop: 'requestPath',
          width: 200,
        },
        {
          label: 'tid',
          prop: 'tid',
          width: 180,
        },
        {
          label: this.$t('restfullLogTab.username'),
          prop: 'username',
          width: 200,
        },
        {
          label: this.$t('hostTab.hostName'),
          prop: 'hostname',
          width: 150,
        },
        {
          label: this.$t('restfullLogTab.platform'),
          prop: 'platform',
          slotName: 'bodyCell',
          width: 100,
        },
        {
          label: this.$t('restfullLogTab.coId'),
          prop: 'coId',
          width: 200,
        },
        {
          label: this.$t('restfullLogTab.startTime'),
          prop: 'startTime',
          slotName: 'bodyCell',
          width: 160,
        },
        {
          label: this.$t('restfullLogTab.endTime'),
          prop: 'endTime',
          slotName: 'bodyCell',
          width: 160,
        },
        {
          label: this.$t('restfullLogTab.requestCost') + '(ms)',
          prop: 'cost',
          width: 160,
        },
        {
          label: this.$t('restfullLogTab.callClient'),
          prop: 'clientType',
          width: 100,
        },
        {
          label: this.$t('restfullLogTab.clientIp'),
          prop: 'clientIp',
          width: 100,
        },
        {
          label: this.$t('restfullLogTab.responseCode'),
          prop: 'code',
          width: 140,
        },
        {
          label: this.$t('restfullLogTab.errorMsg'),
          prop: 'errorMsg',
          width: 120,
        },
        {
          label: this.$t('restfullLogTab.reqParams'),
          prop: 'requestBody',
          slotName: 'bodyCell',
          minWidth: 300,
        },
        {
          label: this.$t('restfullLogTab.resParams'),
          prop: 'response',
          slotName: 'bodyCell',
          minWidth: 300,
        },
      ],
      platformNameObj: {
        10: 'TVT',
        20: this.$t('restfullLogTab.partner'),
        30: this.$t('restfullLogTab.installer'),
        40: 'ToB',
        50: 'ToC',
      },
      statusOptions: [
        { label: this.$t('restfullLogTab.reqSuccess'), value: 'true' },
        { label: this.$t('restfullLogTab.reqFail'), value: 'false' },
      ],
    }
  },
  async mounted() {
    await this.getServiceList()
    this.getHostNameList()
    this.getApiLogList()
  },
  methods: {
    stampToStrLongMethod(time) {
      let intTime = parseInt(time) //接口给的字符串时间
      return stampToStrLong(intTime)
    },
    // 获取微服务列表
    async getServiceList() {
      try {
        // 1:用于微服务日志查询 2:用于resultful接口日志查询
        const { data = [] } = await getServiceList(2)
        this.microTypeOptions = data.map((item) => ({
          label: item,
          value: item,
        }))
        // 微服务列表默认选中第一个
        this.filterCond.projectName = data[0]
      } catch (error) {
        console.error(error)
      }
    },
    // 获取主机名下拉选项
    async getHostNameList() {
      try {
        const { data = [] } = await getHostNameList(2) // 1:用于微服务日志查询 2:用于resultful接口日志查询
        this.hostnameOptions = data.map((item) => ({
          label: item.hostName,
          value: item.hostName,
        }))
      } catch (error) {
        console.error(error)
      }
    },
    handleDate(val) {
      this.filterCond.dayTime = val
      this.getApiLogList()
    },
    handleTime(val) {
      console.log('time', val)
      this.filterCond.timeRange = val
      this.getApiLogList()
    },
    getApiLogList: debounce(async function (pageSize) {
      this.loading = true
      try {
        if (pageSize == 1) {
          this.filterCond.current = 1
        } else if (pageSize && pageSize.current) {
          this.filterCond.current = pageSize.current
          this.filterCond.size = pageSize.size
        }
        const {
          current,
          size,
          projectName,
          username,
          hostname,
          requestPath,
          timeRange,
          dayTime,
          success,
          tid,
        } = this.filterCond
        const params = { pageNum: current, pageSize: size }
        if (projectName) {
          params.projectName = projectName
        }
        if (username) {
          params.username = username.trim()
        }
        if (hostname) {
          params.hostname = hostname.trim()
        }
        if (requestPath) {
          params.requestPath = requestPath.trim()
        }
        if (dayTime) {
          params.dayTime = window.moment(dayTime).format('yyyy-MM-DD')
        }
        if (timeRange.length) {
          const date = window.moment(dayTime).format('yyyy-MM-DD')
          const startTime = window.moment(timeRange[0]).format('HH:mm:ss')
          const endTime = window.moment(timeRange[1]).format('HH:mm:ss')
          params.startTime = window.moment(`${date} ${startTime}`).valueOf()
          // params.stratTimeTmp = window
          //   .moment(`${date} ${startTime}`)
          //   .format('yyyy-MM-DD HH:mm:ss')
          params.endTime = window.moment(`${date} ${endTime}`).valueOf()
          // params.endTimeTmp = window
          //   .moment(`${date} ${endTime}`)
          //   .format('yyyy-MM-DD HH:mm:ss')
        }
        if (success) {
          params.success = success === 'true'
        }
        if (tid) {
          params.tid = tid
        }
        const { data = {} } = await getApiLogList(params)
        const { records = [], total = 0 } = data
        this.restfullLogList = records.slice()
        this.total = Number(total)
        this.loading = false
      } catch (error) {
        console.error(error)
        this.loading = false
      }
    }, 200),
    handleReset() {
      // 条件重置
      this.filterCond = {
        ...this.filterCond,
        current: 1,
        pageSize: 20,
        projectName: this.microTypeOptions[0].value,
        username: '',
        hostname: '',
        requestPath: '',
        dayTime: new Date(),
        timeRange: [
          // 最近一小时的时间范围
          new Date(new Date().getTime() - 3600 * 1000),
          new Date(),
        ],
        success: '',
        tid: '',
      }
      this.$nextTick(() => {
        this.getApiLogList(1)
      })
    },
    handleRefresh() {
      this.getApiLogList(1)
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .tooltip-ellipsis-box {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
