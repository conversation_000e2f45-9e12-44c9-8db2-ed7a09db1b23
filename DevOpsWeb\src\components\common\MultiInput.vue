<!-- 用于vms 分享人 isOutlined ? 'isOutlined' : '', -->
<template>
  <div
    :id="id"
    v-clickoutside="handleBlurDiv"
    class="tvt-input multi-input"
    :class="[
      isFocus ? 'isFocus' : '',
      size ? `tvt-input-${size}` : '',
      !isFocus && !inputValue && !len ? '' : 'isOutlined',
    ]"
    @click="clickMultilInput"
  >
    <label ref="labelRef" class="label">
      <div
        :class="[
          'label-text-wrapper-multi',
          size ? `label-text-wrapper-${size}` : '',
        ]"
      >
        <span
          v-if="inputType == 2"
          :class="[
            'label-text',
            required && !(!isFocus && !inputValue && !len) ? 'required' : '',
          ]"
          >{{ !isFocus && !inputValue && !len ? mockplaceholder : label }}</span
        >
        <span
          v-else
          :class="[
            'label-text',
            required && !(!isFocus && !inputValue && !len) ? 'required' : '',
          ]"
          >{{ label }}</span
        >
      </div>
      <div ref="tagContainer" class="input-tag">
        <span
          v-for="(tag, index) in realDynamicTags"
          :key="tag + index"
          ref="tagElements"
          class="tag-item"
          :class="{ 'show-plus': showPlus }"
        >
          <el-tag
            class="multi-input-tag"
            closable
            disable-transitions
            @close.stop.prevent="handleClose(tag, index)"
          >
            <span class="multi-input-tag-text" :title="tag">{{ tag }}</span>
          </el-tag>
        </span>
        <div v-if="showPlus" class="number" @click.stop="changeShow">
          +{{ len - limitLen }}
        </div>
      </div>
      <el-input
        ref="saveTagInput"
        v-model="inputValue"
        class="input-new-tag"
        :class="{
          active: isFocus || (!isFocus && !dynamicTags.length),
        }"
        :clearable="false"
        :size="size"
        :type="type"
        :maxlength="tagMaxLength"
        @focus="handleFocus"
        @keyup.enter.native="handleInputConfirm"
        @blur="handleBlur"
        @keyup.188.native="handleCommaConfirm"
      >
      </el-input>
    </label>
  </div>
</template>

<script>
import { deepCopy } from '@/utils/common.js'
export default {
  name: 'MultiInput',
  components: {},
  props: {
    id: {
      // 属性唯一ID
      type: String,
      default: '',
    },
    inputType: {
      type: Number, // 1, 2
      default: 1,
    },
    mockplaceholder: {
      type: String,
      default: '',
    },
    label: {
      type: String,
      default: '',
    },
    size: {
      type: String,
      default: '',
    },
    value: {
      type: Array,
      default: () => [],
    },
    required: {
      type: Boolean,
      default: false,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: '',
    },
    length: {
      type: Number,
      default: 10,
    },
    hasLimit: {
      type: Boolean,
      default: true,
    },
    tagMaxLength: {
      type: String,
      default: '50',
    },
    commaConfirm: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 输入框聚焦
      isFocus: false,
      // label样式
      isOutlined: false,
      type: (this.$attrs || { type: 'text' }).type,
      dynamicTags: [],
      inputVisible: false,
      inputValue: '',
      emailError: false,
      errorTip: '',
      showAll: false,
      realDynamicTags: [],
      limitLen: 1,
      rowValue: [],
    }
  },
  computed: {
    len() {
      return this.dynamicTags.length
    },
    showTags() {
      return { dynamicTags: this.dynamicTags, showAll: this.showAll }
    },
    showPlus() {
      return this.hasLimit && !this.showAll && this.len > this.limitLen
    },
  },
  watch: {
    dynamicTags: {
      handler(val) {
        this.$emit('change', val)
        this.realDynamicTags = deepCopy(this.dynamicTags)
      },
      immediate: true,
      deep: true,
    },
    showTags: {
      handler(val) {
        if (val.showAll || !this.hasLimit) {
          this.realDynamicTags = this.dynamicTags
        } else {
          this.$nextTick(() => {
            // limitLen是一行可以显示标签的个数
            const containerWidth = this.$refs.tagContainer.offsetWidth - 30
            let totalWidth = 0,
              tagWidths = []
            if (this.$refs.tagElements) {
              tagWidths = Array.from(this.$refs.tagElements).map(
                (el) => el.offsetWidth
              )
            }
            this.limitLen = 1 //至少显示一个
            for (let i = 0; i < tagWidths.length; i++) {
              let tagWidth = tagWidths[i]
              if (totalWidth + tagWidth + 32 <= containerWidth) {
                totalWidth += tagWidth
                this.limitLen = i + 1
              } else {
                break
              }
            }
            this.realDynamicTags = this.dynamicTags.slice(0, this.limitLen)
          })
        }
      },
      immediate: true,
      deep: true,
    },
    visible: {
      handler(val) {
        if (!val) {
          this.close()
        }
      },
      immediate: true,
    },
    value: {
      handler(val) {
        if (!val || val.length == 0) {
          if (this.label && !this.isFocus) this.isOutlined = false
          if (JSON.stringify(val) !== JSON.stringify(this.rowValue)) {
            this.rowValue = val.slice()
            this.dynamicTags = val.slice()
          }
        } else {
          // console.log('value变化', val)
          this.isOutlined = !!this.label
          if (JSON.stringify(val) !== JSON.stringify(this.rowValue)) {
            this.rowValue = val
            if (val && val.length) {
              this.dynamicTags = val.slice()
            }
          }
        }
      },
      immediate: true,
    },
    isFocus: {
      handler(val) {
        this.isOutlined = this.label ? (this.value.length ? true : val) : false
        if (!val) {
          this.showAll = false
        } else {
          this.showAll = true
        }
      },
      immediate: true,
    },
  },
  methods: {
    changeShow() {
      this.showAll = true
      console.log('this.isFocus', this.isFocus)
    },
    handleClose(tag, index) {
      this.dynamicTags.splice(index, 1)
    },
    handleCommaConfirm() {
      if (this.commaConfirm) {
        this.inputValue = this.inputValue.replaceAll(/[,，]/g, '')
        this.handleInputConfirm()
      }
    },
    handleInputConfirm() {
      let inputValue = this.inputValue.trim()
      if (!inputValue) {
        return
      }
      if (this.length && this.dynamicTags.length >= this.length) {
        this.inputValue = ''
        return
      }
      if (inputValue) {
        this.dynamicTags.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
    },
    handleFocus(e) {
      this.isFocus = true
      this.$emit('focus', e)
    },
    handleBlur(e) {
      this.handleInputConfirm()
      //this.isFocus = false
      this.$emit('blur', e, this.dynamicTags.slice())
    },
    // 点div外面才算失焦
    handleBlurDiv() {
      this.isFocus = false
    },
    close() {
      this.dynamicTags = []
      this.inputVisible = false
      this.inputValue = ''
      this.emailError = false
      this.errorTip = ''
    },
    clickMultilInput() {
      this.$refs.labelRef.click()
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-tag {
  // margin: 5px 3px 5px 3px;
  border-radius: 9999px;
  word-wrap: break-word;
  white-space: normal;
  height: unset !important;
  border: 0px;
  line-height: 24px;
  background: #eee;
  font-size: 12px;
  border-color: #eee;
  color: #181818;
  background-color: #eee;
  .el-tag__close {
    color: #595959;
    font-size: 14px;
    padding: 2px;
    top: 0px;
    &:hover {
      color: #fff;
      background-color: #409eff;
    }
  }
}
.tag-item + .tag-item {
  ::v-deep .el-tag {
    margin-top: 3px;
  }
}

.input-tag {
  line-height: 32px;
  padding: 0 3px;
  font-size: 0px;
  .tag-item {
    max-width: 100%;
    &.show-plus {
      max-width: calc(100% - 40px);
    }
    display: inline-block;
    font-size: 12px;
    cursor: pointer;
  }
  .number {
    font-size: 12px;
    margin: 0px 3px;
    display: inline-block;
    width: 32px;
    height: 24px;
    background: #eee;
    border-radius: 12px;
    line-height: 24px;
    text-align: center;
    cursor: pointer;
    color: #181818;
    z-index: 9999;
  }
}
.input-new-tag:not(.active) {
  width: 100%;
  vertical-align: bottom;
  height: 0;
  position: absolute;
  top: 0;
  left: 0;
  ::v-deep .el-input__inner {
    height: 0;
    position: relative;
    z-index: -1;
  }
}
.multi-input {
  cursor: text;
  position: relative;
  margin-bottom: 4px;
  .email-tip {
    position: absolute;
    color: #f56c6c;
    font-size: 12px;
    bottom: -32px;
    left: 0;
  }
}
.label {
  .label-text {
    position: absolute;
    top: 16px !important;
    left: 8px !important;
  }
}

.isFocus .label {
  position: absolute;
  z-index: 99999;
  background: white;
}

.multi-input .el-tag {
  height: 26px;
  line-height: 26px;
}

.multi-input.isOutlined {
  height: 32px;
  line-height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 16px;
}

.multi-input.isFocus {
  border: none;
}

.multi-input.isFocus.isOutlined {
  box-shadow: none;
}

.multi-input.isOutlined .label-text::before {
  display: none;
}

.multi-input .label .label-text {
  transition: none;
  top: 16px !important;
}

.multi-input-tag {
  max-width: calc(100%);
  display: inline-flex;
  align-items: center;
}
.multi-input-tag .multi-input-tag-text {
  display: inline-block;
  width: calc(100% - 20px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
