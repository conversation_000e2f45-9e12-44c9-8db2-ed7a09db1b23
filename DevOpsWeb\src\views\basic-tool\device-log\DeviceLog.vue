<template>
  <!-- 设备日志 -->
  <div class="page-wrapper">
    <div class="search-wrapper">
      <search-input
        v-model="filterCond.logId"
        :placeholder="$t('appLogTab.logIDInput')"
        clearable
        size="small"
        maxlength="32"
        style="width: 250px; margin-right: 20px"
        @change="handleRefresh"
      />
      <search-input
        v-model="filterCond.sn"
        :placeholder="$t('appLogTab.deviceSnInput')"
        clearable
        size="small"
        maxlength="32"
        style="width: 250px; margin-right: 20px"
        @change="handleRefresh"
      />
      <div class="search-btn-box">
        <el-button type="primary" size="small" round @click="handleReset">{{
          $t('reset')
        }}</el-button>
        <el-button type="primary" size="small" round @click="handleRefresh">{{
          $t('refresh')
        }}</el-button>
      </div>
      <div class="search-left-wrapper"></div>
    </div>
    <!-- 设备日志表格 -->
    <div class="table-wrapper">
      <tvt-table
        ref="myTable"
        v-myLoading="loading"
        :data="deviceLogList"
        :columns="deviceLogColumn"
        :border="true"
        :border-bottom="true"
        :pagination="{
          total: deviceLogTotal,
          current: filterCond.current,
          size: filterCond.size,
          'page-sizes': $tablePageSizes,
          background: true,
        }"
        @onFetchData="getDeviceLogList"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.prop === 'time'">
            <span>{{ stampToStrLongMethod(row[column.prop]) }}</span>
          </template>
          <template v-if="column.prop === 'downloadLog'">
            <span class="btn-text" @click="downloadFile(row)">{{
              $t('download')
            }}</span>
          </template>
        </template>
      </tvt-table>
    </div>
  </div>
</template>
<script>
import { debounce, stampToStrLong, hanldeDownload } from '@/utils/common'
import { getDeviceLogList, downloadDevLog } from '@/api/basicTool'
export default {
  name: 'DeviceLog',
  components: {},
  data() {
    return {
      defaultTimeRange: [],
      filterCond: {
        current: 1,
        size: 20,
        logId: '',
        sn: '',
      },
      loading: false,
      deviceLogTotal: 0,
      deviceLogList: [],
      deviceLogColumn: [
        {
          label: this.$t('appLogTab.logid'),
          prop: 'logId',
          minWidth: 180,
        },
        {
          label: this.$t('appLogTab.sn'),
          prop: 'sn',
          minWidth: 150,
        },
        {
          label: this.$t('appLogTab.time'),
          prop: 'time',
          slotName: 'bodyCell',
          width: 200,
        },
        {
          label: this.$t('appLogTab.downloadLog'),
          prop: 'downloadLog',
          slotName: 'bodyCell',
          fixed: 'right',
          width: 120,
          hide: !this.$isAuthorityExist(['devops_tool_devlog_mgr']),
        },
      ],
      downloadSet: new Set(), // 记录下载的日志
    }
  },
  async mounted() {},
  methods: {
    stampToStrLongMethod(time) {
      let intTime = parseInt(time) //接口给的字符串时间
      return stampToStrLong(intTime)
    },
    getDeviceLogList: debounce(async function (pageSize) {
      try {
        if (pageSize == 1) {
          this.filterCond.current = 1
        } else if (pageSize && pageSize.current) {
          this.filterCond.current = pageSize.current
          this.filterCond.size = pageSize.size
        }
        const { current, size, logId, sn } = this.filterCond
        this.loading = true
        const params = { pageNum: current, pageSize: size }
        if (logId) {
          params.logId = logId
        }
        if (sn) {
          params.sn = sn
        }
        const { data } = await getDeviceLogList(params)
        if (data) {
          this.deviceLogList = data.records
          this.deviceLogTotal = parseInt(data.total)
        } else {
          this.deviceLogList = []
          this.deviceLogTotal = 0
        }
        this.loading = false
      } catch (error) {
        console.error(error)
        this.loading = false
      }
    }, 500),
    handleReset() {
      // 条件重置
      this.filterCond = {
        ...this.filterCond,
        logId: '',
        sn: '',
        current: 1,
        size: 20,
      }
      this.deviceLogList = []
      this.deviceLogTotal = 0
      // this.$nextTick(() => {
      //   this.getdeviceLogList()
      // })
    },
    handleRefresh() {
      this.getDeviceLogList(1)
    },
    // 下载日志
    async downloadFile(row) {
      const { logPath } = row
      const params = {
        url: logPath,
        type: 2, // 1 OSS类 2 日志类
      }
      // 判断url是否在10s内点击过，如果点击过则提示频率太快
      if (this.downloadSet.has(logPath)) {
        this.$message.warning(this.$t('microLogTab.downloadFreq'))
        return
      }
      this.downloadSet.add(logPath)

      // 添加loading效果
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, .5)',
        text: this.$t('LoadingDownload'), // 或者使用 '下载中...'
      })

      try {
        const resBlob = await downloadDevLog(params)
        const pathArr = logPath.split('/')
        let reader = new FileReader()
        reader.onload = () => {
          hanldeDownload(resBlob, pathArr[pathArr.length - 1])
          // 定时器10s后放开下载限制
          setTimeout(() => {
            this.downloadSet.delete(logPath)
          }, 10000)
          loading.close()
        }
        reader.readAsText(resBlob)
      } catch (error) {
        console.error('下载文件失败:', error)
        this.downloadSet.delete(logPath)
        loading.close()
      }
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .tooltip-ellipsis-box {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
::v-deep .search-wrapper .kafka-mutli-input .el-input__inner {
  width: 300px;
}

::v-deep .search-wrapper .kafka-mutli-input.isFocus .label {
  border: 1px solid #dcdfe6;
  border-radius: 18px;
  width: 300px;
  box-sizing: border-box;
}

::v-deep .search-wrapper .kafka-mutli-input.isFocus .el-input__inner {
  border: 1px solid #429efd;
  box-sizing: border-box;
}
</style>
