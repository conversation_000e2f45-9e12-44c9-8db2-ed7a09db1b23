<template>
  <!-- 告警配置 -->
  <div class="alarm-history">
    <div class="search-wrapper">
      <div class="search-left-wrapper">
        <tvt-select
          v-model="filterCond.dcId"
          clearable
          :placeholder="$t('alarmHistoryTab.dcSelect')"
          :options="areaOptions"
          style="width: 250px; margin-right: 20px"
          @change="handleQry"
          @focus="handleFocus"
        />
        <tvt-select
          v-model="filterCond.monitorType"
          :options="ctyTypeOptions"
          :placeholder="$t('alarmConfigTab.alarmTypeSelect')"
          clearable
          style="margin-right: 20px"
          @change="handleCtyType"
          @focus="handleFocus"
        />
        <tvt-select
          v-model="filterCond.ctIndexField"
          :options="filterCtFieldOptions"
          :placeholder="$t('alarmConfigTab.ctIndexFieldSelect')"
          clearable
          style="margin-right: 20px"
          @change="handleQry"
          @focus="handleFocus"
        />
        <tu-search-time
          ref="tuSearchTimeRef"
          v-model="filterCond.timeRange"
          class="search-time"
          :default="defaultTimeRange"
          @change="handleQry"
        />
        <el-button type="primary" size="small" round @click="handleReset">{{
          $t('reset')
        }}</el-button>
        <el-button type="primary" size="small" round @click="handleRefresh">{{
          $t('refresh')
        }}</el-button>
      </div>
    </div>
    <!-- 告警配置表格 -->
    <div class="alarm-table-title">
      <popover-search
        :placement="'right'"
        :default-props="{ value: 'prop', label: 'label' }"
        :options="monitorFieldList"
        :init-select="initSelect"
        @handleSelect="handleSelect"
      >
        <template #referenceType>
          <el-button type="primary" size="small" round>
            {{ $t('search') }}
          </el-button>
        </template>
      </popover-search>
      <popover-checked
        :placement="'right'"
        :default-props="{ value: 'prop', label: 'label' }"
        :options="alarmHistoryColumns"
        :init-check="initCheck"
        @handleCheck="handleCheck"
      >
        <template #referenceType>
          <el-button type="primary" size="small" round>
            {{ $t('columnSetting') }}
          </el-button>
        </template>
      </popover-checked>
    </div>
    <div class="table-wrapper">
      <tvt-table
        ref="myTable"
        v-myLoading="loading"
        :data="alarmHistoryList"
        :columns="filterColumns"
        :border="true"
        :border-bottom="true"
        :pagination="{
          total: historyTotal,
          current: filterCond.current,
          size: filterCond.size,
          'page-sizes': $tablePageSizes,
          background: true,
        }"
        @onFetchData="getAlarmHistory"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.prop === 'monitorType'">
            <span>{{ dealMonitorType(row.monitorType) }}</span>
          </template>
          <template v-if="column.prop === 'ctIndexField'">
            <span>{{ row.ctIndexFieldDesc }}</span>
          </template>
          <template v-if="column.prop === 'st'">
            <span>{{
              stampToStrLongMethod(row.st ? parseInt(row.st) * 1000 : 0)
            }}</span>
          </template>
          <template v-if="column.prop === 'sendEmailNotice'">
            <span>{{ dealSendNotice(row.sendEmailNotice) }}</span>
          </template>
          <template v-if="column.prop === 'sendSmsNotice'">
            <span>{{ dealSendNotice(row.sendSmsNotice) }}</span>
          </template>
        </template>
      </tvt-table>
    </div>
  </div>
</template>
<script>
import { getMonitorHisList, getMonitorConfList } from '@/api/systemAlarm.js'
import { getDcInfoListApi, getFieldTypeApi } from '@/api/home.js'
import { getLocale } from '@/lang'
import { debounce, stampToStrLong, btnBlur } from '@/utils/common'
import TuSearchTime from '@/components/common/TuSearchTime.vue'
import PopoverChecked from '@/components/common/PopoverChecked.vue'
import PopoverSearch from '@/components/common/PopoverSearch.vue'
import { alarmHistoryColumns } from '@/views/system-alarm/config.js'
import selfLocaLStorage from '@/utils/selfLocalStorage'

const lang = getLocale() || 'zh-CN'

export default {
  name: 'AlarmHistory',
  components: {
    TuSearchTime,
    PopoverChecked,
    PopoverSearch,
  },
  data() {
    return {
      ctyTypeOptions: [], //告警类下拉框
      orderList: [],
      alarmHistoryList: [],
      loading: false,
      filterColumns: [],
      alarmHistoryColumns,
      historyTotal: 0,
      defaultTimeRange: [],
      ctIndexFieldOptions: [],
      filterCtFieldOptions: [],
      filterCond: {
        monitorType: 'All',
        ctIndexField: 'All',
        host: null,
        dcId: '',
        timeRange: [],
        current: 1,
        size: 20,
      },
      monitorTypeName: {},
      sendNoticeName: [
        this.$t('alarmHistoryTab.no'),
        this.$t('alarmHistoryTab.yes'),
      ],
      areaOptions: [], // DC树形选择
      initCheck: [],
      initSelect: [
        {
          activityType: '',
          compareType: '',
          inputVal: '',
        },
      ],
      searchType: 'alarm_history',
      monitorConfObj: {
        // 自定义特殊的指标--是否已发送告警
        ALARM_HISTORY_sendNotice: {
          alarmIndexType: `${this.$t('alarmHistoryTab.yes')},${this.$t(
            'alarmHistoryTab.no'
          )}`,
        },
        // 自定义特殊的指标--是否已发送邮件告警
        ALARM_HISTORY_sendEmailNotice: {
          alarmIndexType: `${this.$t('alarmHistoryTab.yes')},${this.$t(
            'alarmHistoryTab.no'
          )}`,
        },
        // 自定义特殊的指标--是否已发送短信告警
        ALARM_HISTORY_sendSmsNotice: {
          alarmIndexType: `${this.$t('alarmHistoryTab.yes')},${this.$t(
            'alarmHistoryTab.no'
          )}`,
        },
      },
      monitorFieldList: [], // 表格字段信息，存储表格中指标名称、指标类型、指标选项
    }
  },
  async created() {
    // await Promise.all()
    // 获取DC下拉
    await this.getAreaOptions()
    this.getMonitorConfList()
    this.getInitCheck()
    this.getFieldType()
  },
  mounted() {
    this.getAlarmHistory()
  },
  methods: {
    // 获取DC下拉选项
    async getAreaOptions() {
      let data = []
      if (this.dcInfoList) {
        // 有dcInfo信息，则不用请求
        data = this.dcInfoList.slice()
      } else {
        // 否则走请求逻辑
        const res = await getDcInfoListApi(getLocale())
        data = res.data.slice()
        // 更新Vuex中存储的dcInfo
        this.$store.commit('monitor/DC_INFO_LIST', data)
      }
      if (data.length > 0) {
        const areaOptions = data.map((item) => ({
          label: item.dcName,
          value: item.dcId,
        }))
        this.areaOptions = areaOptions
      } else {
        this.areaOptions = []
      }
    },
    // 获取告警配置列表，用作告警指标名称下拉
    async getMonitorConfList() {
      const data = {
        lang: getLocale(),
        dcCode: null,
        clusterCode: null,
        host: null,
      }
      const res = await getMonitorConfList(data)
      if (res.data && res.data.length > 0) {
        const ctyTypeOptions = [{ label: this.$t('all'), value: 'All' }]
        const ctTypeSet = new Set()
        const ctIndexFieldOptions = [{ label: this.$t('all'), value: 'All' }]
        res.data.forEach((item) => {
          if (!ctTypeSet.has(item.ctType)) {
            ctyTypeOptions.push({
              label: item.ctTypeDesc,
              value: item.ctType.toLowerCase(),
            })
            ctTypeSet.add(item.ctType)
          }
          ctIndexFieldOptions.push({
            label: item.alarmIndexDesc,
            value: item.alarmIndexKey,
            ctType: item.ctType.toLowerCase(),
          })
        })
        this.ctyTypeOptions = ctyTypeOptions
        this.monitorTypeName = ctyTypeOptions.reduce((pre, next) => {
          pre[next.value] = next.label
          return pre
        }, {})
        this.ctIndexFieldOptions = ctIndexFieldOptions
        this.filterCtFieldOptions = ctIndexFieldOptions
      }
    },
    // 获取表格对应字段的类型，表格搜索时展示字段筛选类型用
    async getFieldType() {
      try {
        const params = {
          ctType: 'monitor.alert',
          lang: getLocale(),
        }
        const res = await getFieldTypeApi(params)
        if (res.data && res.data.length > 0) {
          // 根据表格列将国际化等信息加入到字段列表中
          const fieldList = []
          this.alarmHistoryColumns.forEach((item) => {
            const { prop } = item
            const fieldItem = res.data.find((item2) => item2.fieldName === prop)
            if (fieldItem) {
              fieldList.push({
                ...fieldItem,
                value: fieldItem.fieldName,
                label: item.label,
              })
            }
          })
          // 搜索按钮的过滤条件中不包含告警指标名称
          this.monitorFieldList = fieldList.filter(
            (item) => item.fieldName !== 'ctIndexField'
          )
        } else {
          this.monitorFieldList = []
        }
      } catch (err) {
        console.error(err)
      }
    },
    handleReset: debounce(function (e) {
      // 重置
      this.filterCond = {
        monitorType: 'All',
        ctIndexField: 'All',
        host: null,
        dcId: '',
        timeRange: [],
        current: 1,
        size: 20,
      }
      this.filterCtFieldOptions = this.ctIndexFieldOptions.slice()
      this.getAlarmHistory(1)
      // 按钮失焦
      btnBlur(e)
    }, 500),
    handleRefresh: debounce(function (e) {
      this.getAlarmHistory(1)
      // 按钮失焦
      btnBlur(e)
    }),
    // 选择框获取焦点时
    handleFocus() {
      // 手动关闭时间弹窗
      if (this.$refs.tuSearchTimeRef) {
        this.$refs.tuSearchTimeRef.$refs.dateTimePicker.handleClose()
      }
    },
    handleCtyType(val) {
      if (val === 'All') {
        this.filterCtFieldOptions = this.ctIndexFieldOptions.slice()
        this.filterCond.ctIndexField = 'All'
        this.handleQry()
        return
      }
      const filterCtFieldOptions = this.ctIndexFieldOptions.filter(
        (item) => item.ctType === val
      )
      this.filterCtFieldOptions = [
        { label: this.$t('all'), value: 'All' },
        ...filterCtFieldOptions,
      ]
      this.filterCond.ctIndexField = 'All'
      this.handleQry()
    },
    handleQry() {
      this.initSelect = [
        {
          activityType: '',
          compareType: '',
          inputVal: '',
        },
      ]
      this.getAlarmHistory(1)
    },
    getAlarmHistory: debounce(async function (pageSize) {
      this.loading = true
      if (pageSize == 1) {
        this.filterCond.current = 1
      } else if (pageSize && pageSize.current) {
        this.filterCond.current = pageSize.current
        this.filterCond.size = pageSize.size
      }
      const {
        current,
        size,
        monitorType,
        ctIndexField,
        host,
        dcId,
        timeRange,
      } = this.filterCond
      const data = { pageNum: current, pageSize: size }
      if (monitorType && monitorType !== 'All') {
        data.monitorType = monitorType
      }
      if (ctIndexField && ctIndexField !== 'All') {
        data.ctIndexField = ctIndexField
      }
      if (host) {
        data.host = host.trim()
      }
      if (dcId) {
        data.dcId = dcId
      }
      if (timeRange.length) {
        data.startTime = timeRange[0]
        data.endTime = timeRange[1]
      }
      data.lang = lang
      this.initSelect.forEach((item) => {
        const { activityType, inputVal } = item
        if (activityType) {
          if (
            ['sendNotice', 'sendEmailNotice', 'sendSmsNotice'].includes(
              activityType
            )
          ) {
            data[activityType] = inputVal === 'yes' ? 1 : 0
          } else {
            data[activityType] = inputVal
          }
        }
      })
      return getMonitorHisList(data)
        .then((res) => {
          this.loading = false
          if (res.basic && res.basic.code == 200) {
            if (res.data) {
              this.alarmHistoryList = res.data.records
              this.historyTotal = parseInt(res.data.total)
            } else {
              this.alarmHistoryList = []
              this.historyTotal = 0
            }
          }
        })
        .catch((err) => {
          this.loading = false
          console.log(err)
        })
    }, 500),
    stampToStrLongMethod(time) {
      if (!time) {
        return ''
      }
      const intTime = parseInt(time) //接口给的字符串时间
      return stampToStrLong(intTime)
    },
    dealMonitorType(monitorType) {
      return this.monitorTypeName[monitorType] || ''
    },
    dealSendNotice(sendNotice) {
      return this.sendNoticeName[sendNotice] || ''
    },
    handleSelect(val) {
      this.initSelect = val
      this.$nextTick(() => {
        this.getAlarmHistory(1)
      })
    },
    // 初始化列选择
    getInitCheck() {
      let showColumns = this.alarmHistoryColumns.map((item) => item.prop)
      showColumns =
        selfLocaLStorage.getItem('alarmHistoryColumns') || showColumns
      this.initCheck = showColumns
      const filterColumns = this.alarmHistoryColumns.filter((item) =>
        showColumns.includes(item.prop)
      )
      this.filterColumns = filterColumns
    },
    // 列设置勾选
    handleCheck(val) {
      this.initCheck = val
      const preColumns = this.alarmHistoryColumns.filter(
        (item) => item.unCheckAble
      )
      const checkedSet = new Set(val)
      const quotaColumns = this.alarmHistoryColumns.filter(
        (item) => !item.unCheckAble && checkedSet.has(item.prop)
      )
      this.filterColumns = [...preColumns, ...quotaColumns]
      this.tableResize()
      // 把当前勾选的列保存到localStprage中  {searchType: columns}
      selfLocaLStorage.setItem('alarmHistoryColumns', val)
    },
    tableResize() {
      if (this.$refs.myTable && this.$refs.myTable.$refs.tvtTableElTableRef) {
        this.$nextTick(() => {
          this.$refs.myTable.$refs.tvtTableElTableRef.doLayout()
        })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.alarm-history {
  margin: 24px;
  width: calc(100% - 48px);
  .search-wrapper {
    width: 100%;
    margin: 20px 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    .search-left-wrapper {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
    }
    .search-right-wrapper {
      width: max-content;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
    .search-time {
      margin-right: 20px;
    }
  }
  .alarm-table-title {
    width: 100%;
    height: 50px;
    line-height: 30px;
    font-size: 24px;
    margin: 0px;
    padding-top: 5px;
    min-width: 100px;
    overflow-x: hidden;
    span {
      margin-right: 12px;
    }
  }
  .btn-text {
    color: #429efd !important;
    cursor: pointer;

    + .btn-text {
      margin-left: 20px;
    }
  }

  .pre-detail {
    line-height: 24px;
    white-space: pre-line;
  }

  .no-detail {
    width: 600px;
    text-align: center;
  }
}
</style>
<style>
.alarm-history .table-wrapper .el-table__body .cell {
  white-space: pre-line;
}
.alarm-history .search-wrapper .el-date-editor .el-range-separator {
  display: inline-flex;
  align-items: center;
}
.alarm-history .search-wrapper .el-date-editor .el-range__icon {
  line-height: 28px;
}

.alarm-history .search-wrapper .el-date-editor .el-range__close-icon {
  line-height: 28px;
}
</style>
