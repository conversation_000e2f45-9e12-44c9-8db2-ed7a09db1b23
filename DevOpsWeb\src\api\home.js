import request from '@/api/request'
const baseUrl = '/dev-ops'

/*
首页
*/

// 获取所有DC和集群列表
export const getDcInfoListApi = (data) =>
  request.post(`${baseUrl}/monitor/dc-list`, data)

// 获取监控指标数据
export const getMonitorDataListApi = (data) =>
  request.post(`${baseUrl}/monitor/data-list`, data)

// 获取监控指标表格数据
export const getMonitorTableApi = (data) =>
  request.post(`${baseUrl}/monitor/page`, data)

// 获取监控指标数据--柱状图
export const getMonitorBarDataApi = (data) =>
  request.post(`${baseUrl}/monitor/bar-list`, data)

// 获取多个集群指标数据
export const getAllMonitorDataApi = (data) =>
  request.post(`${baseUrl}/monitor/data-all-list`, data)

// 获取指标中慢查询的日志--mysql和mongo中有
export const getSlowQueryDataApi = (data) =>
  request.post(`${baseUrl}/monitor/slow-query-list`, data)

// 获取websocket指标中断开次数的日志
export const getDisconnectsDataApi = (data) =>
  request.post(`${baseUrl}/monitor/error-list`, data)

// 获取监控告警表格字段类型
export const getFieldTypeApi = (data) =>
  request.post(`${baseUrl}/monitor/search-field-list`, data)
