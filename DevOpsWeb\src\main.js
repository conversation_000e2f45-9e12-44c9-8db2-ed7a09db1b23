import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import i18n from './lang/index'
// import ElementUI from 'element-ui'
// 替换为按需引入
import {
  Button,
  Select,
  Table,
  TableColumn,
  Form,
  FormItem,
  Input,
  InputNumber,
  Dialog,
  Row,
  Col,
  Option,
  Loading,
  Message,
  Menu,
  Submenu,
  MenuItem,
  MenuItemGroup,
  Breadcrumb,
  BreadcrumbItem,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Collapse,
  CollapseItem,
  Tooltip,
  Tree,
  DatePicker,
  Checkbox,
  CheckboxGroup,
  Popover,
  Pagination,
  Cascader,
  TimePicker,
  Switch,
  Tag,
  Upload,
} from 'element-ui'
import moment from 'moment'
import './theme/element-variables.scss'
import './assets/font_4504512_fs9dh8s388v/iconfont.css' // 引入自定义el-icon图标
// 删除原有的引入方式
// import tvtcloudbaiscwidget from 'tvtcloudbaiscwidget'
// 按需加载
import TvtDirectives from 'tvtcloudbaiscwidget/lib/directives' // 组件库的自定义指令
import TvtTable from 'tvtcloudbaiscwidget/lib/tvt-table'
import TvtTablePro from 'tvtcloudbaiscwidget/lib/tvt-table-pro'
import TvtSelect from 'tvtcloudbaiscwidget/lib/tvt-select'
import TvtSelectTree from 'tvtcloudbaiscwidget/lib/tvt-select-tree'
import TvtSearchTree from 'tvtcloudbaiscwidget/lib/tvt-search-tree'
import TvtInput from 'tvtcloudbaiscwidget/lib/tvt-input'
import TvtDialog from 'tvtcloudbaiscwidget/lib/tvt-dialog'
import TvtDatePicker from 'tvtcloudbaiscwidget/lib/tvt-date-picker'
import SearchInput from 'tvtcloudbaiscwidget/lib/search-input'
import TvtLoading from 'tvtcloudbaiscwidget/lib/tvt-loading'
import 'tvtcloudbaiscwidget/lib/theme-chalk/index.css'
import { initTvtLoading } from '@/utils/common'
// 删除原有的引入方式
// import * as echarts from 'echarts'
// 改为按需引入
import * as echarts from 'echarts/core'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent,
  LegendComponent,
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import '@/directives'
import { getTranslateValue } from './utils/common'
import errorCode from '@/api/errorCode.js'
// 全局方法
import { checkAuthority, formatTimeStamp } from './vue-plugin/plugin'

import VueDOMPurifyHTML from 'vue-dompurify-html'
Vue.use(VueDOMPurifyHTML)

import selfLocaLStorage from '@/utils/selfLocalStorage'

// 将storage挂载到Vue原型上，方便在组件中使用
Vue.prototype.$selfLocaLStorage = selfLocaLStorage

// Vue.use(tvtcloudbaiscwidget)
// TVT组件中国际化使用了自定义的tvtTranslate方法
Vue.mixin({
  methods: {
    tvtTranslate(...args) {
      return Vue.prototype.$translate(args)
    },
  },
})
Vue.use(TvtDirectives)
Vue.use(TvtTable)
Vue.use(TvtTablePro)
Vue.use(TvtSelect)
Vue.use(TvtSelectTree)
Vue.use(TvtSearchTree)
Vue.use(TvtInput)
Vue.use(TvtDialog)
Vue.use(TvtDatePicker)
Vue.use(SearchInput)
Vue.use(TvtLoading)
// 初始化loading
initTvtLoading(Vue, i18n, TvtLoading)

window.moment = moment

// 注册必需的组件
echarts.use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent,
  LegendComponent,
])
Vue.prototype.$echarts = echarts

Vue.config.productionTip = false
//翻译
Vue.prototype.$translate = getTranslateValue

Vue.prototype.$tablePageSizes = [10, 20, 50] // 100

Vue.prototype.$EventBus = new Vue()

// 注册组件
Vue.use(Button)
Vue.use(Select)
Vue.use(Table)
Vue.use(TableColumn)
Vue.use(Form)
Vue.use(FormItem)
Vue.use(Input)
Vue.use(InputNumber)
Vue.use(Dialog)
Vue.use(Row)
Vue.use(Col)
Vue.use(Option)
Vue.use(Loading)
Vue.use(Menu)
Vue.use(Submenu)
Vue.use(MenuItem)
Vue.use(MenuItemGroup)
Vue.use(Breadcrumb)
Vue.use(BreadcrumbItem)
Vue.use(Dropdown)
Vue.use(DropdownMenu)
Vue.use(DropdownItem)
Vue.use(Collapse)
Vue.use(CollapseItem)
Vue.use(Tooltip)
Vue.use(Tree)
Vue.use(DatePicker)
Vue.use(Checkbox)
Vue.use(CheckboxGroup)
Vue.use(Popover)
Vue.use(Pagination)
Vue.use(Cascader)
Vue.use(TimePicker) // 添加 TimePicker 组件注册
Vue.use(Switch)
Vue.use(Tag)
Vue.use(Upload)
// 挂载原型方法
Vue.prototype.$message = Message

// Vue.use(ElementUI, {
//   i18n: (key, value) => i18n.t(key, value),
// })
// 设置 i18n
Vue.prototype.$ELEMENT = {
  i18n: (key, value) => i18n.t(key, value),
}

Vue.use(checkAuthority)
Vue.use(formatTimeStamp)

// 全局路由拦截
router.beforeEach((to, from, next) => {
  // 不用校验token就可以访问的路由白名单: 登录
  const whiteNames = ['/login']
  if (
    !selfLocaLStorage.getItem('opsToken') &&
    whiteNames.indexOf(to.path) === -1
  ) {
    next({ path: '/login' })
  } else {
    next()
  }
})

window.errorCode = errorCode

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  components: { App },
  template: '<App/>',
})
