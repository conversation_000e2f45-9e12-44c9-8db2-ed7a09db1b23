<template>
  <!-- 告警信息表格组件 -->
  <div class="monitor-wrapper">
    <div class="monitor-table-title">
      <popover-search
        v-if="monitorFieldList && monitorFieldList.length > 1"
        :placement="'right'"
        :default-props="{ value: 'prop', label: 'label' }"
        :options="monitorFieldList"
        :cluster-options="filterClusterOptions"
        :init-select="initSelect"
        @handleSelect="handleSelect"
      >
        <template #referenceType>
          <el-button type="primary" size="small" round>
            {{ $t('search') }}
          </el-button>
        </template>
      </popover-search>
      <popover-checked
        v-if="checkOptions && checkOptions.length > 1"
        :placement="'right'"
        :default-props="{ value: 'prop', label: 'label' }"
        :options="checkOptions"
        :init-check="initCheck"
        @handleCheck="handleCheck"
      >
        <template #referenceType>
          <el-button type="primary" size="small" round>
            {{ $t('columnSetting') }}
          </el-button>
        </template>
      </popover-checked>
    </div>
    <div :id="id" class="monitor-table-wrapper">
      <tvt-table
        ref="myTable"
        v-myLoading="loading"
        :data="monitorTableDatas"
        :columns="filterColumns"
        :border="true"
        :border-bottom="true"
        :pagination="{
          total: total || 0,
          current: pagination.pageNum,
          size: pagination.pageSize,
          'page-sizes': [10, 20, 50],
          background: true,
        }"
        width="100%"
        :auto-height="false"
        @sort-change="handleSort"
        @onFetchData="getMonitorTableDatas"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.prop === 'st'">
            <span>{{ stampToStrLongMethod(parseInt(row.st) * 1000) }}</span>
          </template>
          <!-- mysql和mongo表格中的慢查询需要点击弹窗显示详情 -->
          <template
            v-else-if="column.prop === 'dbSlows' || column.prop === 'slows'"
          >
            <span
              :class="[
                getClassActualColor(row, column),
                parseInt(row[column.prop]) ? 'quota-link' : '',
              ]"
              @click="parseInt(row[column.prop]) ? handleSlow(row) : null"
              >{{ row[column.prop] }}</span
            >
          </template>
          <!-- C++服务中特定工程名称的服务可使用和重启次数需要点击弹窗显示详情 -->
          <template v-else-if="isCplusSpecialProjectClickable(row, column)">
            <span
              :class="[getClassActualColor(row, column), 'quota-link']"
              @click="handleErrorDetail({ ...row, prop: column.prop })"
              >{{ row[column.prop] }}</span
            >
          </template>
          <!-- 微服务中内存溢出文件数、websocket表格中的断开次数、mysql表格中的数据库死锁次数、
            邮箱中邮件发送失败量、邮件发送超出频率次数、
            每分钟短信发送失败存在异常次数、每分钟短信发送超出频率次数
            每分钟账号注册异常数、每分钟账号登录异常数、
            在线推送失败次数、离线推送失败次数、
            离线推送超出频率次数、推送失败总次数、
            RDC同步异常数、下发异常数、P2P获取异常数、
            服务获取异常数、dpikey异常日志数、
            ios推送失败数、谷歌推送失败数、华为推送失败数、line推送失败数
            需要点击弹窗显示详情
          -->
          <template v-else-if="showErrorInfoProps.includes(column.prop)">
            <span
              :class="[
                getClassActualColor(row, column),
                parseInt(row[column.prop]) ? 'quota-link' : '',
              ]"
              @click="
                parseInt(row[column.prop])
                  ? handleErrorDetail({ ...row, prop: column.prop })
                  : null
              "
              >{{ row[column.prop] }}</span
            >
          </template>
          <!-- 集群名称需要转换成对应的国际化 -->
          <template v-else-if="column.prop === 'clusterName'">
            <span>{{ i18nClusterName(row.clusterCode) }}</span>
          </template>
          <template v-else>
            <span :class="getClassActualColor(row, column)">{{
              row[column.prop]
            }}</span>
          </template>
        </template>
      </tvt-table>
    </div>
    <!-- MYSQL慢查询、webSocket断开次数详情弹窗 -->
    <tvt-dialog
      :title="$t('detail')"
      :show.sync="showDetail"
      width="1000px"
      :cancel-show="false"
      :submit-text="$t('close')"
      :modal-append-to-body="false"
      @close="closeDetail"
      @Submit="closeDetail"
    >
      <tvt-table
        :data="detailTableData"
        :columns="detailType === 0 ? detailColumns : dynamicDetailColumns"
        :border="true"
        :border-bottom="true"
        :height="600"
        :pagination="false"
        style="width: 100%"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="column.prop === 'st'">
            <span>{{ stampToStrLongMethod(parseInt(row.st) * 1000) }}</span>
          </template>
          <template v-if="['businessMsg', 'errorMsg'].includes(column.prop)">
            <el-tooltip
              class="item"
              effect="dark"
              :content="row[column.prop]"
              placement="left-start"
              :disabled="!isShowTooltip"
            >
              <span
                class="tooltip-ellipsis-box text-over-ellipsis2"
                @mouseenter="visibilityChange($event)"
                >{{ row[column.prop] }}</span
              >
            </el-tooltip>
          </template>
          <template v-if="column.prop === 'errorTime'">
            <span>{{ stampToStrLongMethod(row.errorTime) }}</span>
          </template>
        </template>
      </tvt-table>
    </tvt-dialog>
  </div>
</template>

<script>
import { stampToStrLong } from '@/utils/common'
import PopoverChecked from '@/components/common/PopoverChecked.vue'
import PopoverSearch from '@/components/common/PopoverSearch.vue'
import { getMonitorConfList } from '@/api/systemAlarm.js'
import {
  getMonitorTableApi,
  getSlowQueryDataApi,
  getDisconnectsDataApi,
  getFieldTypeApi,
} from '@/api/home.js'
import { columnsObj, monitorMetricsObj } from '@/views/system-monitor/config.js'
import { getLocale } from '@/lang'
import { mapState } from 'vuex'
import selfLocaLStorage from '@/utils/selfLocalStorage'

export default {
  name: 'CommonLayout',
  components: {
    PopoverChecked,
    PopoverSearch,
  },
  props: {
    id: {
      type: String,
      default: 'monitor-table',
    },
    searchCond: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      resizeObserver: null,
      title: this.$t('resourceTab.monitorTableTitle'),
      filterColumns: [],
      initCheck: [],
      initSelect: [
        {
          activityType: '',
          compareType: '',
          inputVal: '',
        },
      ],
      monitorConfObj: {}, // 指标配置项信息，存储每个指标的普通阈值、告警阈值、比较方式。用来确定表格中指标项显示颜色
      monitorFieldList: [], // 表格字段信息，存储表格中指标名称、指标类型、指标选项
      clusterOptions: [], // 当前选择DC的集群下拉选项，用作表格搜索
      loading: false,
      columns: [],
      monitorTableDatas: [], // 表格数据
      filterCond: {
        searchType: null,
        treeQuota: null,
        area: null,
        timeRange: [],
      },
      total: 0,
      pagination: {
        pageNum: 1,
        pageSize: 20,
      },
      order: null, // 排序,
      setSortField: false, // 是否手动重置排序字段,
      detailType: 0, // 详情类型 0-MYSQL慢查询详情  1-webSocket断开次数详情
      showDetail: false, // 显示慢查询详情
      detailTableData: [], // 慢查询详情表格数据
      currentErrorType: null, // 当前错误类型，用于判断是否为超限错误
      detailColumns: [
        { label: this.$t('index'), type: 'index', width: 65 },
        {
          label: this.$t('resourceTab.sqlId'),
          prop: 'sqlId',
          'min-width': 140,
        },
        {
          label: this.$t('resourceTab.cost'),
          prop: 'cost',
          width: 140,
        },
        {
          label: this.$t('resourceTab.st'),
          prop: 'st',
          slotName: 'bodyCell',
          width: 160,
        },
      ],
      detailColumns2: [
        { label: this.$t('index'), type: 'index', width: 65 },
        {
          label: this.$t('resourceTab.errorType'),
          prop: 'errorType',
          width: 140,
        },
        {
          label: this.$t('resourceTab.errorCode'),
          prop: 'errorCode',
          width: 120,
        },
        {
          label: this.$t('resourceTab.errorMsg'),
          prop: 'errorMsg',
          slotName: 'bodyCell',
          'min-width': 150,
        },
        {
          label: this.$t('ossTab.businessType'),
          prop: 'businessType',
          width: 130,
        },
        {
          label: this.$t('resourceTab.businessMsg'),
          prop: 'businessMsg',
          slotName: 'bodyCell',
          'min-width': 200,
        },
        {
          label: this.$t('resourceTab.errorTime'),
          prop: 'errorTime',
          slotName: 'bodyCell',
          width: 160,
        },
      ],
      showErrorInfoProps: [
        'dmpfiles',
        'disconnects',
        'dbDeadlocks',
        'errors',
        'limits',
        'registerErrors',
        'loginErrors',
        'onlineErrors',
        'offlineErrors',
        'offlineLimits',
        'syncErrors',
        'sendErrors',
        'cplusGetErrors',
        'cplusGetErrors',
        'javaGetErrors',
        'apnsErrors',
        'fcmErrors',
        'hmsErrors',
        'lineErrors',
      ],
      isShowTooltip: false, // 是否显示提示文字
    }
  },
  computed: {
    ...mapState('monitor', ['dcInfoList', 'clusterCodeNameObj']),
    // 动态生成详情表格列配置
    dynamicDetailColumns() {
      // 基于完整的 detailColumns2 配置进行过滤
      return this.detailColumns2.filter((column) => {
        const searchType = this.searchCond?.searchType

        // 定义需要过滤掉errorType列的特殊类型
        const specialTypes = [
          'monitor.email',
          'monitor.sms',
          'monitor.push',
          'monitor.account',
          'monitor.websocket',
          'monitor.dpikey',
        ]

        // 邮箱、短信超限错误的特殊处理：隐藏错误码和错误信息列
        const isEmailOrSmsOverLimit =
          ['monitor.email', 'monitor.sms'].includes(searchType) &&
          this.currentErrorType === 'limits'

        // 基础列显示逻辑
        if (['index', 'errorTime'].includes(column.prop || column.type)) {
          return true
        }

        // errorMsg列的特殊处理：邮箱、短信超限错误时隐藏
        if (column.prop === 'errorMsg') {
          return !isEmailOrSmsOverLimit
        }

        // 对于特殊类型，过滤掉errorType列
        if (specialTypes.includes(searchType) && column.prop === 'errorType') {
          return false
        }

        // errorType列对于非特殊类型显示
        if (column.prop === 'errorType') {
          return true
        }

        // 邮箱、短信、推送：额外显示错误码、业务类型、业务信息
        if (
          ['monitor.email', 'monitor.sms', 'monitor.push'].includes(searchType)
        ) {
          const allowedColumns = ['errorCode', 'businessType', 'businessMsg']

          // 邮箱、短信超限错误时，不显示错误码
          if (isEmailOrSmsOverLimit && column.prop === 'errorCode') {
            return false
          }

          return allowedColumns.includes(column.prop)
        }

        // 账号、websocket：额外显示错误码、业务信息
        if (['monitor.account', 'monitor.websocket'].includes(searchType)) {
          return ['errorCode', 'businessMsg'].includes(column.prop)
        }

        // dpikey：额外显示错误码
        if (searchType === 'monitor.dpikey') {
          return column.prop === 'errorCode'
        }

        // 其他类型：不显示额外列
        return false
      })
    },
    checkOptions() {
      return this.columns.filter((item) => !item.unCheckAble)
    },
    // 根据dcInfoList和当前DC过滤集群选项
    filterClusterOptions() {
      const { dcId } = this.searchCond
      // console.log('dcId', dcId, 'dcInfoList', this.dcInfoList)
      if (this.dcInfoList && this.dcInfoList.length && dcId) {
        const node =
          this.dcInfoList.filter((item) => item.dcId === String(dcId))[0] ||
          null
        if (node) {
          return (node.areas || []).map((item2) => ({
            label: item2.name,
            value: item2.name,
          }))
        } else {
          return []
        }
      } else {
        return []
      }
    },
    // 根据dc-info中的国际化集群名称替换掉表格中的中文集群名称
    i18nClusterName() {
      return function (val) {
        return this.clusterCodeNameObj[val] || val
      }
    },
  },
  watch: {
    // 表格数据需要与图的数据异步请求处理，所以在监听searchCond中不请求数据而是父组件直接调用refreshRequest函数请求数据
    searchCond: {
      handler(val) {
        // 清除表格排序
        if (this.$refs.myTable && this.$refs.myTable.$refs.tvtTableElTableRef) {
          this.$refs.myTable.$refs.tvtTableElTableRef.clearSort()
        }
        // 查询条件变化重置搜索和排序
        if (val && val.searchType !== this.filterCond.searchType) {
          // 查询条件变化则看表格列中有没有需要初始排序的
          this.initSort(val)
          // 说明指标发生变化，需要重置表格的搜索
          const initSelect = [
            {
              activityType: '',
              compareType: '',
              inputVal: '',
            },
          ]
          this.initSelect = initSelect
          // 指标变化则重新请求表格指标类型
          this.getFieldType(val.searchType)
        }
        this.pagination = {
          pageNum: 1,
          pageSize: 20,
        }
      },
      deep: true,
      immediate: true,
    },
    columns: {
      handler(val) {
        // this.filterColumns = val.slice()
        // this.initCheck = (val || []).filter((item) => !item.unCheckAble).map((item) => item.prop)
        // 当columns发生变化时,需要判断localStorage中有没有存过之前的列设置，如果存过则直接按照列设置显示字段
        // 把当前勾选的列保存到localStprage中  {searchType: columns}
        if (this.searchCond && this.searchCond.searchType) {
          // 需要根据searchType找到之前存储的列设置
          let monitorColumnsObj = null
          try {
            monitorColumnsObj =
              selfLocaLStorage.getItem('monitorColumnsObj') || {}
          } catch (err) {
            console.log('err', err)
            monitorColumnsObj = {}
          }
          const showColumns =
            monitorColumnsObj[this.searchCond.searchType] || null
          if (showColumns) {
            const checkedSet = new Set(showColumns)
            const preColumns = (val || []).filter((item) => item.unCheckAble)
            const quotaColumns = (val || []).filter(
              (item) => !item.unCheckAble && checkedSet.has(item.prop)
            )
            this.filterColumns = [...preColumns, ...quotaColumns]
            this.initCheck = showColumns
            this.tableResize()
            return
          }
        }
        this.filterColumns = val.slice().filter((item) => !item.initHide)
        this.initCheck = (val || [])
          .filter((item) => !item.unCheckAble)
          .filter((item) => !item.initHide)
          .map((item) => item.prop)
      },
      immediate: true,
    },
  },
  async created() {
    // 获取指标配置
    await this.getMonitorConf()
  },
  methods: {
    // 获取指标配置--取出所有指标的配置项
    async getMonitorConf() {
      try {
        const res = await getMonitorConfList({})
        if (res.data && res.data.length > 0) {
          // 遍历指标配置项，以ctType_ctIndexField为key，{threshold， alarmThreshold， compareType}为value
          let monitorConfObj = {}
          res.data.forEach((item) => {
            const {
              ctType,
              alarmIndexField,
              threshold,
              alarmThreshold,
              compareType,
              alarmIndexType,
            } = item
            if (ctType && alarmIndexField) {
              monitorConfObj[`${ctType}_${alarmIndexField}`] = {
                threshold,
                alarmThreshold,
                compareType,
                alarmIndexType,
              }
            }
          })
          this.monitorConfObj = monitorConfObj
        } else {
          this.monitorConfObj = {}
        }
      } catch (err) {
        console.error(err)
      }
    },
    // 获取表格对应字段的类型，表格搜索时展示字段筛选类型用
    async getFieldType(searchType) {
      try {
        const ctType = searchType.split('-')[0]
        const params = {
          ctType,
          lang: getLocale(),
        }
        const res = await getFieldTypeApi(params)
        if (res.data && res.data.length > 0) {
          // 根据表格列将国际化等信息加入到字段列表中
          const { name } = this.$route
          const columns = columnsObj[name] || []
          const columnList = columns[searchType].slice()
          const fieldList = []
          columnList.forEach((item) => {
            const { prop } = item
            const fieldItem = res.data.find((item2) => item2.fieldName === prop)
            if (fieldItem) {
              fieldList.push({
                ...fieldItem,
                value: fieldItem.fieldName,
                label: item.label,
              })
            }
          })
          this.monitorFieldList = fieldList
        } else {
          this.monitorFieldList = []
        }
      } catch (err) {
        console.error(err)
      }
    },
    // 搜索条件变化刷新请求
    async refreshRequest() {
      await this.getSearchCond(this.initSelect, this.order, true)
    },
    getSearchCond(initSelect, order, needFreshColumn = false) {
      if (this.searchCond) {
        const {
          searchType,
          dcId,
          timeRange = [],
          area = null,
        } = this.searchCond
        const pageSize = this.pagination.pageSize
        const formData = {
          searchType,
          dcId,
          timeRange,
          area,
          pageNum: 1,
          pageSize,
        }
        this.filterCond = formData
        this.pagination = { pageNum: 1, pageSize }
        // P2P2.0中选择RDC、UDT时，不传dcId
        if (searchType === 'monitor.udt' && dcId === '1') {
          delete formData.dcId // 不传dcId
        }
        if (searchType) {
          // 有具体指标则查询
          this.getTableData(
            { ...formData, pageNum: 1, pageSize, initSelect, order },
            needFreshColumn
          )
        }
      }
    },
    // 通过查询参数生成表格接口所需的参数
    getTableReqParams(params) {
      const filterCond = params || {
        ...this.filterCond,
        ...this.pagination,
        initSelect: this.initSelect,
        order: this.order,
      }
      const {
        searchType,
        dcId,
        timeRange,
        pageNum,
        pageSize,
        initSelect,
        order,
      } = filterCond
      // console.log('filterCond', filterCond)
      const formData = { pageNum, pageSize }
      if (searchType) {
        const arr = searchType.split('-')
        formData.monitorType = arr[0]
        formData.metrics = monitorMetricsObj[searchType]
      }
      if (dcId) {
        formData.dcId = dcId
      }
      if (initSelect) {
        formData.tableQuery = {}
        initSelect.forEach((item) => {
          if (item.activityType) {
            formData.tableQuery[item.activityType] = [
              {
                val: item.inputVal,
                op: item.compareType || undefined,
              },
            ]
          }
        })
      }
      if (order) {
        formData.order = [`${order.prop}_${order.order}`]
      }
      if (timeRange && timeRange.length) {
        formData.startTime = timeRange[0]
        formData.endTime = timeRange[1]
        formData.startTimeTmp = window
          .moment(timeRange[0])
          .format('YYYY-MM-DD HH:mm:ss')
        formData.endTimeTmp = window
          .moment(timeRange[1])
          .format('YYYY-MM-DD HH:mm:ss')
      }
      return formData
    },
    // 处理表格列--根据选择的采集项动态展示
    dealTableColumn() {
      const { name } = this.$route
      const columns = columnsObj[name] || []
      this.columns = columns[this.filterCond.searchType]
        ? columns[this.filterCond.searchType].slice()
        : []
    },
    // 根据表格列配置初始化排序
    initSort(val) {
      const { name } = this.$route
      const columns = columnsObj[name] || []
      const tableColumns = columns[val.searchType].slice()
      // 找到第一个待初始排序的列
      const sortColumn = tableColumns.find(
        (item) => item.sortable && item.order
      )
      if (sortColumn) {
        const { order, prop } = sortColumn
        this.order = { prop, order }
      } else {
        this.order = null
      }
    },
    // 初始化表格列的排序效果
    initTableSort() {
      if (this.order) {
        const { order, prop } = this.order
        // 给对应列加上排序效果
        this.setSortField = true
        // 根据order将对应的列给激活排序
        if (this.$refs.myTable && this.$refs.myTable.$refs.tvtTableElTableRef) {
          this.$nextTick(() => {
            this.$refs.myTable.$refs.tvtTableElTableRef.sort(
              prop,
              order === 'asc' ? 'ascending' : 'descending'
            )
          })
        }
      } else {
        // 清除表格排序
        if (this.$refs.myTable && this.$refs.myTable.$refs.tvtTableElTableRef) {
          this.$refs.myTable.$refs.tvtTableElTableRef.clearSort()
        }
      }
    },
    // 获取表格数据
    getTableData(formData = null, needFreshColumn = false) {
      this.loading = true
      if (!formData) {
        const initSelect = [...this.initSelect]
        const order = this.order ? { ...this.order } : null
        formData = { ...this.filterCond, ...this.pagination, initSelect, order }
      }
      const params = this.getTableReqParams(formData)
      // 判断域名和证书，这两个指标的表格排序需要固定加上ct_desc
      if (['monitor.domain', 'monitor.cert'].includes(params.monitorType)) {
        params.order = ['st_desc', ...(params.order || [])]
      }
      getMonitorTableApi(params)
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            if (needFreshColumn) {
              this.$nextTick(() => {
                this.dealTableColumn()
                this.initTableSort()
              })
            }
            if (res.data) {
              this.monitorTableDatas = res.data.records
              this.total = parseInt(res.data.total)
            } else {
              this.monitorTableDatas = []
              this.total = 0
            }
          }
          this.loading = false
        })
        .catch((err) => {
          this.$nextTick(() => {
            this.dealTableColumn()
            this.initTableSort()
          })
          this.loading = false
          this.monitorTableDatas = []
          this.total = 0
          console.log(err)
        })
    },
    // 分页获取指标数据表列表
    getMonitorTableDatas(pageSize) {
      const curCurrent = pageSize.current
      const curPageSize = pageSize.size
      const initSelect = this.initSelect
      const order = this.order
      this.pagination = {
        pageNum: curCurrent,
        pageSize: curPageSize,
      }
      // this.$emit('handlePage', {pageNum: curCurrent, pageSize: curPageSize})
      this.getTableData({
        ...this.filterCond,
        pageNum: curCurrent,
        pageSize: curPageSize,
        initSelect,
        order,
      })
    },
    tableResize() {
      if (this.$refs.myTable && this.$refs.myTable.$refs.tvtTableElTableRef) {
        this.$nextTick(() => {
          this.$refs.myTable.$refs.tvtTableElTableRef.doLayout()
        })
      }
    },
    handleCheck(val) {
      this.initCheck = val
      const preColumns = this.columns.filter((item) => item.unCheckAble)
      const checkedSet = new Set(val)
      const quotaColumns = this.columns.filter(
        (item) => !item.unCheckAble && checkedSet.has(item.prop)
      )
      this.filterColumns = [...preColumns, ...quotaColumns]
      this.tableResize()
      // 把当前勾选的列保存到localStprage中  {searchType: columns}
      let monitorColumnsObj = null
      monitorColumnsObj = selfLocaLStorage.getItem('monitorColumnsObj') || {}
      const { searchType } = this.searchCond
      monitorColumnsObj[searchType] = val
      selfLocaLStorage.setItem('monitorColumnsObj', monitorColumnsObj)
      this.$nextTick(() => {
        // 表格列勾选变化时判断是否有排序，有则加上,没有则清除
        if (!this.order) {
          if (
            this.$refs.myTable &&
            this.$refs.myTable.$refs.tvtTableElTableRef
          ) {
            this.$refs.myTable.$refs.tvtTableElTableRef.clearSort()
          }
        } else {
          const { prop, order } = this.order
          if (this.filterColumns.find((item) => item.prop === prop)) {
            // 排序的列展示出来了，则需要加上排序效果
            this.setSortField = true
            // 根据order将对应的列给激活排序
            if (
              this.$refs.myTable &&
              this.$refs.myTable.$refs.tvtTableElTableRef
            ) {
              this.$refs.myTable.$refs.tvtTableElTableRef.sort(
                prop,
                order === 'asc' ? 'ascending' : 'descending'
              )
            }
          }
        }
      })
    },
    handleSelect(val) {
      this.initSelect = val
      this.getSearchCond(val, this.order, true)
    },
    dealtableQuery() {
      const tableQuery = {}
      if (this.initSelect) {
        this.initSelect.forEach((item) => {
          if (item.activityType) {
            tableQuery[item.activityType] = [
              {
                val: item.inputVal,
                op: item.compareType || undefined,
              },
            ]
          }
        })
      }
      return tableQuery
    },
    stampToStrLongMethod(time) {
      let intTime = parseInt(time) //接口给的字符串时间
      return stampToStrLong(intTime)
    },
    // 动态比较函数
    dynamicCompare(a, b, operator) {
      // 使用Function构造函数来动态执行比较
      return Function('a', 'b', `return a ${operator} b`)(a, b)
    },
    // 获取指标类名
    getClassActualColor(row, column) {
      // 使用动态比较
      const { monitorType } = row
      const { prop } = column
      const value = row[prop]
      const key = `${monitorType}_${prop}`
      const {
        alarmIndexType,
        threshold = null,
        alarmThreshold = null,
        compareType = null,
      } = this.monitorConfObj[key] || {}
      // 比较告警阈值
      if (alarmThreshold && compareType && value != undefined) {
        // 针对success/fail的判断需要通过字符串比较
        if (
          ['success,fail', 'success,fail,none'].includes(alarmIndexType) &&
          compareType === '='
        ) {
          const flag = value.toLowerCase() === alarmThreshold.toLowerCase()
          if (flag) {
            return 'alarmThreshold-cell'
          }
        } else {
          // 常规的数值判断
          // 超过普通阈值，显示黄色#e6a23c   大于告警阈值显示红色  #f56c6c
          const flag2 = this.dynamicCompare(
            parseInt(value),
            parseInt(alarmThreshold),
            compareType
          )
          if (flag2) {
            return 'alarmThreshold-cell'
          }
        }
      }
      // 比较普通阈值
      if (threshold && compareType && value != undefined) {
        // 针对success/fail的判断需要通过字符串比较
        if (
          ['success,fail', 'success,fail,none'].includes(alarmIndexType) &&
          compareType === '='
        ) {
          const flag3 = value.toLowerCase() === threshold.toLowerCase()
          if (flag3) {
            return 'threshold-cell'
          }
        } else {
          // 超过普通阈值，显示黄色#e6a23c   大于告警阈值显示红色  #f56c6c
          const flag4 = this.dynamicCompare(
            parseInt(value),
            parseInt(threshold),
            compareType
          )
          if (flag4) {
            return 'threshold-cell'
          }
        }
      }
      return ''
    },
    handleSort(params) {
      if (this.setSortField) {
        // 是由字段过滤那里手动设置排序字段触发的事件，跳过
        this.setSortField = false
        return
      }
      const { prop = null, order = null } = params || {}
      const orderObj = { ascending: 'asc', descending: 'desc' }
      const initSelect = [...this.initSelect]
      if (prop && order) {
        const orderTemp = { prop, order: orderObj[order] }
        this.order = orderTemp
        this.getSearchCond(initSelect, orderTemp)
      } else {
        // 说明是去掉排序
        this.order = null
        this.getSearchCond(initSelect, null)
      }
    },
    // 点击表格中的慢查询
    handleSlow(row) {
      this.detailType = 0
      this.showDetail = true
      this.getSlowQueryDataApi(row.id)
    },
    // 关闭详情弹窗
    closeDetail() {
      this.detailType = 0
      this.showDetail = false
      this.detailTableData = []
      this.currentErrorType = null
    },
    // 请求慢查询日志数据
    getSlowQueryDataApi(data) {
      getSlowQueryDataApi(data)
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            if (res.data) {
              this.detailTableData = res.data
            } else {
              this.detailTableData = []
            }
          }
        })
        .catch((err) => {
          this.detailTableData = []
          console.log(err)
        })
    },
    // 点击表格中的断开次数/数据库死锁次数等异常信息
    handleErrorDetail(row) {
      this.detailType = 1
      this.showDetail = true
      // 存储当前错误类型，用于动态列配置
      this.currentErrorType = row.prop
      this.getErrorDetailData(row)
    },
    // 请求websocket断开次数/数据库死锁次数日志数据
    getErrorDetailData(data) {
      const { id, monitorType, prop } = data
      getDisconnectsDataApi({
        mainId: id,
        monitorType: monitorType,
        errorType: ['dmpfiles', 'dbDeadlocks'].includes(prop) ? null : prop, // mysql数据库死锁次数、微服务内存溢出文件数不需要传errorType
      })
        .then((res) => {
          if (res.basic && res.basic.code == 200) {
            if (res.data) {
              this.detailTableData = res.data
            } else {
              this.detailTableData = []
            }
          }
        })
        .catch((err) => {
          this.detailTableData = []
          console.log(err)
        })
    },
    // 是否提示toolTip
    visibilityChange(event) {
      const cellDom = event.target
      // range 表示文档的一个区域
      const range = document.createRange()
      range.setStart(cellDom, 0)
      range.setEnd(cellDom, cellDom.childNodes.length)
      const flag = this.getStyle(cellDom, '-webkit-line-clamp')
      if (flag == 'none') {
        // rangeWidth 表示元素内容的宽度
        const rangeWidth = range.getBoundingClientRect().width
        let padding =
          (parseInt(this.getStyle(cellDom, 'paddingLeft')) || 0) +
          (parseInt(this.getStyle(cellDom, 'paddingRight')) || 0)

        // cellDom.offsetWidth 表示选定区域的宽度
        if (rangeWidth > cellDom.offsetWidth - padding) {
          // 显示tooltip
          this.isShowTooltip = true
        } else {
          this.isShowTooltip = false
        }
      } else {
        // rangeHeight 表示元素内容的高度
        const rangeHeight = range.getBoundingClientRect().height
        let padding =
          (parseInt(this.getStyle(cellDom, 'paddingTop')) || 0) +
          (parseInt(this.getStyle(cellDom, 'paddingBottom')) || 0)

        // cellDom.offsetHeight 表示选定区域的高度
        if (rangeHeight > cellDom.offsetHeight - padding) {
          // 显示tooltip
          this.isShowTooltip = true
        } else {
          this.isShowTooltip = false
        }
      }
    },
    // 获取dom的样式
    getStyle(dom, attr) {
      return getComputedStyle(dom, null)[attr]
    },
    // 判断是否为C++服务中特定的工程名称
    isCplusSpecialProject(row) {
      // 检查是否为C++微服务并且工程名称为指定的两个名称之一
      return (
        this.searchCond?.searchType === 'monitor.microservice.cplus' &&
        row.projectName &&
        ['Push1Server', 'Push1SupercctvSdnsServer'].includes(row.projectName)
      )
    },
    // 判断C++特定项目的字段是否可点击
    isCplusSpecialProjectClickable(row, column) {
      if (!this.isCplusSpecialProject(row)) {
        return false
      }

      if (column.prop === 'restarts') {
        // 重启次数：值大于0才可点击
        return parseInt(row[column.prop]) > 0
      } else if (column.prop === 'available') {
        // 服务可使用：值为FAIL才可点击
        return row[column.prop] === 'FAIL'
      }

      return false
    },
  },
}
</script>

<style lang="scss" scoped>
.monitor-wrapper {
  width: calc(100%);
  height: calc(100%);
  .monitor-table-title {
    width: 100%;
    height: 50px;
    line-height: 30px;
    font-size: 24px;
    margin: 0px;
    padding-top: 5px;
    min-width: 100px;
    overflow-x: hidden;
    span {
      margin-right: 12px;
    }
  }
  .monitor-table-wrapper {
    width: calc(100%);
    height: calc(100% - 50px);
    ::v-deep .el-table--scrollable-x .el-table__body-wrapper {
      min-height: 300px;
    }
    ::v-deep .el-table__empty-block {
      min-height: 300px;
    }
    ::v-deep .el-table td.el-table__cell {
      padding: 8px 0px;
    }
  }
  ::v-deep .el-table__cell:has(.cell .alarmThreshold-cell) {
    background-color: #f56c6c;
  }
  ::v-deep .el-table__cell:has(.cell .threshold-cell) {
    background-color: #e6a23c;
  }
  ::v-deep .quota-link {
    cursor: pointer;
    text-decoration: underline;
    color: #68b1fd;
  }

  ::v-deep .tooltip-ellipsis-box {
    display: block;
    width: 100%;
  }

  ::v-deep .text-over-ellipsis2 {
    overflow: hidden; // 超出的文本隐藏
    text-overflow: ellipsis; // 溢出用省略号显示
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}
</style>
