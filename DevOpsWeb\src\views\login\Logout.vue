<template>
  <!-- 退出登录 -->
  <div class="logout-box">
    <tvt-dialog
      :title="$t('info')"
      width="320px"
      :show.sync="show"
      :modal-append-to-body="false"
      :append-to-body="true"
      :cancel-text="$t('cancel')"
      :submit-text="$t('confirm')"
      @close="show = false"
      @Cancel="show = false"
      @Submit="userLogout"
    >
      <div class="logout-con">
        {{ $t('login.confirmLogout') }}
      </div>
    </tvt-dialog>
  </div>
</template>

<script>
import selfLocaLStorage from '@/utils/selfLocalStorage'
export default {
  name: 'LogoutPage',
  data() {
    return {
      show: false,
    }
  },
  methods: {
    open() {
      this.show = true
    },
    // 登出
    userLogout() {
      const localeType = sessionStorage.getItem('localeType')
      // 将envHomeQuota额外保存下载，其余的清除--特殊要求
      const treeQuota = selfLocaLStorage.getItem('envHomeQuota') || null
      selfLocaLStorage.clear()
      sessionStorage.clear()
      if (localeType) sessionStorage.setItem('localeType', localeType)
      // 将treeQuotq暂存下来
      if (treeQuota) {
        selfLocaLStorage.setItem('envHomeQuota', treeQuota)
      }
      this.show = false
      this.$router.push('/login')
    },
  },
}
</script>

<style lang="scss" scoped>
.logout-con {
  height: 80px;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
::v-deep .el-dialog .el-dialog__body {
  min-height: 100px;
  align-items: center;
}
</style>
