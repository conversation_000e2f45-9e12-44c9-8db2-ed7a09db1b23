<template>
  <div class="navbar">
    <breadcrumb class="breadcrumb-container" />
    <div class="right-menu">
      <div class="environment">
        <span>{{ environmentName }}</span>
        <span class="separator"> /</span>
        <span>{{ dcNameDesc || $t('all') }}</span>
      </div>
      <el-dropdown trigger="click" @command="handleCommand">
        <span class="el-dropdown-link">
          {{ $t('hello') }}{{ username
          }}<i class="el-icon-arrow-down el-icon--right" />
        </span>
        <el-dropdown-menu slot="dropdown">
          <!-- <el-dropdown-item icon="el-icon-edit-outline" command="pwd">
            {{ $t('modifyPass') }}
          </el-dropdown-item> -->
          <el-dropdown-item icon="el-icon-switch-button" command="logout">
            {{ $t('logout') }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <modify-pass ref="modifyPass" />
    <logout-page ref="logoutPage" />
  </div>
</template>

<script>
import Breadcrumb from '@/layout/components/Breadcrumb.vue'
import ModifyPass from '@/views/login/ModifyPass.vue'
import LogoutPage from '@/views/login/Logout.vue'
import { mapState } from 'vuex'
import selfLocaLStorage from '@/utils/selfLocalStorage'
export default {
  name: 'NavBar',
  components: {
    Breadcrumb,
    ModifyPass,
    LogoutPage,
  },
  data() {
    return {
      // 登录接口返回了userName字段，如果没有则使用登录页面的loginName字段
      username:
        selfLocaLStorage.getItem('userName') &&
        selfLocaLStorage.getItem('userName') !== 'null'
          ? selfLocaLStorage.getItem('userName')
          : selfLocaLStorage.getItem('loginName') || '',
    }
  },
  computed: {
    ...mapState('params', ['environmentName', 'dcNameDesc']),
  },
  methods: {
    handleCommand(command) {
      switch (command) {
        case 'pwd':
          this.openPwd()
          break
        case 'logout':
          this.openLogout()
          break
        default:
          return
      }
    },
    openPwd() {
      // 修改密码
      this.$refs.modifyPass.visable = true
    },
    openLogout() {
      // 退出登录
      this.$refs.logoutPage.open()
    },
  },
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  background-color: #36393f;
  .breadcrumb-container {
    float: left;
  }
  .right-menu {
    position: absolute;
    right: 20px;
    height: 100%;
    line-height: 50px;
    .environment {
      display: inline-block;
      margin-right: 30px;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
      font-weight: bold;
      .separator {
        margin: 0 9px;
        color: #c0c4cc;
      }
    }
    .el-dropdown {
      cursor: pointer;
      .el-dropdown-link {
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }
}
</style>
<style lang="scss">
.right-menu {
  .select-param {
    margin-right: 40px;
    .el-input {
      width: 265px;
    }
  }
}
.eloption .el-cascader-menu__wrap {
  min-height: 260px !important;
}
</style>
