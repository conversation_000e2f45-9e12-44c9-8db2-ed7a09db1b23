<template>
  <!-- 定时任务日志 -->
  <div class="page-wrapper">
    <div class="search-wrapper">
      <tvt-date-picker
        v-model="filterCond.dayTime"
        align="right"
        type="daterange"
        :placeholder="$t('microLogTab.dayTimeSelect')"
        :clearable="false"
        style="width: 250px; margin-right: 20px"
        :picker-options="pickerOptions"
        @change="handleDate"
      ></tvt-date-picker>
      <tvt-select
        v-model="filterCond.projectNames"
        :options="microTypeOptions"
        :mockplaceholder="$t('microLogTab.microSelect')"
        :clearable="false"
        style="width: 250px; margin-right: 20px"
        @change="handleProjectChange"
      />
      <tvt-select
        v-model="filterCond.businessIds"
        multiple
        collapse-tags
        :options="currentBusinessIdOptions"
        :mockplaceholder="$t('scheduleTaskLogTab.scheduleTaskSelect')"
        :clearable="true"
        style="width: 250px; margin-right: 20px"
        @change="handleBusinessIdChange"
      />
      <tvt-select
        v-model="filterCond.executeStatus"
        multiple
        collapse-tags
        :options="executeStatusOptions"
        :mockplaceholder="$t('scheduleTaskLogTab.excuteStatusSelect')"
        :clearable="false"
        style="width: 250px; margin-right: 20px"
        @change="getTaskLogList(1)"
      />
      <div class="search-btn-box">
        <el-button type="primary" size="small" round @click="handleReset">{{
          $t('reset')
        }}</el-button>
        <el-button type="primary" size="small" round @click="handleRefresh">{{
          $t('refresh')
        }}</el-button>
      </div>
      <div class="search-left-wrapper"></div>
    </div>
    <!-- 定时任务日志表格 -->
    <div class="table-wrapper timerTask-table-wrapper">
      <tvt-table
        ref="myTable"
        v-myLoading="loading"
        :data="scheduleTaskLogList"
        :columns="scheduleTaskLogColumn"
        :border="true"
        :border-bottom="true"
        :pagination="{
          total,
          current: filterCond.current,
          size: filterCond.size,
          'page-sizes': $tablePageSizes,
          background: true,
        }"
        @onFetchData="getTaskLogList"
      >
        <template #bodyCell="{ row, column }">
          <template v-if="['startTime', 'endTime'].includes(column.prop)">
            <span>{{ stampToStrLongMethod(row[column.prop]) }}</span>
          </template>
          <template v-if="column.prop === 'msg'">
            <el-tooltip
              class="item"
              effect="dark"
              :content="row[column.prop]"
              placement="left-start"
            >
              <span class="tooltip-ellipsis-box">{{ row[column.prop] }}</span>
            </el-tooltip>
          </template>
          <template v-if="column.prop === 'executeStatus'">
            <!-- <span
              :class="row.executeStatus === 'fail' ? 'alarmThreshold-cell' : ''"
              >{{ row['executeStatus'] }}</span> -->
            <span :class="[getClassActualColor(row, column)]">{{
              row['executeStatus']
            }}</span>
          </template>
        </template>
      </tvt-table>
    </div>
  </div>
</template>
<script>
import { debounce, stampToStrLong, commonShortcuts } from '@/utils/common'
import { getTimerClassList, getScheduleTask } from '@/api/basicTool.js'
import { getLocale } from '@/lang'
import { getInitDate } from '@/utils/dateFormat.js'

const lang = getLocale() || 'zh-CN'

export default {
  name: 'ScheduleTaskLog',
  components: {},
  data() {
    const pageVM = this
    return {
      total: 0,
      filterCond: {
        current: 1,
        size: 20,
        projectNames: '',
        dayTime: getInitDate(),
        businessIds: [],
        executeStatus: [],
      },
      businessIdMap: {}, // 新增：以projectName为key，businessId选项为value的映射
      currentBusinessIdOptions: [], // 当前显示的businessId选项
      executeStatusOptions: [
        { label: this.$t('scheduleTaskLogTab.success'), value: 'success' },
        { label: this.$t('scheduleTaskLogTab.fail'), value: 'fail' },
        { label: this.$t('scheduleTaskLogTab.running'), value: 'running' },
        { label: this.$t('scheduleTaskLogTab.timeout'), value: 'timeout' },
      ],
      loading: false,
      pickerOptions: {
        // 点击时，选择的是开始时间，也就是minDate
        onPick: (time) => {
          // 记录首次选择的日期（自然月计算基准）
          if (time.minDate && !time.maxDate) {
            pageVM.startDate = time.minDate
          }
          if (time.maxDate) {
            pageVM.startDate = null
          }
        },
        disabledDate(time) {
          if (pageVM.startDate) {
            const start = moment(pageVM.startDate)
            const end = moment(time)
            // 限制结束日期在[start, start+1月]范围内,且不能超过当前日期
            return (
              time.getTime() > Date.now() ||
              end < start.clone().add(-29, 'd') ||
              end > start.clone().add(29, 'd')
            )
          } else {
            return time.getTime() > Date.now()
          }
        },
        shortcuts: commonShortcuts,
      },
      arrowControl: false,
      microTypeOptions: [],
      scheduleTaskLogList: [],
      scheduleTaskLogColumn: [
        {
          label: this.$t('microServeTab.projectName'),
          prop: 'projectName',
          width: 240,
        },
        {
          label: this.$t('scheduleTaskLogTab.scheduleTask'),
          prop: 'description',
          width: 150,
        },
        {
          label: this.$t('scheduleTaskLogTab.executeClass'),
          prop: 'executeClass',
          minWidth: 200,
        },
        {
          label: this.$t('scheduleTaskLogTab.executeMethod'),
          prop: 'executeMethod',
          width: 150,
        },
        {
          label: this.$t('scheduleTaskLogTab.excutePeroid'),
          prop: 'excutePeroid',
          width: 120,
        },
        {
          label: this.$t('scheduleTaskLogTab.scheduleInstanceId'),
          prop: 'scheduledInstanceId',
          width: lang === 'zh-CN' ? 100 : 170,
        },
        {
          label: this.$t('scheduleTaskLogTab.excuteInstanceId'),
          prop: 'executeInstanceId',
          width: lang === 'zh-CN' ? 100 : 150,
        },
        {
          label: this.$t('restfullLogTab.startTime'),
          prop: 'startTime',
          slotName: 'bodyCell',
          width: 160,
        },
        {
          label: this.$t('restfullLogTab.endTime'),
          prop: 'endTime',
          slotName: 'bodyCell',
          width: 160,
        },
        {
          label: this.$t('scheduleTaskLogTab.excuteCost'),
          prop: 'cost',
          width: lang === 'zh-CN' ? 80 : 100,
        },
        {
          label: this.$t('scheduleTaskLogTab.executeStatus'),
          prop: 'executeStatus',
          slotName: 'bodyCell',
          width: lang === 'zh-CN' ? 80 : 120,
        },
        {
          label: this.$t('scheduleTaskLogTab.code'),
          prop: 'code',
          width: 80,
        },
        {
          label: this.$t('scheduleTaskLogTab.msg'),
          prop: 'msg',
          width: 130,
        },
      ],
      statusOptions: [
        { label: this.$t('restfullLogTab.reqSuccess'), value: 'true' },
        { label: this.$t('restfullLogTab.reqFail'), value: 'false' },
      ],
      activeName: 'success',
    }
  },
  async mounted() {
    await this.getTimerClassList()
    this.getTaskLogList()
  },
  methods: {
    stampToStrLongMethod(time) {
      let intTime = parseInt(time) //接口给的字符串时间
      return stampToStrLong(intTime)
    },
    // 获取服务名及执行类名列表
    async getTimerClassList() {
      try {
        const res = await getTimerClassList({})
        const data = res?.data || []
        // 处理项目名称选项，添加去重逻辑
        const uniqueProjectNames = new Set()
        this.businessIdMap = {} // 初始化映射对象
        data.forEach((item) => {
          const { projectName, businessId, description } = item
          uniqueProjectNames.add(projectName)
          // 构建businessIdMap
          if (businessId) {
            if (!this.businessIdMap[projectName]) {
              this.businessIdMap[projectName] = []
            }
            // 将businessIds添加到对应的projectName下
            // 检查是否已存在，避免重复
            const exists = this.businessIdMap[projectName].some(
              (option) => option.value === businessId
            )
            if (!exists) {
              this.businessIdMap[projectName].push({
                label: description,
                value: businessId,
              })
            }
          }
        })
        this.microTypeOptions = Array.from(uniqueProjectNames).map((name) => ({
          label: name,
          value: name,
        }))
        // 微服务列表默认选中第一个
        if (this.microTypeOptions.length > 0) {
          this.filterCond.projectNames = this.microTypeOptions[0].value
          // 设置当前项目对应的businessId选项
          this.updateBusinessIdOptions(this.filterCond.projectNames)
        }
      } catch (error) {
        console.error(error)
      }
    },
    handleDate(val) {
      this.filterCond.dayTime = val
      this.getTaskLogList()
    },
    handleBusinessIdChange(list) {
      this.filterCond.businessIds = list
      this.$nextTick(() => {
        this.getTaskLogList()
      })
    },
    getTaskLogList: debounce(async function (pageSize) {
      this.loading = true
      try {
        if (pageSize == 1) {
          this.filterCond.current = 1
        } else if (pageSize && pageSize.current) {
          this.filterCond.current = pageSize.current
          this.filterCond.size = pageSize.size
        }
        const {
          current,
          size,
          projectNames,
          dayTime,
          businessIds,
          executeStatus,
        } = this.filterCond
        const params = { pageNum: current, pageSize: size }
        if (projectNames) {
          params.projectNames = [projectNames]
        }
        if (businessIds) {
          params.businessIds = businessIds
        }
        if (dayTime && dayTime.length > 0) {
          const dateStart = window.moment(dayTime[0]).format('yyyy-MM-DD')
          const dateEnd = window.moment(dayTime[1]).format('yyyy-MM-DD')
          const startTime = '00:00:00'
          const endTime = '23:59:59'
          params.startTime = window
            .moment(`${dateStart} ${startTime}`)
            .valueOf()
          params.endTime = window.moment(`${dateEnd} ${endTime}`).valueOf()
        }
        if (executeStatus) {
          params.executeStatus = executeStatus
        }
        const { data = {} } = await getScheduleTask(params)
        const records = data?.records || []
        const total = data?.total || 0
        this.scheduleTaskLogList = records.slice().map((item) => {
          return {
            ...item,
            startTime: item.startTime ? item.startTime : '',
            endTime: item.endTime ? item.endTime : '',
            excutePeroid: `${item.zone ? item.zone + ' ' : ''}${item.cron}`,
          }
        })
        this.total = Number(total)
        this.loading = false
      } catch (error) {
        console.error(error)
        this.loading = false
      }
    }, 200),
    handleReset() {
      // 条件重置
      this.filterCond = {
        ...this.filterCond,
        current: 1,
        pageSize: 20,
        projectNames: this.microTypeOptions[0].value || '',
        dayTime: getInitDate(),
        businessIds: [],
        executeStatus: [],
      }
      this.$nextTick(() => {
        this.getTaskLogList(1)
      })
    },
    handleRefresh() {
      this.getTaskLogList(1)
    },
    // 处理项目名称变化
    handleProjectChange(value) {
      // 清空已选择的businessIds
      this.filterCond.businessIds = []
      // 更新businessId的选项
      this.updateBusinessIdOptions(value)
      // 获取任务日志列表
      this.getTaskLogList(1)
    },
    // 更新businessId选项
    updateBusinessIdOptions(projectName) {
      if (projectName && this.businessIdMap[projectName]) {
        this.currentBusinessIdOptions = this.businessIdMap[projectName]
      } else {
        this.currentBusinessIdOptions = []
      }
    },
    // 获取指标类名
    getClassActualColor(row, column) {
      return ['fail', 'timeout'].includes(row[column.prop])
        ? 'alarmThreshold-cell'
        : ''
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .tooltip-ellipsis-box {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
::v-deep .search-wrapper .device-mutli-input .el-input__inner {
  width: 250px;
}

::v-deep .search-wrapper .device-mutli-input.isFocus .label {
  border: 1px solid #dcdfe6;
  border-radius: 18px;
  width: 250px;
  box-sizing: border-box;
}

::v-deep .search-wrapper .device-mutli-input.isFocus .el-input__inner {
  border: 1px solid #429efd;
  box-sizing: border-box;
}
::v-deep .search-wrapper .el-select .el-tag__close.el-icon-close {
  top: 2px !important;
}

.timerTask-table-wrapper {
  width: calc(100%);
  height: calc(100%);
  ::v-deep .el-table--scrollable-x .el-table__body-wrapper {
    min-height: 300px;
  }
  ::v-deep .el-table__empty-block {
    min-height: 300px;
  }
  ::v-deep .el-table td.el-table__cell {
    padding: 8px 0px;
  }
}
::v-deep .el-table__cell:has(.cell .alarmThreshold-cell) {
  background-color: #f56c6c;
}
</style>
